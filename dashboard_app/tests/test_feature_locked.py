from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class FeatureLockedTestCase(TestCase):
    """Test cases for feature locked functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create a service provider user without a venue
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name='Test Provider',
            display_name='Test Provider'
        )

    def test_todays_bookings_locked(self):
        """Test that today's bookings shows locked page when no venue exists."""
        self.client.force_login(self.provider_user)
        
        response = self.client.get(reverse('dashboard_app:provider_todays_bookings'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/feature_locked.html')
        self.assertContains(response, "Today's Bookings")
        self.assertContains(response, 'Feature Locked')
        self.assertContains(response, 'Create Your Venue')

    def test_earnings_reports_locked(self):
        """Test that earnings reports shows locked page when no venue exists."""
        self.client.force_login(self.provider_user)
        
        response = self.client.get(reverse('dashboard_app:provider_earnings_reports'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/feature_locked.html')
        self.assertContains(response, 'Earnings Reports')
        self.assertContains(response, 'Feature Locked')
        self.assertContains(response, 'Create Your Venue')

    def test_service_performance_locked(self):
        """Test that service performance shows locked page when no venue exists."""
        self.client.force_login(self.provider_user)
        
        response = self.client.get(reverse('dashboard_app:provider_service_performance'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/feature_locked.html')
        self.assertContains(response, 'Service Performance')
        self.assertContains(response, 'Feature Locked')
        self.assertContains(response, 'Create Your Venue')

    def test_discounts_locked(self):
        """Test that discounts shows locked page when no venue exists."""
        self.client.force_login(self.provider_user)
        
        response = self.client.get(reverse('discount_app:provider_discount_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/feature_locked.html')
        self.assertContains(response, 'Discounts')
        self.assertContains(response, 'Feature Locked')
        self.assertContains(response, 'Create Your Venue')

    def test_discount_analytics_locked(self):
        """Test that discount analytics shows locked page when no venue exists."""
        self.client.force_login(self.provider_user)
        
        response = self.client.get(reverse('discount_app:provider_discount_analytics'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/feature_locked.html')
        self.assertContains(response, 'Discount Analytics')
        self.assertContains(response, 'Feature Locked')
        self.assertContains(response, 'Create Your Venue')

    def test_discount_dashboard_locked(self):
        """Test that discount dashboard shows locked page when no venue exists."""
        self.client.force_login(self.provider_user)
        
        response = self.client.get(reverse('discount_app:provider_discount_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/provider/feature_locked.html')
        self.assertContains(response, 'Discounts')
        self.assertContains(response, 'Feature Locked')
        self.assertContains(response, 'Create Your Venue') 