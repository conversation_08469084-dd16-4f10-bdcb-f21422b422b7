# Generated by Django 5.2.4 on 2025-07-05 17:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('venues_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FavoriteVenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_date', models.DateTimeField(auto_now_add=True, help_text='When the venue was added to favorites')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the favorite was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who favorited this venue', on_delete=django.db.models.deletion.CASCADE, related_name='favorite_venues', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue that was favorited', on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Favorite Venue',
                'verbose_name_plural': 'Favorite Venues',
                'ordering': ['-added_date'],
                'indexes': [models.Index(fields=['customer', '-added_date'], name='dashboard_a_custome_fa9ce1_idx'), models.Index(fields=['venue', '-added_date'], name='dashboard_a_venue_i_830d12_idx')],
                'unique_together': {('customer', 'venue')},
            },
        ),
        migrations.CreateModel(
            name='UserPreferences',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True, help_text='Receive email notifications for booking updates and promotions')),
                ('sms_reminders', models.BooleanField(default=True, help_text='Receive SMS reminders for upcoming appointments')),
                ('marketing_updates', models.BooleanField(default=False, help_text='Receive marketing emails about special offers and new venues')),
                ('dashboard_notifications', models.BooleanField(default=True, help_text='Show notifications in dashboard')),
                ('weekly_summary', models.BooleanField(default=True, help_text='Receive weekly summary emails')),
                ('profile_visibility', models.CharField(choices=[('public', 'Public'), ('private', 'Private'), ('friends', 'Friends Only')], default='public', help_text='Who can see your profile information', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When preferences were created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When preferences were last updated')),
                ('user', models.OneToOneField(help_text='User these preferences belong to', on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Preferences',
                'verbose_name_plural': 'User Preferences',
                'indexes': [models.Index(fields=['user'], name='dashboard_a_user_id_5e8b99_idx')],
            },
        ),
    ]
