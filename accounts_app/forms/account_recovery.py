# --- Django Imports ---
from django import forms
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings

# --- Third-Party Imports ---
from allauth.account.forms import ResetPasswordForm
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Submit, HTML, Div
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ..models import CustomUser

User = get_user_model()


class EnhancedPasswordResetForm(ResetPasswordForm):
    """
    Enhanced password reset form with additional security features.
    
    Features:
    - Enhanced validation
    - Security logging
    - Rate limiting integration
    - Custom styling
    """
    
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'autofocus': True,
            }
        ),
        help_text=_('Enter the email address associated with your account.')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'password-reset-form'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🔐
                    </div>
                    <h2 class="form-title">Reset Your Password</h2>
                    <p class="form-subtitle">Enter your email to receive reset instructions</p>
                </div>
            '''),
            Field('email', css_class='form-control-lg'),
            HTML('''
                <div class="security-notice mt-3 mb-4">
                    <div class="alert alert-info">
                        <strong>🔒 Security Notice:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Password reset links expire after 24 hours</li>
                            <li>Only the most recent reset link will be valid</li>
                            <li>If you don't receive an email, check your spam folder</li>
                        </ul>
                    </div>
                </div>
            '''),
            FormActions(
                Submit(
                    'submit',
                    _('Send Reset Instructions'),
                    css_class='btn btn-primary btn-lg w-100 mb-3'
                ),
                HTML('''
                    <div class="text-center">
                        <a href="{% url 'account_login' %}" class="btn btn-link">
                            ← Back to Sign In
                        </a>
                        <span class="mx-2">|</span>
                        <a href="{% url 'accounts_app:account_recovery' %}" class="btn btn-link">
                            Account Recovery
                        </a>
                    </div>
                ''')
            )
        )
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            email = email.lower().strip()
            
            # Additional validation can be added here
            # For example, checking against a list of blocked domains
            
        return email


class SecurityQuestionForm(forms.Form):
    """
    Security questions form for additional account verification.
    
    Note: This is a placeholder for future implementation of security questions.
    """
    
    security_answer_1 = forms.CharField(
        label=_('Security Answer 1'),
        max_length=200,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your answer'),
            }
        ),
        help_text=_('Answer to your first security question.')
    )
    
    security_answer_2 = forms.CharField(
        label=_('Security Answer 2'),
        max_length=200,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your answer'),
            }
        ),
        help_text=_('Answer to your second security question.')
    )
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🔐
                    </div>
                    <h2 class="form-title">Security Verification</h2>
                    <p class="form-subtitle">Answer your security questions to verify your identity</p>
                </div>
            '''),
            Field('security_answer_1'),
            Field('security_answer_2'),
            FormActions(
                Submit(
                    'submit',
                    _('Verify Identity'),
                    css_class='btn btn-primary btn-lg w-100'
                )
            )
        )


class AccountRecoveryForm(forms.Form):
    """
    Account recovery form for when standard password reset doesn't work.
    
    Features:
    - Multiple recovery reasons
    - Additional information field
    - Security validation
    """
    
    RECOVERY_REASONS = [
        ('forgot_password', _('Forgot password and email not received')),
        ('account_locked', _('Account appears to be locked')),
        ('email_changed', _('Email address was changed without permission')),
        ('suspicious_activity', _('Suspicious activity on account')),
        ('lost_access', _('Lost access to email account')),
        ('other', _('Other security concern')),
    ]
    
    email = forms.EmailField(
        label=_('Account Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your account email address'),
                'autocomplete': 'email',
            }
        ),
        help_text=_('The email address associated with your CozyWish account.')
    )
    
    recovery_reason = forms.ChoiceField(
        label=_('Reason for Recovery Request'),
        choices=RECOVERY_REASONS,
        widget=forms.Select(
            attrs={
                'class': 'form-control',
            }
        ),
        help_text=_('Select the reason you need account recovery assistance.')
    )
    
    additional_info = forms.CharField(
        label=_('Additional Information'),
        widget=forms.Textarea(
            attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('Please provide any additional details that might help us verify your identity and resolve the issue...'),
            }
        ),
        help_text=_(
            'Provide details such as: last successful login date, recent activities, '
            'services you\'ve booked, or any other information that can help verify your identity.'
        ),
        required=False
    )
    
    agree_to_verification = forms.BooleanField(
        label=_(
            'I understand that account recovery requires manual verification and may take 24-48 hours'
        ),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must agree to the verification process to proceed.'),
        }
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'account-recovery-form'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🆘
                    </div>
                    <h2 class="form-title">Account Recovery</h2>
                    <p class="form-subtitle">Request manual assistance to recover your account</p>
                </div>
            '''),
            Field('email'),
            Field('recovery_reason'),
            Field('additional_info'),
            HTML('''
                <div class="recovery-notice mt-3 mb-4">
                    <div class="alert alert-warning">
                        <strong>⚠️ Important:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Account recovery requests are manually reviewed by our security team</li>
                            <li>You may be asked to provide additional verification documents</li>
                            <li>The process typically takes 24-48 hours</li>
                            <li>False recovery requests may result in account restrictions</li>
                        </ul>
                    </div>
                </div>
            '''),
            Field('agree_to_verification'),
            FormActions(
                Submit(
                    'submit',
                    _('Submit Recovery Request'),
                    css_class='btn btn-warning btn-lg w-100 mb-3'
                ),
                HTML('''
                    <div class="text-center">
                        <a href="{% url 'account_reset_password' %}" class="btn btn-link">
                            ← Try Password Reset Again
                        </a>
                        <span class="mx-2">|</span>
                        <a href="{% url 'account_login' %}" class="btn btn-link">
                            Back to Sign In
                        </a>
                    </div>
                ''')
            )
        )
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            email = email.lower().strip()
        return email
    
    def clean_additional_info(self):
        additional_info = self.cleaned_data.get('additional_info', '')
        
        # Basic validation to prevent spam/abuse
        if additional_info:
            # Check for minimum meaningful content
            if len(additional_info.strip()) < 10:
                raise ValidationError(
                    _('Please provide more detailed information to help us assist you.')
                )
            
            # Check for suspicious patterns (basic spam detection)
            suspicious_patterns = ['http://', 'https://', 'www.', '.com', '.net', '.org']
            if any(pattern in additional_info.lower() for pattern in suspicious_patterns):
                raise ValidationError(
                    _('Please do not include URLs or promotional content in your recovery request.')
                )
        
        return additional_info


class AccountUnlockForm(forms.Form):
    """
    Simple form for account unlock requests.
    """
    
    email = forms.EmailField(
        label=_('Account Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your account email address'),
                'autocomplete': 'email',
            }
        ),
        help_text=_('The email address of the locked account.')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🔓
                    </div>
                    <h2 class="form-title">Account Unlock Request</h2>
                    <p class="form-subtitle">Request to unlock your account</p>
                </div>
            '''),
            Field('email'),
            HTML('''
                <div class="unlock-notice mt-3 mb-4">
                    <div class="alert alert-info">
                        <strong>ℹ️ Account Unlock Process:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Our security team will review your unlock request</li>
                            <li>You may be contacted for additional verification</li>
                            <li>Unlock requests are typically processed within 2-4 hours</li>
                        </ul>
                    </div>
                </div>
            '''),
            FormActions(
                Submit(
                    'submit',
                    _('Request Account Unlock'),
                    css_class='btn btn-success btn-lg w-100'
                )
            )
        )
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            email = email.lower().strip()
        return email
