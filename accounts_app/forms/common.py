# --- Standard Library Imports ---
import re

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import CustomUser



# --- Form Mixins ---

class AccessibleFormMixin:
    """
    Mixin to add ARIA labels for better accessibility on form fields.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for name, field in self.fields.items():
            field.widget.attrs.setdefault('aria-label', field.label)


class CommonValidationMixin:
    """
    Mixin providing common validation patterns for all user types.

    Features:
    - Email validation with domain checking
    - Password strength validation
    - Phone number formatting and validation
    - Real-time validation attributes
    - Security validation helpers
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.add_validation_attributes()

    def add_validation_attributes(self):
        """Add data attributes for client-side validation."""
        for name, field in self.fields.items():
            # Add validation type based on field name and type
            if 'email' in name.lower():
                field.widget.attrs.update({
                    'data-validation': 'email',
                    'data-validation-realtime': 'true',
                    'autocomplete': 'email',
                })
            elif 'password' in name.lower():
                field.widget.attrs.update({
                    'data-validation': 'password',
                    'data-validation-realtime': 'true',
                    'autocomplete': 'new-password' if 'new' in name.lower() else 'current-password',
                })
            elif 'phone' in name.lower():
                field.widget.attrs.update({
                    'data-validation': 'phone',
                    'data-validation-realtime': 'true',
                    'data-phone-format': 'true',
                    'autocomplete': 'tel',
                })
            elif field.__class__.__name__ == 'URLField':
                field.widget.attrs.update({
                    'data-validation': 'url',
                    'data-validation-realtime': 'true',
                })

            # Add required indicator
            if field.required:
                field.widget.attrs['data-required'] = 'true'
                field.widget.attrs['aria-required'] = 'true'

    def validate_email_domain(self, email):
        """Enhanced email validation with domain checking."""
        if not email:
            return email

        # Basic format validation is handled by EmailField
        # Add domain-specific validation here
        domain = email.split('@')[-1].lower()

        # Block common disposable email domains
        disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email'
        ]

        if domain in disposable_domains:
            raise ValidationError(
                _('Please use a permanent email address. Temporary email services are not allowed.')
            )

        return email

    def validate_password_strength(self, password):
        """Enhanced password strength validation."""
        if not password:
            return password

        errors = []

        if len(password) < 8:
            errors.append(_('Password must be at least 8 characters long.'))

        if not re.search(r'[A-Z]', password):
            errors.append(_('Password must contain at least one uppercase letter.'))

        if not re.search(r'[a-z]', password):
            errors.append(_('Password must contain at least one lowercase letter.'))

        if not re.search(r'\d', password):
            errors.append(_('Password must contain at least one number.'))

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append(_('Password must contain at least one special character.'))

        # Check for common patterns
        common_patterns = ['123456', 'password', 'qwerty', 'abc123']
        if any(pattern in password.lower() for pattern in common_patterns):
            errors.append(_('Password contains common patterns that are not secure.'))

        if errors:
            raise ValidationError(errors)

        return password


class RoleBasedFormMixin:
    """
    Mixin for forms that need role-based field visibility and validation.

    Features:
    - Dynamic field requirements based on user role
    - Role-specific validation rules
    - Conditional field display
    """

    def __init__(self, *args, **kwargs):
        self.user_role = kwargs.pop('user_role', None)
        super().__init__(*args, **kwargs)
        self.apply_role_based_rules()

    def apply_role_based_rules(self):
        """Apply role-specific field requirements and validation."""
        if not self.user_role:
            return

        # Service provider specific requirements
        if self.user_role == 'service_provider':
            business_fields = [
                'business_name', 'business_phone', 'business_email',
                'business_address', 'business_description'
            ]

            for field_name in business_fields:
                if field_name in self.fields:
                    field = self.fields[field_name]
                    field.required = True
                    field.widget.attrs.update({
                        'data-role-required': 'service_provider',
                        'data-validation-message': f'This field is required for service providers.'
                    })

        # Customer specific requirements
        elif self.user_role == 'customer':
            # Customers have fewer required fields
            optional_fields = ['business_name', 'business_phone', 'business_email']

            for field_name in optional_fields:
                if field_name in self.fields:
                    field = self.fields[field_name]
                    field.required = False
                    field.widget.attrs.update({
                        'data-role-optional': 'customer',
                        'style': 'display: none;'  # Hide business fields for customers
                    })


class ProfileCompletionMixin:
    """
    Mixin for tracking and encouraging profile completion.

    Features:
    - Profile completion percentage calculation
    - Smart field prioritization
    - Progress indicators
    - Completion incentives
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.add_completion_tracking()

    def add_completion_tracking(self):
        """Add attributes for profile completion tracking."""
        # Define field weights for completion calculation
        field_weights = {
            'email': 20,
            'first_name': 10,
            'last_name': 10,
            'phone_number': 15,
            'profile_picture': 10,
            'business_name': 15,  # For service providers
            'business_description': 10,
            'address': 10,
            'city': 5,
            'zip_code': 5,
        }

        for name, field in self.fields.items():
            weight = field_weights.get(name, 5)  # Default weight
            field.widget.attrs.update({
                'data-completion-weight': weight,
                'data-completion-field': 'true',
            })

            # Add priority indicators for important fields
            if weight >= 15:
                field.widget.attrs['data-completion-priority'] = 'high'
            elif weight >= 10:
                field.widget.attrs['data-completion-priority'] = 'medium'
            else:
                field.widget.attrs['data-completion-priority'] = 'low'

    def calculate_completion_percentage(self, instance=None):
        """Calculate profile completion percentage."""
        if not instance:
            return 0

        total_weight = 0
        completed_weight = 0

        for name, field in self.fields.items():
            weight = int(field.widget.attrs.get('data-completion-weight', 5))
            total_weight += weight

            # Check if field has value
            if hasattr(instance, name):
                value = getattr(instance, name)
                if value:  # Field has a value
                    completed_weight += weight

        return int((completed_weight / total_weight) * 100) if total_weight > 0 else 0


class SecurityValidationMixin:
    """
    Mixin for enhanced security validation on sensitive operations.

    Features:
    - Rate limiting indicators
    - CSRF protection enhancements
    - Suspicious activity detection
    - Security headers and attributes
    """

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        self.add_security_attributes()

    def add_security_attributes(self):
        """Add security-related attributes to form fields."""
        for name, field in self.fields.items():
            # Add security attributes for sensitive fields
            if any(sensitive in name.lower() for sensitive in ['password', 'email', 'phone', 'ssn', 'credit']):
                field.widget.attrs.update({
                    'data-security-sensitive': 'true',
                    'autocomplete': 'off' if 'password' in name.lower() else field.widget.attrs.get('autocomplete', ''),
                    'data-validation-security': 'true',
                })

            # Add rate limiting indicators
            if name in ['email', 'login', 'username']:
                field.widget.attrs.update({
                    'data-rate-limit': 'true',
                    'data-rate-limit-attempts': '5',
                    'data-rate-limit-window': '300',  # 5 minutes
                })

    def validate_security_context(self):
        """Validate security context for the form submission."""
        if not self.request:
            return

        # Check for suspicious patterns
        user_agent = self.request.META.get('HTTP_USER_AGENT', '')
        if not user_agent or len(user_agent) < 10:
            raise ValidationError(_('Invalid request. Please try again.'))

        # Check for rapid submissions (basic rate limiting)
        session = self.request.session
        last_submission = session.get('last_form_submission', 0)
        current_time = timezone.now().timestamp()

        if current_time - last_submission < 2:  # 2 second minimum between submissions
            raise ValidationError(_('Please wait before submitting again.'))

        session['last_form_submission'] = current_time




# --- Account Deactivation Form ---

class AccountDeactivationForm(AccessibleFormMixin, forms.Form):
    """
    Form for service provider account deactivation with email confirmation.

    Fields:
    - confirm_email: Email field to confirm the user's email address before deactivation.
    """

    confirm_email = forms.EmailField(
        label=_('Confirm your email address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address to confirm'),
                'autocomplete': 'email',
            }
        ),
        help_text=_(
            'Please enter your email address to confirm account deactivation.'
        )
    )

    def __init__(self, user: CustomUser, *args, **kwargs):
        """
        Initialize form with the current user for validation context.

        Args:
            user (CustomUser): The user requesting account deactivation.
        """
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_confirm_email(self) -> str:
        """
        Validate that the provided email matches the authenticated user's email.

        Raises:
            ValidationError: If the emails do not match.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('confirm_email')
        if email and email != self.user.email:
            raise ValidationError(
                _('Email address does not match your account email.'),
                code='invalid_email_match'
            )
        return email









