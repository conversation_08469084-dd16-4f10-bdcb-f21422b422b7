# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Submit, HTML, Div
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ..models import CustomUser


class SocialAccountLinkForm(forms.Form):
    """
    Form for linking social accounts.
    
    Features:
    - Provider selection
    - Data sync preferences
    - Privacy settings
    """
    
    PROVIDER_CHOICES = [
        ('google', 'Google'),
        ('facebook', 'Facebook'),
        ('twitter', 'Twitter'),
        ('linkedin', 'LinkedIn'),
    ]
    
    provider = forms.ChoiceField(
        label=_('Social Provider'),
        choices=PROVIDER_CHOICES,
        widget=forms.Select(
            attrs={
                'class': 'form-control',
            }
        ),
        help_text=_('Select the social media provider you want to connect.')
    )
    
    sync_profile_data = forms.BooleanField(
        label=_('Sync profile information'),
        required=False,
        initial=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        help_text=_('Allow syncing of name, email, and profile picture from your social account.')
    )
    
    allow_auto_login = forms.BooleanField(
        label=_('Allow automatic login'),
        required=False,
        initial=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        help_text=_('Allow signing in using this social account.')
    )
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🔗
                    </div>
                    <h3 class="form-title">Link Social Account</h3>
                    <p class="form-subtitle">Connect your social media account for easier sign-in</p>
                </div>
            '''),
            Field('provider'),
            HTML('<hr class="my-4">'),
            HTML('<h5 style="color: #2F160F; margin-bottom: 1rem;">Privacy & Sync Settings</h5>'),
            Field('sync_profile_data'),
            Field('allow_auto_login'),
            HTML('''
                <div class="privacy-notice mt-3 mb-4">
                    <div class="alert alert-info">
                        <strong>🔒 Privacy Notice:</strong>
                        <ul class="mb-0 mt-2">
                            <li>We only access basic profile information</li>
                            <li>You can unlink your account at any time</li>
                            <li>Your social account data is kept secure</li>
                            <li>We never post to your social media without permission</li>
                        </ul>
                    </div>
                </div>
            '''),
            FormActions(
                Submit(
                    'submit',
                    _('Connect Account'),
                    css_class='btn btn-primary btn-lg w-100'
                )
            )
        )


class SocialAccountSyncForm(forms.Form):
    """
    Form for syncing data from social accounts.
    
    Features:
    - Field selection for sync
    - Preview of changes
    - Conflict resolution
    """
    
    SYNC_FIELD_CHOICES = [
        ('first_name', _('First Name')),
        ('last_name', _('Last Name')),
        ('email', _('Email Address')),
        ('profile_picture', _('Profile Picture')),
    ]
    
    sync_fields = forms.MultipleChoiceField(
        label=_('Fields to Sync'),
        choices=SYNC_FIELD_CHOICES,
        widget=forms.CheckboxSelectMultiple(
            attrs={
                'class': 'form-check-input',
            }
        ),
        help_text=_('Select which fields you want to update from your social account.')
    )
    
    confirm_sync = forms.BooleanField(
        label=_('I understand that this will overwrite my current information'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must confirm that you understand the sync will overwrite current data.'),
        }
    )
    
    def __init__(self, *args, **kwargs):
        self.social_account = kwargs.pop('social_account', None)
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Customize field choices based on available data
        if self.social_account:
            available_fields = []
            extra_data = self.social_account.extra_data
            
            # Check which fields are available in social account
            field_mapping = {
                'first_name': ['given_name', 'first_name'],
                'last_name': ['family_name', 'last_name'],
                'email': ['email'],
                'profile_picture': ['picture', 'avatar_url'],
            }
            
            for field_key, field_label in self.SYNC_FIELD_CHOICES:
                social_fields = field_mapping.get(field_key, [])
                if any(sf in extra_data for sf in social_fields):
                    available_fields.append((field_key, field_label))
            
            self.fields['sync_fields'].choices = available_fields
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🔄
                    </div>
                    <h3 class="form-title">Sync Account Data</h3>
                    <p class="form-subtitle">Update your profile with information from your social account</p>
                </div>
            '''),
            Field('sync_fields'),
            HTML('''
                <div class="sync-preview mt-3 mb-4" id="sync-preview">
                    <!-- Preview will be populated by JavaScript -->
                </div>
            '''),
            Field('confirm_sync'),
            FormActions(
                Submit(
                    'submit',
                    _('Sync Selected Fields'),
                    css_class='btn btn-warning btn-lg w-100'
                )
            )
        )


class SocialAccountConflictResolutionForm(forms.Form):
    """
    Form for resolving social account conflicts.
    
    Features:
    - Conflict type handling
    - Resolution options
    - Data preservation choices
    """
    
    RESOLUTION_CHOICES = [
        ('ignore', _('Ignore conflict and keep current setup')),
        ('update_email', _('Update my email to match social account')),
        ('merge_accounts', _('Merge accounts (contact support)')),
    ]
    
    resolution = forms.ChoiceField(
        label=_('How would you like to resolve this conflict?'),
        choices=RESOLUTION_CHOICES,
        widget=forms.RadioSelect(
            attrs={
                'class': 'form-check-input',
            }
        ),
        help_text=_('Choose how to handle the conflicting information.')
    )
    
    confirm_resolution = forms.BooleanField(
        label=_('I understand the consequences of this action'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must confirm that you understand the consequences.'),
        }
    )
    
    def __init__(self, *args, **kwargs):
        self.conflict_type = kwargs.pop('conflict_type', 'email_conflict')
        self.social_account = kwargs.pop('social_account', None)
        super().__init__(*args, **kwargs)
        
        # Customize choices based on conflict type
        if self.conflict_type == 'email_conflict':
            self.fields['resolution'].help_text = _(
                'Your social account has a different email address than your CozyWish account. '
                'Choose how to resolve this conflict.'
            )
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        ⚠️
                    </div>
                    <h3 class="form-title">Resolve Account Conflict</h3>
                    <p class="form-subtitle">We found conflicting information between your accounts</p>
                </div>
            '''),
            HTML('''
                <div class="conflict-details mb-4">
                    <div class="alert alert-warning">
                        <strong>Conflict Details:</strong>
                        <p class="mt-2 mb-0">Your social account contains different information than your CozyWish account. Please choose how to resolve this.</p>
                    </div>
                </div>
            '''),
            Field('resolution'),
            HTML('''
                <div class="resolution-warning mt-3 mb-4">
                    <div class="alert alert-info">
                        <strong>ℹ️ Important:</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>Ignore:</strong> No changes will be made to either account</li>
                            <li><strong>Update email:</strong> Your CozyWish email will be changed to match your social account</li>
                            <li><strong>Merge accounts:</strong> Our support team will help you merge the accounts manually</li>
                        </ul>
                    </div>
                </div>
            '''),
            Field('confirm_resolution'),
            FormActions(
                Submit(
                    'submit',
                    _('Resolve Conflict'),
                    css_class='btn btn-warning btn-lg w-100'
                )
            )
        )


class SocialAccountUnlinkForm(forms.Form):
    """
    Form for unlinking social accounts.
    
    Features:
    - Safety checks
    - Confirmation requirements
    - Data preservation options
    """
    
    confirm_unlink = forms.BooleanField(
        label=_('I want to unlink this social account'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must confirm that you want to unlink this account.'),
        }
    )
    
    understand_consequences = forms.BooleanField(
        label=_('I understand I will no longer be able to sign in using this social account'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must acknowledge the consequences of unlinking.'),
        }
    )
    
    def __init__(self, *args, **kwargs):
        self.social_account = kwargs.pop('social_account', None)
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Add safety warnings based on user's authentication setup
        safety_warnings = []
        
        if self.user and not self.user.has_usable_password():
            from allauth.socialaccount.models import SocialAccount
            other_accounts = SocialAccount.objects.filter(user=self.user)
            if self.social_account:
                other_accounts = other_accounts.exclude(id=self.social_account.id)
            
            if not other_accounts.exists():
                safety_warnings.append(
                    "⚠️ Warning: You don't have a password set and this is your only social account. "
                    "Unlinking it will prevent you from signing in!"
                )
        
        self.safety_warnings = safety_warnings
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        
        warning_html = ''
        if safety_warnings:
            warning_html = '<div class="alert alert-danger">'
            for warning in safety_warnings:
                warning_html += f'<p class="mb-1">{warning}</p>'
            warning_html += '</div>'
        
        self.helper.layout = Layout(
            HTML('''
                <div class="form-header text-center mb-4">
                    <div class="form-icon">
                        🔓
                    </div>
                    <h3 class="form-title">Unlink Social Account</h3>
                    <p class="form-subtitle">Remove the connection to your social account</p>
                </div>
            '''),
            HTML(warning_html) if warning_html else HTML(''),
            Field('confirm_unlink'),
            Field('understand_consequences'),
            FormActions(
                Submit(
                    'submit',
                    _('Unlink Account'),
                    css_class='btn btn-danger btn-lg w-100'
                )
            )
        )
