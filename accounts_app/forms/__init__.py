"""
Forms package for accounts_app.

This package contains all form classes organized by feature area for better maintainability.
All forms are imported here to maintain backward compatibility.
"""

# --- Local App Imports ---
from .common import (
    AccountDeactivationForm,
    AccessibleFormMixin,
    CommonValidationMixin,
    RoleBasedFormMixin,
    ProfileCompletionMixin,
    SecurityValidationMixin,
)
from .customer import (
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerProfileForm,
    CustomerSignupForm,
)
from .provider import (
    ServiceProviderLoginForm,
    ServiceProviderPasswordChangeForm,
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
)
from .team import TeamMemberForm

# Import allauth forms (optional import to avoid circular dependencies)
try:
    from ..allauth_forms import (
        CozyWishSignupForm,
        CozyWishSocialSignupForm,
        CozyWishLoginForm,
    )
    ALLAUTH_FORMS_AVAILABLE = True
except ImportError:
    ALLAUTH_FORMS_AVAILABLE = False


# Make all forms available at package level for backward compatibility
__all__ = [
    # Common forms and mixins
    'AccessibleFormMixin',
    'CommonValidationMixin',
    'RoleBasedFormMixin',
    'ProfileCompletionMixin',
    'SecurityValidationMixin',
    'AccountDeactivationForm',

    # Customer forms
    'CustomerSignupForm',
    'CustomerLoginForm',
    'CustomerProfileForm',
    'CustomerPasswordChangeForm',

    # Service provider forms
    'ServiceProviderSignupForm',
    'ServiceProviderLoginForm',
    'ServiceProviderProfileForm',
    'ServiceProviderPasswordChangeForm',

    # Team management forms
    'TeamMemberForm',
]

# Add allauth forms to __all__ if available
if ALLAUTH_FORMS_AVAILABLE:
    __all__.extend([
        'CozyWishSignupForm',
        'CozyWishSocialSignupForm',
        'CozyWishLoginForm',
    ])
