# Customer views package
# Import all views for backward compatibility

from .auth import (
    CustomerSignupView,
    customer_login_view,
    customer_logout_view,
    unified_logout_view,
    CustomerPasswordResetView,
    CustomerPasswordResetDoneView,
    CustomerPasswordResetConfirmView,
    CustomerPasswordResetCompleteView,
)
from .profile import (
    CustomerProfileView,
    CustomerProfileEditView,
)
from .account import (
    customer_change_password_view,
    customer_deactivate_account_view,
)

__all__ = [
    'CustomerSignupView',
    'customer_login_view',
    'customer_logout_view',
    'unified_logout_view',
    'CustomerPasswordResetView',
    'CustomerPasswordResetDoneView',
    'CustomerPasswordResetConfirmView',
    'CustomerPasswordResetCompleteView',
    'CustomerProfileView',
    'CustomerProfileEditView',
    'customer_change_password_view',
    'customer_deactivate_account_view',
]
