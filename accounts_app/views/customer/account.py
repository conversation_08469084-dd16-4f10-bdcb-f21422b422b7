# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, render
from django.views.decorators.http import require_http_methods

# --- Local App Imports ---
from ...forms import (
    AccountDeactivationForm,
    CustomerPasswordChangeForm,
)
from ...logging_utils import (
    log_account_lifecycle_event,
    log_authentication_event,
    log_error,
    log_user_activity,
    performance_monitor,
)
from ..common import MESSAGES, logger


# --- Customer Security Features ---

@login_required
@require_http_methods(["GET", "POST"])
@performance_monitor("customer_password_change")
def customer_change_password_view(request):
    """
    Handle customer password changes with security best practices.
    
    Security Features:
    - Requires current password validation
    - Automatic session termination after change
    - Comprehensive audit logging
    - Error handling with detailed diagnostics
    """
    # Verify customer role
    if not request.user.is_customer:
        return redirect('home')
    
    # Process password change request
    if request.method == 'POST':
        form = CustomerPasswordChangeForm(user=request.user, data=request.POST)
        
        if form.is_valid():
            try:
                user = request.user
                user_email = user.email
                
                # Update password
                form.save()
                
                # Terminate session
                logout(request)
                
                # Security logging
                log_user_activity(
                    activity_type='password_change',
                    user=user,
                    request=request,
                    details={
                        'user_type': 'customer',
                        'ip_address': request.META.get('REMOTE_ADDR')
                    }
                )
                log_authentication_event(
                    event_type='password_change',
                    user_email=user_email,
                    success=True,
                    request=request
                )
                
                # User feedback
                messages.success(request, MESSAGES['password_change'])
                logger.info(
                    f"Customer password changed: {user_email}",
                    extra={
                        'user_id': user.id,
                        'event': 'password_change_success'
                    }
                )
                
                return redirect('home')
            
            except Exception as error:
                # Error handling
                log_error(
                    error_type='password_change',
                    error_message="Password change failed",
                    user=request.user,
                    request=request,
                    exception=error,
                    details={
                        'user_type': 'customer',
                        'form_errors': form.errors
                    }
                )
                messages.error(request, MESSAGES['password_change_error'])
    
    # Initial GET request
    else:
        form = CustomerPasswordChangeForm(user=request.user)
    
    return render(request, 'accounts_app/customer/change_password.html', {'form': form})


@login_required
@require_http_methods(["GET", "POST"])
@performance_monitor("customer_account_deactivation")
def customer_deactivate_account_view(request):
    """
    Handle customer account deactivation with security safeguards.
    
    Security Features:
    - Email confirmation requirement
    - Automatic session termination
    - Audit trail with reason tracking
    - Error handling with rollback safety
    """
    # Verify customer role
    if not request.user.is_customer:
        return redirect('home')
    
    # Process deactivation request
    if request.method == 'POST':
        form = AccountDeactivationForm(user=request.user, data=request.POST)
        
        if form.is_valid():
            try:
                user = request.user
                user_email = user.email
                
                # Deactivate account
                user.is_active = False
                user.save()
                
                # Security logging
                log_account_lifecycle_event(
                    event_type='deactivation',
                    user=user,
                    request=request,
                    reason='user_requested',
                    additional_data={
                        'ip_address': request.META.get('REMOTE_ADDR'),
                        'confirmation_email': form.cleaned_data['confirm_email']
                    }
                )
                
                # Terminate session
                logout(request)
                
                # User feedback
                messages.success(request, MESSAGES['account_deactivated'])
                logger.info(
                    f"Customer account deactivated: {user_email}",
                    extra={
                        'user_id': user.id,
                        'event': 'account_deactivation_success'
                    }
                )
                
                return redirect('home')
            
            except Exception as error:
                # Error handling and rollback
                log_error(
                    error_type='account_deactivation',
                    error_message="Account deactivation failed",
                    user=request.user,
                    request=request,
                    exception=error,
                    details={
                        'user_type': 'customer',
                        'form_data': form.cleaned_data
                    }
                )
                messages.error(request, MESSAGES['deactivation_error'])
                return redirect('accounts_app:customer_profile')
    
    # Initial GET request
    else:
        form = AccountDeactivationForm(user=request.user)
    
    return render(request, 'accounts_app/customer/deactivate_account.html', {'form': form})
