# --- Third-Party Imports ---
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.http import HttpResponseRedirect
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.views.generic import DetailView, UpdateView

# --- Local App Imports ---
from ...forms import CustomerProfileForm
from ...logging_utils import log_error, log_profile_change
from ...models import CustomerProfile
from ..common import MESSAGES, logger


# --- Customer Profile Management ---

class CustomerProfileView(DetailView):
    """
    Display authenticated customer's profile details.
    
    Features:
    - Automatic profile creation for new users
    - Role-based access control
    - Context injection for template rendering
    """
    model = CustomerProfile
    template_name = 'accounts_app/customer/profile.html'
    context_object_name = 'profile'

    def dispatch(self, request, *args, **kwargs):
        """Enforce authentication and customer role requirements."""
        if not request.user.is_authenticated or not request.user.is_customer:
            return redirect('accounts_app:customer_login')
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        """Retrieve or create customer profile with fail-safe."""
        try:
            return CustomerProfile.objects.get(user=self.request.user)
        except CustomerProfile.DoesNotExist:
            return CustomerProfile.objects.create(user=self.request.user)


class CustomerProfileEditView(UpdateView):
    """
    Handle customer profile updates with validation.
    
    Features:
    - Automatic profile creation for new users
    - Form validation with error handling
    - Success messaging and activity logging
    """
    model = CustomerProfile
    form_class = CustomerProfileForm
    template_name = 'accounts_app/customer/profile_edit.html'
    success_url = reverse_lazy('accounts_app:customer_profile')

    def dispatch(self, request, *args, **kwargs):
        """Enforce authentication and customer role requirements."""
        if not request.user.is_authenticated or not request.user.is_customer:
            return redirect('accounts_app:customer_login')
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        """Retrieve or create customer profile with fail-safe."""
        try:
            return CustomerProfile.objects.get(user=self.request.user)
        except CustomerProfile.DoesNotExist:
            return CustomerProfile.objects.create(user=self.request.user)

    def post(self, request, *args, **kwargs):
        """Handle POST requests with special logic for profile picture only updates."""
        self.object = self.get_object()

        # Check if this is a profile picture only update
        # Profile picture only updates have only csrf token in POST and profile_picture in FILES
        post_keys = set(request.POST.keys())
        file_keys = set(request.FILES.keys())

        is_profile_picture_only = (
            post_keys == {'csrfmiddlewaretoken'} and
            file_keys == {'profile_picture'}
        )

        if is_profile_picture_only:
            return self._handle_profile_picture_only_update(request)
        else:
            # Handle normal form submission
            return super().post(request, *args, **kwargs)

    def _handle_profile_picture_only_update(self, request):
        """Handle profile picture only updates without affecting other fields."""
        try:
            profile = self.object
            profile_picture = request.FILES.get('profile_picture')

            if profile_picture:
                # Validate the image using the form's clean method
                form = self.get_form_class()(instance=profile)
                form.files = request.FILES

                # Only validate the profile_picture field
                try:
                    cleaned_picture = form.fields['profile_picture'].clean(profile_picture)
                    # Use the form's clean_profile_picture method if it exists
                    if hasattr(form, 'clean_profile_picture'):
                        form.cleaned_data = {'profile_picture': cleaned_picture}
                        cleaned_picture = form.clean_profile_picture()
                except ValidationError as e:
                    error_message = str(e) if hasattr(e, 'message') else str(e)
                    messages.error(request, f"Invalid image: {error_message}")
                    return redirect(self.success_url)

                # Update only the profile picture
                profile.profile_picture = cleaned_picture
                profile.save(update_fields=['profile_picture'])

                # Log the change
                log_profile_change(
                    user=request.user,
                    profile_type='customer',
                    changed_fields={'profile_picture': cleaned_picture},
                    request=request
                )

                messages.success(request, "Profile picture updated successfully!")
                logger.info(
                    f"Customer profile picture updated: {request.user.email}",
                    extra={
                        'user_id': request.user.id,
                        'changed_fields': ['profile_picture']
                    }
                )
            else:
                messages.error(request, "No image file provided.")

        except Exception as error:
            log_error(
                error_type='profile_picture_update',
                error_message="Profile picture update failed",
                user=request.user,
                request=request,
                exception=error
            )
            messages.error(request, 'An error occurred while updating your profile picture.')

        return redirect(self.success_url)

    def form_valid(self, form):
        """Process valid form submissions with activity logging and partial update support."""
        try:
            # Get the current profile instance
            profile = self.object

            # Only update fields that were actually submitted in the form
            # This prevents clearing fields that weren't included in the form submission
            submitted_fields = set(self.request.POST.keys()) | set(self.request.FILES.keys())
            submitted_fields.discard('csrfmiddlewaretoken')  # Remove CSRF token

            # Identify changed fields for audit logging
            changed_fields = {}
            if form.changed_data:
                for field in form.changed_data:
                    changed_fields[field] = form.cleaned_data[field]

            # Save the form but don't commit to database yet
            updated_profile = form.save(commit=False)

            # Only update fields that were actually submitted
            form_fields = set(form.fields.keys())
            fields_to_update = []

            for field_name in form_fields:
                # Check if field was submitted (either in POST data or FILES)
                field_submitted = (
                    field_name in self.request.POST or
                    field_name in self.request.FILES or
                    # Special handling for checkboxes and selects that might not appear in POST when empty
                    (field_name in ['gender', 'birth_month', 'birth_year'] and field_name in submitted_fields)
                )

                if field_submitted:
                    # Update this field
                    new_value = getattr(updated_profile, field_name)
                    setattr(profile, field_name, new_value)
                    fields_to_update.append(field_name)

            # Save only the updated fields
            if fields_to_update:
                profile.save(update_fields=fields_to_update)

            # Log profile changes
            if changed_fields:
                log_profile_change(
                    user=self.request.user,
                    profile_type='customer',
                    changed_fields=changed_fields,
                    request=self.request
                )

            # User feedback
            messages.success(self.request, MESSAGES['profile_update'])
            logger.info(
                f"Customer profile updated: {self.request.user.email}",
                extra={
                    'user_id': self.request.user.id,
                    'changed_fields': list(changed_fields.keys()),
                    'updated_fields': fields_to_update
                }
            )

            return HttpResponseRedirect(self.get_success_url())

        except Exception as error:
            log_error(
                error_type='profile_update',
                error_message="Customer profile update failed",
                user=self.request.user,
                request=self.request,
                exception=error
            )
            messages.error(
                self.request,
                'An error occurred while updating your profile'
            )
            return self.form_invalid(form)
