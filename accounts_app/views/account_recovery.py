# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView, FormView
from django.urls import reverse_lazy
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
import logging
from datetime import timedelta

# --- Third-Party Imports ---
from allauth.account.models import EmailAddress
from allauth.account.utils import send_email_confirmation
from allauth.account import app_settings as allauth_settings
try:
    from django_ratelimit.decorators import ratelimit
except ImportError:
    def ratelimit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

# --- Local App Imports ---
from ..models import CustomUser
from ..forms.account_recovery import (
    EnhancedPasswordResetForm,
    SecurityQuestionForm,
    AccountRecoveryForm,
)

User = get_user_model()
logger = logging.getLogger(__name__)


@method_decorator([
    csrf_protect,
    ratelimit(key='ip', rate='5/5m', method='POST', block=True)
], name='dispatch')
class EnhancedPasswordResetView(FormView):
    """
    Enhanced password reset with security features.
    
    Features:
    - Rate limiting (5 attempts per 5 minutes per IP)
    - Security logging
    - Enhanced email templates
    - Account lockout protection
    """
    template_name = 'allauth/account/password_reset_enhanced.html'
    form_class = EnhancedPasswordResetForm
    success_url = reverse_lazy('account_reset_password_done')
    
    def form_valid(self, form):
        email = form.cleaned_data['email']
        
        # Log password reset attempt
        logger.info(
            "Password reset requested",
            extra={
                'email': email,
                'ip_address': self.request.META.get('REMOTE_ADDR'),
                'user_agent': self.request.META.get('HTTP_USER_AGENT', '')[:200],
                'event': 'password_reset_requested'
            }
        )
        
        # Check if user exists and is active
        try:
            user = User.objects.get(email=email, is_active=True)
            
            # Check for account lockout
            if self._is_account_locked(user):
                messages.warning(
                    self.request,
                    "This account is temporarily locked due to security reasons. "
                    "Please contact support for assistance."
                )
                return redirect('accounts_app:account_recovery')
            
            # Send password reset email
            form.save(request=self.request)
            
            # Send security notification
            self._send_security_notification(user)
            
            messages.success(
                self.request,
                "Password reset instructions have been sent to your email address."
            )
            
        except User.DoesNotExist:
            # Don't reveal if email exists or not for security
            messages.success(
                self.request,
                "If an account with that email exists, password reset instructions have been sent."
            )
        
        return super().form_valid(form)
    
    def _is_account_locked(self, user):
        """Check if account is locked due to security reasons."""
        # This would integrate with a more sophisticated lockout system
        # For now, just check if user has been inactive for security reasons
        return hasattr(user, 'security_lockout') and user.security_lockout
    
    def _send_security_notification(self, user):
        """Send security notification about password reset request."""
        try:
            context = {
                'user': user,
                'timestamp': timezone.now(),
                'ip_address': self.request.META.get('REMOTE_ADDR', 'Unknown'),
                'user_agent': self.request.META.get('HTTP_USER_AGENT', 'Unknown'),
            }
            
            subject = '[CozyWish] Security Alert: Password Reset Requested'
            message = render_to_string(
                'emails/security_notification.txt',
                context
            )
            html_message = render_to_string(
                'emails/security_notification.html',
                context
            )
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=True,
            )
            
            logger.info(
                "Security notification sent",
                extra={
                    'user_id': user.id,
                    'email': user.email,
                    'event': 'security_notification_sent'
                }
            )
            
        except Exception as e:
            logger.error(
                "Failed to send security notification",
                extra={
                    'user_id': user.id,
                    'email': user.email,
                    'error': str(e),
                    'event': 'security_notification_failed'
                }
            )


@method_decorator([
    csrf_protect,
    ratelimit(key='ip', rate='3/10m', method='POST', block=True)
], name='dispatch')
class AccountRecoveryView(FormView):
    """
    Account recovery for locked or compromised accounts.
    
    Features:
    - Alternative recovery method when password reset fails
    - Security questions (if implemented)
    - Manual review process for suspicious activity
    """
    template_name = 'allauth/account/account_recovery.html'
    form_class = AccountRecoveryForm
    success_url = reverse_lazy('accounts_app:account_recovery_submitted')
    
    def form_valid(self, form):
        email = form.cleaned_data['email']
        reason = form.cleaned_data['recovery_reason']
        additional_info = form.cleaned_data['additional_info']
        
        # Log recovery request
        logger.warning(
            "Account recovery requested",
            extra={
                'email': email,
                'reason': reason,
                'ip_address': self.request.META.get('REMOTE_ADDR'),
                'user_agent': self.request.META.get('HTTP_USER_AGENT', '')[:200],
                'event': 'account_recovery_requested'
            }
        )
        
        # Send recovery request to support team
        self._send_recovery_request_to_support(email, reason, additional_info)
        
        # Send confirmation to user
        self._send_recovery_confirmation_to_user(email)
        
        messages.success(
            self.request,
            "Your account recovery request has been submitted. "
            "Our support team will review it and contact you within 24-48 hours."
        )
        
        return super().form_valid(form)
    
    def _send_recovery_request_to_support(self, email, reason, additional_info):
        """Send account recovery request to support team."""
        try:
            context = {
                'email': email,
                'reason': reason,
                'additional_info': additional_info,
                'timestamp': timezone.now(),
                'ip_address': self.request.META.get('REMOTE_ADDR', 'Unknown'),
                'user_agent': self.request.META.get('HTTP_USER_AGENT', 'Unknown'),
            }
            
            subject = f'[CozyWish] Account Recovery Request - {email}'
            message = render_to_string(
                'emails/account_recovery_support.txt',
                context
            )
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')],
                fail_silently=True,
            )
            
        except Exception as e:
            logger.error(
                "Failed to send recovery request to support",
                extra={
                    'email': email,
                    'error': str(e),
                    'event': 'recovery_support_email_failed'
                }
            )
    
    def _send_recovery_confirmation_to_user(self, email):
        """Send confirmation email to user about recovery request."""
        try:
            context = {
                'email': email,
                'timestamp': timezone.now(),
            }
            
            subject = '[CozyWish] Account Recovery Request Received'
            message = render_to_string(
                'emails/account_recovery_confirmation.txt',
                context
            )
            html_message = render_to_string(
                'emails/account_recovery_confirmation.html',
                context
            )
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                html_message=html_message,
                fail_silently=True,
            )
            
        except Exception as e:
            logger.error(
                "Failed to send recovery confirmation to user",
                extra={
                    'email': email,
                    'error': str(e),
                    'event': 'recovery_confirmation_failed'
                }
            )


class AccountRecoverySubmittedView(TemplateView):
    """Confirmation page after account recovery request submission."""
    template_name = 'allauth/account/account_recovery_submitted.html'


@method_decorator([
    csrf_protect,
    ratelimit(key='ip', rate='10/h', method='POST', block=True)
], name='dispatch')
class AccountUnlockRequestView(FormView):
    """
    Handle account unlock requests for locked accounts.
    """
    template_name = 'allauth/account/account_unlock_request.html'
    form_class = AccountRecoveryForm
    success_url = reverse_lazy('accounts_app:account_unlock_submitted')
    
    def form_valid(self, form):
        email = form.cleaned_data['email']
        
        # Check if user exists and is actually locked
        try:
            user = User.objects.get(email=email)
            
            if user.is_active:
                messages.info(
                    self.request,
                    "This account is not locked. If you're having trouble signing in, "
                    "try resetting your password instead."
                )
                return redirect('account_reset_password')
            
            # Log unlock request
            logger.info(
                "Account unlock requested",
                extra={
                    'user_id': user.id,
                    'email': email,
                    'ip_address': self.request.META.get('REMOTE_ADDR'),
                    'user_agent': self.request.META.get('HTTP_USER_AGENT', '')[:200],
                    'event': 'account_unlock_requested'
                }
            )
            
            # Send unlock request to support
            self._send_unlock_request_to_support(user)
            
            messages.success(
                self.request,
                "Account unlock request submitted. Our support team will review it shortly."
            )
            
        except User.DoesNotExist:
            # Don't reveal if email exists
            messages.success(
                self.request,
                "If an account with that email exists and is locked, "
                "an unlock request has been submitted."
            )
        
        return super().form_valid(form)
    
    def _send_unlock_request_to_support(self, user):
        """Send account unlock request to support team."""
        try:
            context = {
                'user': user,
                'timestamp': timezone.now(),
                'ip_address': self.request.META.get('REMOTE_ADDR', 'Unknown'),
                'user_agent': self.request.META.get('HTTP_USER_AGENT', 'Unknown'),
            }
            
            subject = f'[CozyWish] Account Unlock Request - {user.email}'
            message = render_to_string(
                'emails/account_unlock_support.txt',
                context
            )
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')],
                fail_silently=True,
            )
            
        except Exception as e:
            logger.error(
                "Failed to send unlock request to support",
                extra={
                    'user_id': user.id,
                    'error': str(e),
                    'event': 'unlock_support_email_failed'
                }
            )


class AccountUnlockSubmittedView(TemplateView):
    """Confirmation page after account unlock request submission."""
    template_name = 'allauth/account/account_unlock_submitted.html'
