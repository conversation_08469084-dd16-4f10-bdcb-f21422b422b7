# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView
from django.urls import reverse
from django.conf import settings
import logging

# --- Third-Party Imports ---
from allauth.account.models import EmailConfirmation, EmailAddress
from allauth.account.utils import send_email_confirmation
from allauth.account import app_settings as allauth_settings
try:
    from django_ratelimit.decorators import ratelimit
except ImportError:
    # Fallback if django-ratelimit is not installed
    def ratelimit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

# --- Local App Imports ---
from ..models import CustomUser

logger = logging.getLogger(__name__)


class EmailVerificationStatusView(TemplateView):
    """
    Display email verification status and provide resend functionality.
    
    Features:
    - Show current verification status
    - Display verification attempts
    - Provide resend verification email option
    - Rate limiting for resend requests
    - Security logging
    """
    template_name = 'allauth/account/email_verification_status.html'
    
    @method_decorator(login_required)
    @method_decorator(csrf_protect)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get email address object
        try:
            email_address = EmailAddress.objects.get(
                user=user,
                email=user.email,
                primary=True
            )
        except EmailAddress.DoesNotExist:
            email_address = None
        
        # Get pending confirmations
        pending_confirmations = EmailConfirmation.objects.filter(
            email_address__user=user,
            email_address__email=user.email
        ).order_by('-created')
        
        # Check if user can resend verification
        can_resend = self._can_resend_verification(user, pending_confirmations)
        
        context.update({
            'email_address': email_address,
            'pending_confirmations': pending_confirmations,
            'can_resend': can_resend,
            'verification_required': allauth_settings.EMAIL_VERIFICATION == 'mandatory',
            'resend_cooldown': getattr(settings, 'EMAIL_VERIFICATION_RESEND_COOLDOWN', 300),  # 5 minutes
        })
        
        return context
    
    def _can_resend_verification(self, user, pending_confirmations):
        """Check if user can resend verification email."""
        if not pending_confirmations.exists():
            return True
        
        # Check cooldown period
        latest_confirmation = pending_confirmations.first()
        cooldown_seconds = getattr(settings, 'EMAIL_VERIFICATION_RESEND_COOLDOWN', 300)
        time_since_last = timezone.now() - latest_confirmation.created
        
        return time_since_last.total_seconds() > cooldown_seconds


@method_decorator([
    login_required,
    csrf_protect,
    ratelimit(key='user', rate='3/5m', method='POST', block=True)
], name='dispatch')
class ResendVerificationEmailView(View):
    """
    Handle resending verification emails with rate limiting and security.
    
    Features:
    - Rate limiting (3 attempts per 5 minutes)
    - Security logging
    - AJAX support
    - Proper error handling
    """
    
    def post(self, request):
        user = request.user
        
        # Check if user already verified
        try:
            email_address = EmailAddress.objects.get(
                user=user,
                email=user.email,
                primary=True
            )
            if email_address.verified:
                message = "Your email address is already verified."
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': message,
                        'already_verified': True
                    })
                messages.info(request, message)
                return redirect('accounts_app:email_verification_status')
        except EmailAddress.DoesNotExist:
            pass
        
        # Check cooldown
        pending_confirmations = EmailConfirmation.objects.filter(
            email_address__user=user,
            email_address__email=user.email
        ).order_by('-created')
        
        if pending_confirmations.exists():
            latest_confirmation = pending_confirmations.first()
            cooldown_seconds = getattr(settings, 'EMAIL_VERIFICATION_RESEND_COOLDOWN', 300)
            time_since_last = timezone.now() - latest_confirmation.created
            
            if time_since_last.total_seconds() <= cooldown_seconds:
                remaining_time = cooldown_seconds - int(time_since_last.total_seconds())
                message = f"Please wait {remaining_time} seconds before requesting another verification email."
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': message,
                        'cooldown_remaining': remaining_time
                    })
                messages.warning(request, message)
                return redirect('accounts_app:email_verification_status')
        
        # Send verification email
        try:
            send_email_confirmation(request, user)
            
            # Log successful resend
            logger.info(
                "Verification email resent",
                extra={
                    'user_id': user.id,
                    'email': user.email,
                    'ip_address': request.META.get('REMOTE_ADDR'),
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
                    'event': 'verification_email_resent'
                }
            )
            
            message = "Verification email sent! Please check your inbox and spam folder."
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': message
                })
            
            messages.success(request, message)
            
        except Exception as e:
            logger.error(
                "Failed to resend verification email",
                extra={
                    'user_id': user.id,
                    'email': user.email,
                    'error': str(e),
                    'event': 'verification_email_resend_failed'
                }
            )
            
            message = "Failed to send verification email. Please try again later."
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': message
                })
            
            messages.error(request, message)
        
        return redirect('accounts_app:email_verification_status')


@require_http_methods(["GET"])
@login_required
def email_verification_check_ajax(request):
    """
    AJAX endpoint to check email verification status.
    
    Returns JSON with current verification status.
    """
    user = request.user
    
    try:
        email_address = EmailAddress.objects.get(
            user=user,
            email=user.email,
            primary=True
        )
        verified = email_address.verified
    except EmailAddress.DoesNotExist:
        verified = False
    
    return JsonResponse({
        'verified': verified,
        'email': user.email,
        'timestamp': timezone.now().isoformat()
    })


class EmailVerificationSuccessView(TemplateView):
    """Custom success page after email verification."""
    template_name = 'allauth/account/email_verification_success.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Determine next steps based on user role
        if user.is_authenticated:
            if user.role == CustomUser.SERVICE_PROVIDER:
                next_url = reverse('accounts_app:provider_profile')
                next_action = "Complete your business profile"
            elif user.role == CustomUser.CUSTOMER:
                next_url = reverse('dashboard_app:customer_dashboard')
                next_action = "Explore spa services"
            else:
                next_url = reverse('venues_app:home')
                next_action = "Get started"
        else:
            next_url = reverse('accounts_app:customer_login')
            next_action = "Sign in to your account"
        
        context.update({
            'next_url': next_url,
            'next_action': next_action,
        })
        
        return context


class EmailVerificationErrorView(TemplateView):
    """Custom error page for email verification failures."""
    template_name = 'allauth/account/email_verification_error.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get error type from URL parameter
        error_type = self.request.GET.get('error', 'unknown')
        
        error_messages = {
            'expired': {
                'title': 'Verification Link Expired',
                'message': 'This verification link has expired. Please request a new one.',
                'show_resend': True
            },
            'invalid': {
                'title': 'Invalid Verification Link',
                'message': 'This verification link is invalid or has already been used.',
                'show_resend': True
            },
            'already_verified': {
                'title': 'Email Already Verified',
                'message': 'Your email address has already been verified.',
                'show_resend': False
            },
            'unknown': {
                'title': 'Verification Error',
                'message': 'An error occurred during email verification. Please try again.',
                'show_resend': True
            }
        }
        
        error_info = error_messages.get(error_type, error_messages['unknown'])
        
        context.update({
            'error_type': error_type,
            'error_title': error_info['title'],
            'error_message': error_info['message'],
            'show_resend': error_info['show_resend'],
        })
        
        return context
