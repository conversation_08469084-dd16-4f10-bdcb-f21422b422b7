"""
Django Allauth Integration Views

This module provides custom views that integrate with django-allauth
while maintaining role-based signup flows and backward compatibility.
"""

from django.shortcuts import redirect, render
from django.views.generic import View
from django.contrib import messages
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from django.http import HttpRequest, HttpResponse
import logging

from accounts_app.models import CustomUser


@method_decorator([csrf_protect, never_cache], name='dispatch')
class RoleBasedSignupView(View):
    """
    Base view for role-based signup that integrates with allauth.
    
    This view sets the user role in the session and redirects to
    allauth signup, which will use our custom adapter to assign
    the correct role and create the appropriate profile.
    """
    
    role = None  # To be set by subclasses
    template_name = None  # To be set by subclasses
    success_url_name = 'account_signup'  # Allauth signup URL
    
    def dispatch(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        """Redirect authenticated users to appropriate destinations"""
        if request.user.is_authenticated:
            if self.role == CustomUser.CUSTOMER and request.user.is_customer:
                return redirect('accounts_app:customer_profile')
            elif self.role == CustomUser.SERVICE_PROVIDER and request.user.is_service_provider:
                return redirect('accounts_app:service_provider_profile')
            else:
                return redirect('dashboard_app:dashboard')
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request: HttpRequest) -> HttpResponse:
        """
        Handle GET requests - set role in session and redirect to allauth signup.
        """
        # Set the role in session for the allauth adapter to pick up
        request.session['signup_role'] = self.role
        request.session.save()
        
        # Add a message to inform the user about the account type
        role_name = 'Customer' if self.role == CustomUser.CUSTOMER else 'Service Provider'
        messages.info(
            request, 
            f'You are signing up as a {role_name}. '
            f'You can change your account type later if needed.'
        )
        
        # Redirect to allauth signup
        return redirect(self.success_url_name)
    
    def post(self, request: HttpRequest) -> HttpResponse:
        """Handle POST requests the same as GET for this view"""
        return self.get(request)


class CustomerSignupRedirectView(RoleBasedSignupView):
    """
    Customer signup view that redirects to allauth with customer role preset.
    
    This maintains backward compatibility with existing customer signup URLs
    while leveraging allauth for the actual signup process.
    """
    
    role = CustomUser.CUSTOMER
    template_name = 'accounts_app/customer/signup.html'
    
    def get(self, request: HttpRequest) -> HttpResponse:
        """Set customer role and redirect to allauth signup"""
        # Set specific session data for customer signup
        request.session['signup_role'] = self.role
        request.session['signup_source'] = 'customer_direct'
        request.session.save()
        
        messages.info(
            request,
            'Welcome! You are signing up as a Customer. '
            'You\'ll be able to book venues and services.'
        )
        
        return redirect('account_signup')


class ServiceProviderSignupRedirectView(RoleBasedSignupView):
    """
    Service provider signup view that redirects to allauth with provider role preset.
    
    This maintains backward compatibility with existing provider signup URLs
    while leveraging allauth for the actual signup process.
    """
    
    role = CustomUser.SERVICE_PROVIDER
    template_name = 'accounts_app/provider/signup.html'
    
    def get(self, request: HttpRequest) -> HttpResponse:
        """Set service provider role and redirect to allauth signup"""
        # Set specific session data for service provider signup
        request.session['signup_role'] = self.role
        request.session['signup_source'] = 'provider_direct'
        request.session.save()
        
        messages.info(
            request,
            'Welcome! You are signing up as a Service Provider. '
            'You\'ll be able to list venues and offer services.'
        )
        
        return redirect('account_signup')


@csrf_protect
@never_cache
def unified_login_redirect_view(request: HttpRequest) -> HttpResponse:
    """
    Unified login view that redirects to allauth login.

    This maintains backward compatibility with existing login URLs
    while using allauth for the actual authentication process.

    Features:
    - Handles both GET and POST requests
    - Maintains security measures from custom login views
    - Preserves user feedback and messaging
    - Supports next URL parameter
    - Integrates with existing logging and monitoring
    """
    if request.user.is_authenticated:
        # Use smart redirect for authenticated users
        return redirect('accounts_app:post_auth_redirect')

    # Store the next parameter if provided
    next_url = request.GET.get('next')
    if next_url:
        request.session['login_next_url'] = next_url
        request.session.save()

    # Store source information for analytics
    request.session['login_source'] = 'unified_redirect'
    request.session.save()

    # Add helpful message for users
    if not request.session.get('login_message_shown'):
        messages.info(
            request,
            'Please log in to access your account. '
            'You will be redirected to the appropriate dashboard after login.'
        )
        request.session['login_message_shown'] = True
        request.session.save()

    # Redirect to allauth login
    return redirect('account_login')


@csrf_protect
@never_cache
def enhanced_unified_login_view(request: HttpRequest) -> HttpResponse:
    """
    Enhanced unified login view that can handle both allauth and custom flows.

    This view provides a unified entry point for all login attempts while
    maintaining compatibility with both authentication systems.

    Features:
    - Automatic flow detection (allauth vs custom)
    - Role-based redirect preferences
    - Basic security logging
    - Session management
    """
    if request.user.is_authenticated:
        return redirect('accounts_app:post_auth_redirect')

    # Handle POST requests (form submissions)
    if request.method == 'POST':
        # Check if this is an allauth form submission
        if 'login' in request.POST:
            # This is an allauth login form - let allauth handle it
            # Store additional context for post-login processing
            request.session['login_method'] = 'allauth_unified'
            request.session.save()

            # Redirect to allauth login to process the form
            return redirect('account_login')

        # Handle custom form submissions if needed
        # (This would be for backward compatibility with custom forms)
        else:
            # Basic logging
            logger = logging.getLogger(__name__)
            logger.info(
                f"Enhanced unified login attempt",
                extra={
                    'login_method': 'unified_custom',
                    'event': 'login_attempt'
                }
            )

            # Redirect to allauth for processing
            return redirect('account_login')

    # Handle GET requests
    else:
        # Store context for the login process
        next_url = request.GET.get('next')
        if next_url:
            request.session['login_next_url'] = next_url

        # Store login source
        request.session['login_source'] = 'unified_enhanced'
        request.session.save()

        # Redirect to allauth login
        return redirect('account_login')


class RoleBasedLoginRedirectView(View):
    """
    Base view for role-specific login redirects.
    
    This maintains backward compatibility with role-specific login URLs
    while using allauth for authentication.
    """
    
    role = None  # To be set by subclasses
    dashboard_url = None  # To be set by subclasses
    
    @method_decorator([csrf_protect, never_cache])
    def dispatch(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        """Handle both GET and POST requests"""
        if request.user.is_authenticated:
            # Check if user has the expected role
            if (self.role == CustomUser.CUSTOMER and request.user.is_customer) or \
               (self.role == CustomUser.SERVICE_PROVIDER and request.user.is_service_provider):
                return redirect(self.dashboard_url)
            else:
                # User is authenticated but has different role
                messages.warning(
                    request,
                    'You are already logged in with a different account type.'
                )
                return redirect('dashboard_app:dashboard')
        
        # Store role preference for post-login redirect
        request.session['preferred_role'] = self.role
        request.session.save()
        
        # Store next parameter if provided
        next_url = request.GET.get('next')
        if next_url:
            request.session['login_next_url'] = next_url
            request.session.save()
        
        # Redirect to allauth login
        return redirect('account_login')


class CustomerLoginRedirectView(RoleBasedLoginRedirectView):
    """Customer login redirect view"""
    role = CustomUser.CUSTOMER
    dashboard_url = 'accounts_app:customer_profile'


class ServiceProviderLoginRedirectView(RoleBasedLoginRedirectView):
    """Service provider login redirect view"""
    role = CustomUser.SERVICE_PROVIDER
    dashboard_url = 'accounts_app:service_provider_profile'


@csrf_protect
@never_cache
def post_auth_redirect_view(request: HttpRequest) -> HttpResponse:
    """
    Smart post-authentication redirect view.

    This view handles redirects after successful authentication,
    taking into account user roles, preferences, and intended destinations.
    """
    if not request.user.is_authenticated:
        return redirect('account_login')

    user = request.user

    # Check for specific redirect preferences in session
    preferred_destination = request.session.get('auth_redirect_destination')
    if preferred_destination:
        del request.session['auth_redirect_destination']
        request.session.save()
        return redirect(preferred_destination)

    # Check for next parameter
    next_url = request.GET.get('next')
    if next_url:
        return redirect(next_url)

    # Role-based default redirects
    if user.role == CustomUser.SERVICE_PROVIDER:
        # Check if provider has completed profile setup
        if hasattr(user, 'service_provider_profile'):
            return redirect('dashboard_app:provider_dashboard')
        else:
            # Redirect to profile completion
            messages.info(
                request,
                'Please complete your business profile to access all features.'
            )
            return redirect('accounts_app:service_provider_profile')

    elif user.role == CustomUser.CUSTOMER:
        return redirect('dashboard_app:customer_dashboard')

    elif user.role == CustomUser.ADMIN:
        return redirect('admin_app:admin_dashboard')

    # Fallback redirect
    return redirect('home')


@csrf_protect
@never_cache
def role_switch_redirect_view(request: HttpRequest) -> HttpResponse:
    """
    Handle role-based redirects when users access wrong dashboard sections.

    This view can be used when users try to access dashboard sections
    that don't match their role.
    """
    if not request.user.is_authenticated:
        return redirect('account_login')

    user = request.user

    # Add appropriate message based on role
    if user.role == CustomUser.CUSTOMER:
        messages.info(
            request,
            'You have been redirected to the customer dashboard.'
        )
        return redirect('dashboard_app:customer_dashboard')

    elif user.role == CustomUser.SERVICE_PROVIDER:
        messages.info(
            request,
            'You have been redirected to the service provider dashboard.'
        )
        return redirect('dashboard_app:provider_dashboard')

    elif user.role == CustomUser.ADMIN:
        messages.info(
            request,
            'You have been redirected to the admin dashboard.'
        )
        return redirect('admin_app:admin_dashboard')

    # Fallback
    messages.warning(
        request,
        'Unable to determine appropriate dashboard. Redirecting to home.'
    )
    return redirect('home')


@csrf_protect
@never_cache
def unified_logout_integration_view(request: HttpRequest) -> HttpResponse:
    """
    Unified logout view that integrates allauth logout with custom logout logic.

    This view maintains all existing security measures and logging while
    using allauth for the actual logout process.

    Features:
    - Maintains existing audit logging
    - Preserves user feedback messages
    - Integrates with allauth logout flow
    - Handles role-specific logout logic
    """
    from accounts_app.views.common import MESSAGES
    from accounts_app.logging_utils import log_user_activity, log_authentication_event
    import logging

    logger = logging.getLogger(__name__)

    if not request.user.is_authenticated:
        return redirect('home')

    user = request.user
    user_email = user.email
    user_type = None

    # Role-specific logging (maintaining existing logic)
    if user.is_customer:
        user_type = 'customer'
        log_user_activity(
            activity_type='logout',
            user=user,
            request=request,
            details={'user_type': 'customer', 'logout_method': 'unified_allauth'}
        )
        log_authentication_event(
            event_type='logout',
            user_email=user_email,
            success=True,
            request=request
        )

    elif user.is_service_provider:
        user_type = 'service_provider'
        logger.info(
            f"Service provider logged out: {user_email}",
            extra={
                'user_email': user_email,
                'ip_address': request.META.get('REMOTE_ADDR'),
                'event_type': 'logout',
                'logout_method': 'unified_allauth'
            }
        )

    # Store logout context for allauth
    request.session['logout_user_type'] = user_type
    request.session['logout_user_email'] = user_email
    request.session.save()

    # Add success message (will be shown after redirect)
    messages.success(request, MESSAGES['logout_success'])

    # Audit log
    if user_type:
        logger.info(
            f"User logged out: {user_email} (Type: {user_type})",
            extra={
                'user_email': user_email,
                'user_type': user_type,
                'event': 'logout_success',
                'logout_method': 'unified_allauth'
            }
        )

    # Redirect to allauth logout
    return redirect('account_logout')


class UnifiedAuthenticationMixin:
    """
    Mixin to provide unified authentication functionality.

    This mixin can be used by views that need to work with both
    allauth and custom authentication flows.
    """

    def get_login_url(self):
        """Get the appropriate login URL."""
        return reverse('accounts_app:unified_login_redirect')

    def get_logout_url(self):
        """Get the appropriate logout URL."""
        return reverse('accounts_app:unified_logout_integration')

    def handle_authenticated_user(self, request):
        """Handle already authenticated users."""
        return redirect('accounts_app:post_auth_redirect')

    def store_next_url(self, request):
        """Store the next URL for post-authentication redirect."""
        next_url = request.GET.get('next')
        if next_url:
            request.session['login_next_url'] = next_url
            request.session.save()

    def add_auth_context(self, request, context_type='login'):
        """Add authentication context to session."""
        request.session[f'{context_type}_source'] = 'unified_mixin'
        request.session.save()
