# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth import logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, render
from django.views.decorators.http import require_http_methods

# --- Local App Imports ---
from ...forms import ServiceProviderPasswordChangeForm
from ..common import MESSAGES, logger


# --- Service Provider Security Features ---

@login_required
@require_http_methods(["GET", "POST"])
def service_provider_change_password_view(request):
    """
    Change password for service provider and force logout.

    Features:
    - Password change form validation
    - Automatic logout on success
    - Audit logging
    """
    if not request.user.is_service_provider:
        return redirect('home')

    if request.method == 'POST':
        form = ServiceProviderPasswordChangeForm(
            user=request.user,
            data=request.POST
        )
        if form.is_valid():
            try:
                user_email = request.user.email
                form.save()
                logout(request)
                messages.success(request, MESSAGES['password_change'])
                logger.info(
                    "Password changed for service provider: %s", user_email
                )
                return redirect('accounts_app:service_provider_login')
            except Exception as e:
                logger.error(
                    "Error changing password: %s", e
                )
                messages.error(
                    request,
                    'There was an error changing your password.'
                )
    else:
        form = ServiceProviderPasswordChangeForm(user=request.user)

    return render(
        request,
        'accounts_app/provider/change_password.html',
        {'form': form}
    )



@login_required
@require_http_methods(["GET", "POST"])
def service_provider_deactivate_account_view(request):
    """
    Deactivate service provider account and logout.

    Features:
    - Restrict to POST for deactivation
    - Automatic logout
    - Audit logging
    """
    if not request.user.is_service_provider:
        return redirect('home')

    if request.method == 'GET':
        return redirect('accounts_app:service_provider_profile')

    try:
        user_email = request.user.email
        request.user.is_active = False
        request.user.save()
        logout(request)
        messages.success(request, MESSAGES['account_deactivated'])
        logger.info(
            "Service provider account deactivated: %s", user_email
        )
    except Exception as e:
        logger.error(
            "Error deactivating service provider account: %s", e
        )
        messages.error(
            request,
            'There was an error deactivating your account.'
        )
        return redirect('accounts_app:service_provider_profile')

    return redirect('home')


@login_required
def premium_upgrade(request):
    """Premium upgrade page for service providers."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'This page is only available for service providers.')
        return redirect('utility_app:home')
    
    # Check if user is already premium (accessing is_premium safely)
    is_premium = False
    try:
        is_premium = getattr(request.user.service_provider_profile, 'is_premium', False)
    except AttributeError:
        is_premium = False
    
    context = {
        'current_plan': 'Premium' if is_premium else 'Basic',
        'premium_features': [
            {'feature': 'Up to 10 venue images', 'basic': '5 images', 'premium': '10 images'},
            {'feature': 'Priority customer support', 'basic': 'Standard support', 'premium': 'Priority support'},
            {'feature': 'Advanced analytics', 'basic': 'Basic stats', 'premium': 'Detailed analytics'},
            {'feature': 'Featured listings', 'basic': 'Standard listing', 'premium': 'Featured placement'},
            {'feature': 'Custom branding', 'basic': 'Standard branding', 'premium': 'Custom colors & logo'},
        ],
        'pricing': {
            'monthly': 29.99,
            'yearly': 299.99,
            'yearly_savings': 60.00
        }
    }
    
    return render(request, 'accounts_app/premium_upgrade.html', context)
