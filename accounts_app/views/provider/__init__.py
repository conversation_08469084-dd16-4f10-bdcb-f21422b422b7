# Provider views package
# This module maintains backward compatibility by importing all provider views

# Import all views for backward compatibility

# Authentication views
from .auth import (
    ServiceProviderSignupView,
    provider_signup_done_view,
    provider_email_verify_view,
    service_provider_login_view,
    service_provider_logout_view,
    ServiceProviderPasswordResetView,
    ServiceProviderPasswordResetDoneView,
    ServiceProviderPasswordResetConfirmView,
    ServiceProviderPasswordResetCompleteView,
)

# Profile management views
from .profile import (
    ServiceProviderProfileView,
    ServiceProviderProfileEditView,
)

# Account management views
from .account import (
    service_provider_change_password_view,
    service_provider_deactivate_account_view,
    premium_upgrade,
)

# Explicit __all__ for clarity
__all__ = [
    # Authentication views
    'ServiceProviderSignupView',
    'provider_signup_done_view',
    'provider_email_verify_view',
    'service_provider_login_view',
    'service_provider_logout_view',
    'ServiceProviderPasswordResetView',
    'ServiceProviderPasswordResetDoneView',
    'ServiceProviderPasswordResetConfirmView',
    'ServiceProviderPasswordResetCompleteView',

    # Profile management views
    'ServiceProviderProfileView',
    'ServiceProviderProfileEditView',

    # Account management views
    'service_provider_change_password_view',
    'service_provider_deactivate_account_view',
    'premium_upgrade',
]
