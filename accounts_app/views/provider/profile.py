# --- Third-Party Imports ---
from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.views.generic import DetailView, UpdateView

# --- Local App Imports ---
from ...forms import ServiceProviderProfileForm
from ...models import ServiceProviderProfile, TeamMember
from ..common import logger


# --- Service Provider Profile Management ---

class ServiceProviderProfileView(DetailView):
    """
    Display service provider profile details.

    Features:
    - Prefetch related team members for efficiency
    - Automatic profile creation with defaults
    - Access restricted to authenticated service providers
    """
    model = ServiceProviderProfile
    template_name = 'accounts_app/provider/profile.html'
    context_object_name = 'profile'

    def dispatch(self, request, *args, **kwargs):
        """Ensure only authenticated service providers can access."""
        if not request.user.is_authenticated or not request.user.is_service_provider:
            return redirect('accounts_app:service_provider_login')
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """Optimize queries by prefetching team members."""
        return ServiceProviderProfile.objects.prefetch_related('team')

    def get_object(self, queryset=None):
        """
        Retrieve or create the profile for the current user with default values.
        """
        profile, _ = ServiceProviderProfile.objects.prefetch_related('team').get_or_create(
            user=self.request.user,
            defaults={
                'legal_name': 'Your Business Name',
                'phone': '',
                'address': '',
                'city': '',
                'state': 'CA',
                'zip_code': '',
            }
        )
        return profile

    def get_context_data(self, **kwargs):
        """Add team metrics to context."""
        context = super().get_context_data(**kwargs)
        context['team_members'] = self.object.team.all()
        context['team_count'] = self.object.team.count()
        context['max_team_members'] = TeamMember.max_count()
        
        # Add venue context for sidebar
        context['venue'] = getattr(self.object, 'venue', None)
        
        return context



class ServiceProviderProfileEditView(UpdateView):
    """
    Edit service provider profile.

    Features:
    - Prefetch team members for performance
    - Success/failure messaging with audit logging
    """
    model = ServiceProviderProfile
    form_class = ServiceProviderProfileForm
    template_name = 'accounts_app/provider/profile_edit.html'
    success_url = reverse_lazy('accounts_app:service_provider_profile')

    def dispatch(self, request, *args, **kwargs):
        """Restrict access to authenticated service providers."""
        if not request.user.is_authenticated or not request.user.is_service_provider:
            return redirect('accounts_app:service_provider_login')
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """Optimize queries by prefetching team members."""
        return ServiceProviderProfile.objects.prefetch_related('team')

    def get_object(self, queryset=None):
        """
        Retrieve or create the profile for the current user with default values.
        """
        profile, _ = ServiceProviderProfile.objects.prefetch_related('team').get_or_create(
            user=self.request.user,
            defaults={
                'legal_name': 'Your Business Name',
                'phone': '',
                'address': '',
                'city': '',
                'state': 'CA',
                'zip_code': '',
            }
        )
        return profile

    def get_context_data(self, **kwargs):
        """Add venue context for sidebar."""
        context = super().get_context_data(**kwargs)
        context['venue'] = getattr(self.object, 'venue', None)
        return context

    def form_valid(self, form):
        """
        Handle valid form submissions with success and error feedback.
        """
        try:
            # Call parent form_valid first to save the form
            response = super().form_valid(form)

            # Add success message after successful save
            from ..common import MESSAGES
            messages.success(self.request, MESSAGES['business_profile_update'])
            logger.info(
                "Service provider profile updated: %s",
                self.request.user.email
            )
            return response

        except Exception as e:
            logger.error(
                "Error updating service provider profile: %s", e
            )
            messages.error(
                self.request,
                'There was an error updating your profile.'
            )
            return self.form_invalid(form)
