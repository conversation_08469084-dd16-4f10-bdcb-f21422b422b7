# This file has been refactored into a directory structure.
# Import all views from the customer directory for backward compatibility.

from .customer.auth import (
    CustomerSignupView,
    customer_login_view,
    customer_logout_view,
    unified_logout_view,
    CustomerPasswordResetView,
    CustomerPasswordResetDoneView,
    CustomerPasswordResetConfirmView,
    CustomerPasswordResetCompleteView,
)
from .customer.profile import (
    CustomerProfileView,
    CustomerProfileEditView,
)
from .customer.account import (
    customer_change_password_view,
    customer_deactivate_account_view,
)

# Maintain backward compatibility by exposing all views at the module level
__all__ = [
    'CustomerSignupView',
    'customer_login_view',
    'customer_logout_view',
    'unified_logout_view',
    'CustomerPasswordResetView',
    'CustomerPasswordResetDoneView',
    'CustomerPasswordResetConfirmView',
    'CustomerPasswordResetCompleteView',
    'CustomerProfileView',
    'CustomerProfileEditView',
    'customer_change_password_view',
    'customer_deactivate_account_view',
]
