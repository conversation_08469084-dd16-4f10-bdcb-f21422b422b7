# --- Django Imports ---
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.conf import settings
from django.core.exceptions import ValidationError
import logging

# --- Third-Party Imports ---
from allauth.socialaccount.models import SocialAccount, SocialApp
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from allauth.socialaccount import app_settings as socialaccount_settings
try:
    from django_ratelimit.decorators import ratelimit
except ImportError:
    def ratelimit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

# --- Local App Imports ---
from ..models import CustomUser
from ..forms.social_account import (
    SocialAccountLinkForm,
    SocialAccountConflictResolutionForm,
)

logger = logging.getLogger(__name__)


@method_decorator([
    login_required,
    csrf_protect,
], name='dispatch')
class SocialAccountManagementView(TemplateView):
    """
    View for managing connected social accounts.
    
    Features:
    - View connected social accounts
    - Link new social accounts
    - Unlink existing social accounts
    - Account data synchronization
    """
    template_name = 'accounts/social_account_management.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get connected social accounts
        social_accounts = SocialAccount.objects.filter(user=user)
        
        # Get available social providers
        available_providers = []
        for provider_id, provider_config in socialaccount_settings.PROVIDERS.items():
            try:
                social_app = SocialApp.objects.get(provider=provider_id)
                
                # Check if user already has this provider connected
                is_connected = social_accounts.filter(provider=provider_id).exists()
                
                available_providers.append({
                    'id': provider_id,
                    'name': provider_config.get('name', provider_id.title()),
                    'is_connected': is_connected,
                    'app': social_app,
                })
            except SocialApp.DoesNotExist:
                continue
        
        # Get account conflicts (if any)
        conflicts = self._check_account_conflicts(user)
        
        context.update({
            'social_accounts': social_accounts,
            'available_providers': available_providers,
            'conflicts': conflicts,
            'can_disconnect': social_accounts.count() > 0 and user.has_usable_password(),
        })
        
        return context
    
    def _check_account_conflicts(self, user):
        """Check for potential account conflicts."""
        conflicts = []
        
        # Check for duplicate emails across social accounts
        social_accounts = SocialAccount.objects.filter(user=user)
        
        for account in social_accounts:
            extra_data = account.extra_data
            social_email = extra_data.get('email')
            
            if social_email and social_email != user.email:
                # Check if another user has this email
                try:
                    other_user = CustomUser.objects.get(email=social_email)
                    if other_user != user:
                        conflicts.append({
                            'type': 'email_conflict',
                            'provider': account.provider,
                            'social_email': social_email,
                            'other_user': other_user,
                            'account': account,
                        })
                except CustomUser.DoesNotExist:
                    pass
        
        return conflicts


@method_decorator([
    login_required,
    csrf_protect,
    ratelimit(key='user', rate='10/5m', method='POST', block=True)
], name='dispatch')
class UnlinkSocialAccountView(View):
    """
    View for unlinking social accounts.
    
    Features:
    - Secure unlinking with validation
    - Prevent unlinking if it would lock user out
    - Security logging
    """
    
    def post(self, request):
        account_id = request.POST.get('account_id')
        
        if not account_id:
            return JsonResponse({
                'success': False,
                'message': 'Invalid account ID.'
            })
        
        try:
            # Get the social account
            social_account = get_object_or_404(
                SocialAccount,
                id=account_id,
                user=request.user
            )
            
            # Check if user can safely unlink this account
            can_unlink, reason = self._can_unlink_account(request.user, social_account)
            
            if not can_unlink:
                return JsonResponse({
                    'success': False,
                    'message': reason
                })
            
            # Unlink the account
            provider_name = social_account.get_provider().name
            social_account.delete()
            
            # Log the unlinking
            logger.info(
                "Social account unlinked",
                extra={
                    'user_id': request.user.id,
                    'provider': social_account.provider,
                    'social_account_id': account_id,
                    'event': 'social_account_unlinked'
                }
            )
            
            return JsonResponse({
                'success': True,
                'message': f'{provider_name} account unlinked successfully.'
            })
            
        except SocialAccount.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Social account not found.'
            })
        except Exception as e:
            logger.error(
                "Error unlinking social account",
                extra={
                    'user_id': request.user.id,
                    'account_id': account_id,
                    'error': str(e),
                    'event': 'social_account_unlink_error'
                }
            )
            
            return JsonResponse({
                'success': False,
                'message': 'An error occurred while unlinking the account.'
            })
    
    def _can_unlink_account(self, user, social_account):
        """
        Check if user can safely unlink a social account.
        
        Returns:
            tuple: (can_unlink: bool, reason: str)
        """
        # Check if user has a usable password
        if not user.has_usable_password():
            # Check if this is the only social account
            other_social_accounts = SocialAccount.objects.filter(
                user=user
            ).exclude(id=social_account.id)
            
            if not other_social_accounts.exists():
                return False, (
                    "Cannot unlink this account because you don't have a password set "
                    "and this is your only way to sign in. Please set a password first."
                )
        
        return True, ""


@method_decorator([
    login_required,
    csrf_protect,
    ratelimit(key='user', rate='5/5m', method='POST', block=True)
], name='dispatch')
class SyncSocialAccountDataView(View):
    """
    View for synchronizing data from social accounts.
    
    Features:
    - Sync profile information
    - Update profile picture
    - Merge contact information
    """
    
    def post(self, request):
        account_id = request.POST.get('account_id')
        sync_fields = request.POST.getlist('sync_fields')
        
        if not account_id:
            return JsonResponse({
                'success': False,
                'message': 'Invalid account ID.'
            })
        
        try:
            # Get the social account
            social_account = get_object_or_404(
                SocialAccount,
                id=account_id,
                user=request.user
            )
            
            # Sync the requested fields
            updated_fields = self._sync_account_data(
                request.user,
                social_account,
                sync_fields
            )
            
            if updated_fields:
                # Log the sync
                logger.info(
                    "Social account data synced",
                    extra={
                        'user_id': request.user.id,
                        'provider': social_account.provider,
                        'synced_fields': updated_fields,
                        'event': 'social_account_data_synced'
                    }
                )
                
                return JsonResponse({
                    'success': True,
                    'message': f'Synced {len(updated_fields)} fields from {social_account.get_provider().name}.',
                    'updated_fields': updated_fields
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'No fields were updated.'
                })
                
        except SocialAccount.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'Social account not found.'
            })
        except Exception as e:
            logger.error(
                "Error syncing social account data",
                extra={
                    'user_id': request.user.id,
                    'account_id': account_id,
                    'error': str(e),
                    'event': 'social_account_sync_error'
                }
            )
            
            return JsonResponse({
                'success': False,
                'message': 'An error occurred while syncing account data.'
            })
    
    def _sync_account_data(self, user, social_account, sync_fields):
        """
        Sync data from social account to user profile.
        
        Returns:
            list: Updated field names
        """
        updated_fields = []
        extra_data = social_account.extra_data
        
        # Map of sync fields to social data fields
        field_mapping = {
            'first_name': ['given_name', 'first_name'],
            'last_name': ['family_name', 'last_name'],
            'email': ['email'],
            'profile_picture': ['picture', 'avatar_url'],
        }
        
        for field in sync_fields:
            if field not in field_mapping:
                continue
            
            # Get value from social account data
            social_value = None
            for social_field in field_mapping[field]:
                if social_field in extra_data:
                    social_value = extra_data[social_field]
                    break
            
            if not social_value:
                continue
            
            # Update user field if different
            if field == 'email':
                # Special handling for email - don't auto-update
                continue
            elif field == 'profile_picture':
                # Handle profile picture sync
                if hasattr(user, 'profile') and hasattr(user.profile, 'avatar_url'):
                    if not user.profile.avatar_url or user.profile.avatar_url != social_value:
                        user.profile.avatar_url = social_value
                        user.profile.save()
                        updated_fields.append('profile_picture')
            else:
                # Handle regular fields
                current_value = getattr(user, field, '')
                if not current_value or current_value != social_value:
                    setattr(user, field, social_value)
                    updated_fields.append(field)
        
        # Save user if any fields were updated
        if updated_fields:
            user.save()
        
        return updated_fields


@method_decorator([
    login_required,
    csrf_protect,
], name='dispatch')
class SocialAccountConflictResolutionView(TemplateView):
    """
    View for resolving social account conflicts.
    
    Features:
    - Resolve email conflicts
    - Merge account data
    - Handle duplicate accounts
    """
    template_name = 'accounts/social_account_conflict_resolution.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        conflict_id = self.kwargs.get('conflict_id')
        conflict_type = self.request.GET.get('type', 'email_conflict')
        
        # Get conflict details based on type
        if conflict_type == 'email_conflict':
            context.update(self._get_email_conflict_context(conflict_id))
        
        context['conflict_type'] = conflict_type
        return context
    
    def _get_email_conflict_context(self, conflict_id):
        """Get context for email conflict resolution."""
        try:
            social_account = get_object_or_404(
                SocialAccount,
                id=conflict_id,
                user=self.request.user
            )
            
            extra_data = social_account.extra_data
            social_email = extra_data.get('email')
            
            # Find conflicting user
            conflicting_user = None
            if social_email:
                try:
                    conflicting_user = CustomUser.objects.get(email=social_email)
                except CustomUser.DoesNotExist:
                    pass
            
            return {
                'social_account': social_account,
                'social_email': social_email,
                'conflicting_user': conflicting_user,
            }
            
        except SocialAccount.DoesNotExist:
            return {'error': 'Social account not found.'}
    
    def post(self, request, *args, **kwargs):
        """Handle conflict resolution."""
        action = request.POST.get('action')
        conflict_id = kwargs.get('conflict_id')
        
        if action == 'ignore_conflict':
            return self._ignore_conflict(request, conflict_id)
        elif action == 'update_email':
            return self._update_user_email(request, conflict_id)
        elif action == 'merge_accounts':
            return self._merge_accounts(request, conflict_id)
        else:
            messages.error(request, 'Invalid action.')
            return redirect('accounts_app:social_account_management')
    
    def _ignore_conflict(self, request, conflict_id):
        """Ignore the conflict and keep current setup."""
        messages.info(request, 'Conflict ignored. No changes were made.')
        return redirect('accounts_app:social_account_management')
    
    def _update_user_email(self, request, conflict_id):
        """Update user's email to match social account."""
        try:
            social_account = get_object_or_404(
                SocialAccount,
                id=conflict_id,
                user=request.user
            )
            
            social_email = social_account.extra_data.get('email')
            if social_email and social_email != request.user.email:
                # Check if email is available
                if CustomUser.objects.filter(email=social_email).exists():
                    messages.error(
                        request,
                        'Cannot update email because another account already uses this email address.'
                    )
                else:
                    request.user.email = social_email
                    request.user.save()
                    messages.success(
                        request,
                        f'Your email has been updated to {social_email}.'
                    )
            
        except Exception as e:
            logger.error(f"Error updating user email: {e}")
            messages.error(request, 'An error occurred while updating your email.')
        
        return redirect('accounts_app:social_account_management')
    
    def _merge_accounts(self, request, conflict_id):
        """Merge conflicting accounts (placeholder for complex logic)."""
        messages.warning(
            request,
            'Account merging is not yet implemented. Please contact support for assistance.'
        )
        return redirect('accounts_app:social_account_management')
