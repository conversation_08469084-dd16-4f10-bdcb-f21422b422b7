# --- Django Imports ---
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.utils import timezone
import logging

# --- Third-Party Imports ---
try:
    from django_ratelimit.decorators import ratelimit
except ImportError:
    def ratelimit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

# --- Local App Imports ---
from ..models.session_management import UserSession, SessionSecurityEvent
from ..utils.session_utils import (
    extend_session,
    terminate_session,
    get_user_sessions,
)

logger = logging.getLogger(__name__)


@method_decorator([
    login_required,
    csrf_protect,
], name='dispatch')
class SessionManagementView(TemplateView):
    """
    View for managing user sessions.
    
    Features:
    - View active sessions
    - Terminate sessions
    - Session details and security info
    """
    template_name = 'accounts/session_management.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get active sessions
        active_sessions = get_user_sessions(user, active_only=True)
        
        # Get recent security events
        recent_events = SessionSecurityEvent.objects.filter(
            user=user
        ).order_by('-created_at')[:20]
        
        # Get current session
        current_session_key = self.request.session.session_key
        current_session = None
        if current_session_key:
            current_session = active_sessions.filter(
                session_key=current_session_key
            ).first()
        
        context.update({
            'active_sessions': active_sessions,
            'recent_events': recent_events,
            'current_session': current_session,
            'current_session_key': current_session_key,
        })
        
        return context


@method_decorator([
    login_required,
    csrf_protect,
    ratelimit(key='user', rate='10/5m', method='POST', block=True)
], name='dispatch')
class TerminateSessionView(View):
    """
    View for terminating specific sessions.
    
    Features:
    - Terminate individual sessions
    - Terminate all other sessions
    - Security logging
    """
    
    def post(self, request):
        session_id = request.POST.get('session_id')
        action = request.POST.get('action')
        
        if action == 'terminate_all_others':
            return self._terminate_all_other_sessions(request)
        elif session_id:
            return self._terminate_single_session(request, session_id)
        else:
            return JsonResponse({
                'success': False,
                'message': 'Invalid request parameters.'
            })
    
    def _terminate_single_session(self, request, session_id):
        """Terminate a single session."""
        try:
            # Find the session
            user_session = UserSession.objects.filter(
                session_id=session_id,
                user=request.user,
                is_active=True
            ).first()
            
            if not user_session:
                return JsonResponse({
                    'success': False,
                    'message': 'Session not found or already terminated.'
                })
            
            # Don't allow terminating current session this way
            current_session_key = request.session.session_key
            if user_session.session_key == current_session_key:
                return JsonResponse({
                    'success': False,
                    'message': 'Cannot terminate your current session. Use logout instead.'
                })
            
            # Terminate the session
            success = terminate_session(user_session.session_key, 'user_request')
            
            if success:
                # Log the termination
                SessionSecurityEvent.log_event(
                    'security_termination',
                    user=request.user,
                    session=user_session,
                    request=request,
                    reason='user_terminated_session'
                )
                
                return JsonResponse({
                    'success': True,
                    'message': 'Session terminated successfully.'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Failed to terminate session.'
                })
                
        except Exception as e:
            logger.error(
                "Error terminating session",
                extra={
                    'user_id': request.user.id,
                    'session_id': session_id,
                    'error': str(e),
                    'event': 'session_termination_error'
                }
            )
            
            return JsonResponse({
                'success': False,
                'message': 'An error occurred while terminating the session.'
            })
    
    def _terminate_all_other_sessions(self, request):
        """Terminate all sessions except the current one."""
        try:
            current_session_key = request.session.session_key
            
            # Get all other active sessions
            other_sessions = UserSession.objects.filter(
                user=request.user,
                is_active=True
            ).exclude(session_key=current_session_key)
            
            terminated_count = 0
            
            for session in other_sessions:
                if terminate_session(session.session_key, 'user_request'):
                    terminated_count += 1
                    
                    # Log each termination
                    SessionSecurityEvent.log_event(
                        'security_termination',
                        user=request.user,
                        session=session,
                        request=request,
                        reason='user_terminated_all_others'
                    )
            
            return JsonResponse({
                'success': True,
                'message': f'Terminated {terminated_count} other sessions.',
                'terminated_count': terminated_count
            })
            
        except Exception as e:
            logger.error(
                "Error terminating all other sessions",
                extra={
                    'user_id': request.user.id,
                    'error': str(e),
                    'event': 'bulk_session_termination_error'
                }
            )
            
            return JsonResponse({
                'success': False,
                'message': 'An error occurred while terminating sessions.'
            })


@require_http_methods(["POST"])
@login_required
@csrf_protect
@ratelimit(key='user', rate='5/5m', method='POST', block=True)
def extend_session_view(request):
    """
    AJAX endpoint to extend current session.
    
    Returns JSON response with success status.
    """
    try:
        hours = request.POST.get('hours')
        if hours:
            try:
                hours = int(hours)
                if hours < 1 or hours > 24:
                    hours = None
            except ValueError:
                hours = None
        
        success = extend_session(request, hours)
        
        if success:
            user_session = getattr(request, 'user_session', None)
            time_until_expiry = None
            
            if user_session:
                expiry_delta = user_session.time_until_expiry
                if expiry_delta:
                    time_until_expiry = int(expiry_delta.total_seconds())
            
            return JsonResponse({
                'success': True,
                'message': 'Session extended successfully.',
                'expires_in': time_until_expiry
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Failed to extend session.'
            })
            
    except Exception as e:
        logger.error(
            "Error extending session",
            extra={
                'user_id': request.user.id,
                'error': str(e),
                'event': 'session_extension_error'
            }
        )
        
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while extending the session.'
        })


@require_http_methods(["GET"])
@login_required
def session_status_ajax(request):
    """
    AJAX endpoint to check session status.
    
    Returns JSON with session information.
    """
    try:
        user_session = getattr(request, 'user_session', None)
        
        if user_session:
            time_until_expiry = user_session.time_until_expiry
            expires_in = int(time_until_expiry.total_seconds()) if time_until_expiry else None
            
            return JsonResponse({
                'authenticated': True,
                'expires_in': expires_in,
                'is_remember_me': user_session.is_remember_me,
                'session_id': str(user_session.session_id),
                'last_activity': user_session.last_activity.isoformat(),
            })
        else:
            return JsonResponse({
                'authenticated': request.user.is_authenticated,
                'expires_in': None,
                'is_remember_me': False,
            })
            
    except Exception as e:
        logger.error(
            "Error checking session status",
            extra={
                'user_id': request.user.id if request.user.is_authenticated else None,
                'error': str(e),
                'event': 'session_status_error'
            }
        )
        
        return JsonResponse({
            'authenticated': request.user.is_authenticated,
            'error': 'Failed to get session status'
        })


@method_decorator([
    login_required,
    csrf_protect,
], name='dispatch')
class SessionSecurityView(TemplateView):
    """
    View for session security information and settings.
    
    Features:
    - Security event history
    - Session security settings
    - Suspicious activity alerts
    """
    template_name = 'accounts/session_security.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get security events
        security_events = SessionSecurityEvent.objects.filter(
            user=user
        ).order_by('-created_at')[:50]
        
        # Get suspicious events
        suspicious_events = security_events.filter(is_suspicious=True)[:10]
        
        # Get login statistics
        login_events = security_events.filter(
            event_type__in=['login_success', 'login_failed']
        )
        
        successful_logins = login_events.filter(event_type='login_success').count()
        failed_logins = login_events.filter(event_type='login_failed').count()
        
        context.update({
            'security_events': security_events,
            'suspicious_events': suspicious_events,
            'successful_logins': successful_logins,
            'failed_logins': failed_logins,
            'total_sessions': UserSession.objects.filter(user=user).count(),
            'active_sessions': UserSession.objects.filter(user=user, is_active=True).count(),
        })
        
        return context
