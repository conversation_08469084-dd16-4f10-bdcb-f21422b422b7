# --- Django Imports ---
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings
import uuid

User = get_user_model()


class UserSession(models.Model):
    """
    Track user sessions for enhanced security and management.
    
    Features:
    - Multiple device session tracking
    - Session metadata (IP, user agent, location)
    - Remember me functionality
    - Session timeout management
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='user_sessions'
    )
    
    session_key = models.CharField(
        max_length=40,
        unique=True,
        help_text="Django session key"
    )
    
    session_id = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        help_text="Unique session identifier"
    )
    
    device_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="User-friendly device name"
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the session"
    )
    
    user_agent = models.TextField(
        blank=True,
        help_text="Browser user agent string"
    )
    
    location = models.CharField(
        max_length=200,
        blank=True,
        help_text="Approximate location (city, country)"
    )
    
    is_remember_me = models.BooleanField(
        default=False,
        help_text="Whether this session uses 'remember me' functionality"
    )
    
    is_active = models.BooleanField(
        default=True,
        help_text="Whether the session is currently active"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When the session was created"
    )
    
    last_activity = models.DateTimeField(
        auto_now=True,
        help_text="Last activity timestamp"
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the session expires"
    )
    
    terminated_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the session was terminated"
    )
    
    termination_reason = models.CharField(
        max_length=50,
        blank=True,
        choices=[
            ('logout', 'User logout'),
            ('timeout', 'Session timeout'),
            ('security', 'Security termination'),
            ('admin', 'Admin termination'),
            ('expired', 'Natural expiration'),
        ],
        help_text="Reason for session termination"
    )
    
    class Meta:
        db_table = 'accounts_user_sessions'
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'
        ordering = ['-last_activity']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
            models.Index(fields=['last_activity']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.device_name or 'Unknown Device'}"
    
    @property
    def is_expired(self):
        """Check if the session has expired."""
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at
    
    @property
    def time_until_expiry(self):
        """Get time until session expires."""
        if not self.expires_at:
            return None
        delta = self.expires_at - timezone.now()
        return delta if delta.total_seconds() > 0 else None
    
    @property
    def duration(self):
        """Get session duration."""
        end_time = self.terminated_at or timezone.now()
        return end_time - self.created_at
    
    def terminate(self, reason='logout'):
        """Terminate the session."""
        self.is_active = False
        self.terminated_at = timezone.now()
        self.termination_reason = reason
        self.save(update_fields=['is_active', 'terminated_at', 'termination_reason'])
    
    def extend_expiry(self, hours=None):
        """Extend session expiry time."""
        if hours is None:
            hours = 24 if self.is_remember_me else 8
        
        self.expires_at = timezone.now() + timezone.timedelta(hours=hours)
        self.save(update_fields=['expires_at'])
    
    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])
    
    @classmethod
    def cleanup_expired_sessions(cls):
        """Clean up expired sessions."""
        expired_sessions = cls.objects.filter(
            is_active=True,
            expires_at__lt=timezone.now()
        )
        
        count = expired_sessions.count()
        expired_sessions.update(
            is_active=False,
            terminated_at=timezone.now(),
            termination_reason='expired'
        )
        
        return count
    
    @classmethod
    def get_active_sessions_for_user(cls, user):
        """Get all active sessions for a user."""
        return cls.objects.filter(
            user=user,
            is_active=True
        ).exclude(
            expires_at__lt=timezone.now()
        )


class SessionSecurityEvent(models.Model):
    """
    Track security events related to sessions.
    
    Features:
    - Login attempts tracking
    - Suspicious activity detection
    - Security alerts
    """
    
    EVENT_TYPES = [
        ('login_success', 'Successful Login'),
        ('login_failed', 'Failed Login'),
        ('logout', 'User Logout'),
        ('session_timeout', 'Session Timeout'),
        ('concurrent_login', 'Concurrent Login Detected'),
        ('suspicious_location', 'Suspicious Location'),
        ('password_change', 'Password Changed'),
        ('security_termination', 'Security Termination'),
    ]
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='security_events',
        null=True,
        blank=True,
        help_text="User associated with the event (if any)"
    )
    
    session = models.ForeignKey(
        UserSession,
        on_delete=models.CASCADE,
        related_name='security_events',
        null=True,
        blank=True,
        help_text="Session associated with the event (if any)"
    )
    
    event_type = models.CharField(
        max_length=50,
        choices=EVENT_TYPES,
        help_text="Type of security event"
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address where event occurred"
    )
    
    user_agent = models.TextField(
        blank=True,
        help_text="Browser user agent string"
    )
    
    location = models.CharField(
        max_length=200,
        blank=True,
        help_text="Approximate location"
    )
    
    details = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional event details"
    )
    
    risk_score = models.IntegerField(
        default=0,
        help_text="Risk score (0-100)"
    )
    
    is_suspicious = models.BooleanField(
        default=False,
        help_text="Whether this event is flagged as suspicious"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When the event occurred"
    )
    
    class Meta:
        db_table = 'accounts_session_security_events'
        verbose_name = 'Session Security Event'
        verbose_name_plural = 'Session Security Events'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'event_type']),
            models.Index(fields=['ip_address']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_suspicious']),
        ]
    
    def __str__(self):
        user_email = self.user.email if self.user else 'Anonymous'
        return f"{user_email} - {self.get_event_type_display()}"
    
    @classmethod
    def log_event(cls, event_type, user=None, session=None, request=None, **kwargs):
        """Log a security event."""
        event_data = {
            'event_type': event_type,
            'user': user,
            'session': session,
        }
        
        if request:
            event_data.update({
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        # Add any additional details
        if kwargs:
            event_data['details'] = kwargs
        
        return cls.objects.create(**event_data)
