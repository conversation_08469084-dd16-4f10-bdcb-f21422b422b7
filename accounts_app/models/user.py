# --- Standard Library Imports ---
from typing import Optional

# --- Django Imports ---
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


# --- Custom User Management ---

class CustomUserManager(BaseUserManager):
    """
    Custom user manager using email as unique identifier instead of username.
    Fully compatible with django-allauth and provides enhanced user creation methods.
    """

    def normalize_email(self, email: str) -> str:
        """
        Normalize the email address by lowercasing the domain part.

        :param email: Email address to normalize
        :return: Normalized email address
        """
        if not email:
            return email
        return super().normalize_email(email)

    def create_user(self, email: str, password: str = None, **extra_fields) -> 'CustomUser':
        """
        Create and save a regular user with the given email and password.

        :param email: User's email address
        :param password: User's password
        :param extra_fields: Additional user fields
        :return: CustomUser instance
        :raises ValueError: If email is not provided
        """
        if not email:
            raise ValueError(_('The Email field must be set'))

        # Normalize email and set defaults
        email = self.normalize_email(email)
        extra_fields.setdefault('is_active', True)

        # Create user instance
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email: str, password: str = None, **extra_fields) -> 'CustomUser':
        """
        Create and save a superuser with the given email and password.

        :param email: Superuser's email address
        :param password: Superuser's password
        :param extra_fields: Additional superuser fields
        :return: CustomUser instance
        :raises ValueError: If required superuser flags are missing
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('role', 'admin')  # Set admin role for superusers

        if not extra_fields.get('is_staff'):
            raise ValueError(_('Superuser must have is_staff=True.'))
        if not extra_fields.get('is_superuser'):
            raise ValueError(_('Superuser must have is_superuser=True.'))

        return self.create_user(email, password, **extra_fields)

    def get_by_natural_key(self, username: str) -> 'CustomUser':
        """
        Get user by natural key (email in our case).
        Required for django-allauth compatibility.

        :param username: Email address (our natural key)
        :return: CustomUser instance
        """
        return self.get(**{self.model.USERNAME_FIELD: username})


class CustomUser(AbstractUser):
    """
    Custom authentication model using email as unique identifier with role-based access.
    Fully compatible with django-allauth and provides enhanced user management.
    """
    # Role constants and choices
    ROLES = (
        ('customer', _('Customer')),
        ('service_provider', _('Service Provider')),
        ('admin', _('Admin')),
    )
    CUSTOMER, SERVICE_PROVIDER, ADMIN = ROLES[0][0], ROLES[1][0], ROLES[2][0]

    # Authentication configuration
    username = None  # Remove username field
    email = models.EmailField(
        _('email address'),
        unique=True,
        error_messages={
            'unique': _('A user with this email already exists.')
        },
        help_text=_('Required. Enter a valid email address.')
    )
    role = models.CharField(
        _('role'),
        max_length=20,
        choices=ROLES,
        default=CUSTOMER,
        help_text=_('User role determines access permissions.')
    )
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

    # Django and allauth configuration
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    EMAIL_FIELD = 'email'  # Explicitly set for allauth compatibility
    objects = CustomUserManager()

    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['-date_joined']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['role']),
        ]

    def __str__(self) -> str:
        """String representation of the user."""
        return self.email

    def clean(self) -> None:
        """Validate the model instance."""
        super().clean()
        if self.email:
            self.email = self.email.lower()

    def save(self, *args, **kwargs) -> None:
        """Save the user instance with email normalization."""
        if self.email:
            self.email = self.email.lower()
        super().save(*args, **kwargs)

    def get_full_name(self) -> str:
        """
        Return the first_name plus the last_name, with a space in between.
        Required by Django's AbstractUser and used by allauth.
        """
        full_name = f'{self.first_name} {self.last_name}'.strip()
        return full_name or self.email

    def get_short_name(self) -> str:
        """
        Return the short name for the user.
        Required by Django's AbstractUser and used by allauth.
        """
        return self.first_name or self.email.split('@')[0]

    def get_username(self) -> str:
        """
        Return the username for this User.
        Since we use email as username, return email.
        """
        return getattr(self, self.USERNAME_FIELD)

    def natural_key(self) -> tuple:
        """
        Return the natural key for this user (email).
        Used by Django's serialization framework and allauth.
        """
        return (self.get_username(),)

    def email_user(self, subject: str, message: str, from_email: Optional[str] = None, **kwargs) -> None:
        """
        Send an email to this user.
        Enhanced version that's compatible with allauth email sending.
        """
        from django.core.mail import send_mail
        send_mail(subject, message, from_email, [self.email], **kwargs)

    # Convenience properties for backward compatibility
    @property
    def full_name(self) -> str:
        """Backward compatibility property."""
        return self.get_full_name()

    @property
    def short_name(self) -> str:
        """Backward compatibility property."""
        return self.get_short_name()

    # Dynamic role properties
    def _role_property(role_value):
        @property
        def prop(self):
            return self.role == role_value
        return prop

    # Role properties
    is_customer = _role_property(CUSTOMER)
    is_service_provider = _role_property(SERVICE_PROVIDER)
    is_admin = _role_property(ADMIN)

    # Additional role methods for enhanced functionality
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role."""
        return self.role == role

    def set_role(self, role: str) -> None:
        """Set user role with validation."""
        if role not in [choice[0] for choice in self.ROLES]:
            raise ValidationError(f"Invalid role: {role}")
        self.role = role

    def get_role_display_name(self) -> str:
        """Get the display name for the current role."""
        return dict(self.ROLES).get(self.role, self.role)
