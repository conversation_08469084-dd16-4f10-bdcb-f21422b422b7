# --- Django Imports ---
from django.utils import timezone
from django.conf import settings
import re
import requests
from datetime import timedelta
from user_agents import parse
import logging

logger = logging.getLogger(__name__)


def get_device_info(request):
    """
    Extract device information from request.
    
    Returns:
        dict: Device information including name, type, browser, OS
    """
    user_agent_string = request.META.get('HTTP_USER_AGENT', '')
    
    try:
        user_agent = parse(user_agent_string)
        
        # Generate device name
        device_name = f"{user_agent.browser.family}"
        if user_agent.browser.version_string:
            device_name += f" {user_agent.browser.version_string.split('.')[0]}"
        
        device_name += f" on {user_agent.os.family}"
        if user_agent.os.version_string:
            device_name += f" {user_agent.os.version_string}"
        
        # Determine device type
        if user_agent.is_mobile:
            device_type = "Mobile"
        elif user_agent.is_tablet:
            device_type = "Tablet"
        elif user_agent.is_pc:
            device_type = "Desktop"
        else:
            device_type = "Unknown"
        
        return {
            'device_name': device_name,
            'device_type': device_type,
            'browser': user_agent.browser.family,
            'browser_version': user_agent.browser.version_string,
            'os': user_agent.os.family,
            'os_version': user_agent.os.version_string,
            'is_mobile': user_agent.is_mobile,
            'is_tablet': user_agent.is_tablet,
            'is_pc': user_agent.is_pc,
        }
        
    except Exception as e:
        logger.warning(f"Error parsing user agent: {e}")
        return {
            'device_name': 'Unknown Device',
            'device_type': 'Unknown',
            'browser': 'Unknown',
            'browser_version': '',
            'os': 'Unknown',
            'os_version': '',
            'is_mobile': False,
            'is_tablet': False,
            'is_pc': False,
        }


def get_location_info(request):
    """
    Get approximate location information from IP address.
    
    Note: This is a basic implementation. In production, you might want to use
    a more sophisticated IP geolocation service.
    
    Returns:
        dict: Location information
    """
    ip_address = request.META.get('REMOTE_ADDR')
    
    # Skip location lookup for local/private IPs
    if not ip_address or is_private_ip(ip_address):
        return {
            'location': 'Local/Private Network',
            'country': '',
            'city': '',
            'region': '',
        }
    
    try:
        # Use a free IP geolocation service (replace with your preferred service)
        # Note: In production, consider using a paid service for better accuracy
        response = requests.get(
            f'http://ip-api.com/json/{ip_address}',
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'success':
                location_parts = []
                if data.get('city'):
                    location_parts.append(data['city'])
                if data.get('regionName'):
                    location_parts.append(data['regionName'])
                if data.get('country'):
                    location_parts.append(data['country'])
                
                location = ', '.join(location_parts) if location_parts else 'Unknown'
                
                return {
                    'location': location,
                    'country': data.get('country', ''),
                    'city': data.get('city', ''),
                    'region': data.get('regionName', ''),
                    'latitude': data.get('lat'),
                    'longitude': data.get('lon'),
                }
        
    except Exception as e:
        logger.warning(f"Error getting location info for IP {ip_address}: {e}")
    
    return {
        'location': 'Unknown Location',
        'country': '',
        'city': '',
        'region': '',
    }


def is_private_ip(ip_address):
    """Check if IP address is private/local."""
    if not ip_address:
        return True
    
    # IPv4 private ranges
    private_ipv4_patterns = [
        r'^127\.',  # Loopback
        r'^10\.',   # Private class A
        r'^172\.(1[6-9]|2[0-9]|3[01])\.',  # Private class B
        r'^192\.168\.',  # Private class C
        r'^169\.254\.',  # Link-local
    ]
    
    for pattern in private_ipv4_patterns:
        if re.match(pattern, ip_address):
            return True
    
    # IPv6 private/local addresses
    if ':' in ip_address:
        if ip_address.startswith('::1') or ip_address.startswith('fe80:'):
            return True
    
    return False


def is_suspicious_login(user, request):
    """
    Determine if a login attempt is suspicious.
    
    Factors considered:
    - New location
    - New device
    - Time since last login
    - Multiple failed attempts
    
    Returns:
        bool: True if login appears suspicious
    """
    from ..models.session_management import UserSession, SessionSecurityEvent
    
    try:
        current_ip = request.META.get('REMOTE_ADDR')
        current_location = get_location_info(request).get('location', '')
        
        # Check recent sessions for this user
        recent_sessions = UserSession.objects.filter(
            user=user,
            created_at__gte=timezone.now() - timedelta(days=30)
        ).order_by('-created_at')[:10]
        
        # Check if this is a new location
        known_locations = set()
        known_ips = set()
        
        for session in recent_sessions:
            if session.location:
                known_locations.add(session.location)
            if session.ip_address:
                known_ips.add(session.ip_address)
        
        # Suspicious if new location and new IP
        if (current_location not in known_locations and 
            current_ip not in known_ips and 
            current_location != 'Unknown Location'):
            return True
        
        # Check for recent failed login attempts
        recent_failures = SessionSecurityEvent.objects.filter(
            user=user,
            event_type='login_failed',
            created_at__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        if recent_failures >= 3:
            return True
        
        # Check time since last successful login
        last_login = user.last_login
        if last_login and (timezone.now() - last_login) > timedelta(days=90):
            return True
        
    except Exception as e:
        logger.warning(f"Error checking suspicious login for user {user.id}: {e}")
    
    return False


def should_warn_timeout(user_session):
    """
    Determine if user should be warned about session timeout.
    
    Returns:
        bool: True if timeout warning should be shown
    """
    if not user_session or not user_session.expires_at:
        return False
    
    time_until_expiry = user_session.time_until_expiry
    if not time_until_expiry:
        return False
    
    # Warn if less than 5 minutes remaining
    warning_threshold = getattr(settings, 'SESSION_TIMEOUT_WARNING_MINUTES', 5) * 60
    
    return time_until_expiry.total_seconds() <= warning_threshold


def extend_session(request, hours=None):
    """
    Extend the current user session.
    
    Args:
        request: HTTP request object
        hours: Number of hours to extend (default based on remember me)
    
    Returns:
        bool: True if session was extended successfully
    """
    if not request.user.is_authenticated:
        return False
    
    try:
        user_session = getattr(request, 'user_session', None)
        if not user_session:
            # Try to find session by session key
            session_key = request.session.session_key
            if session_key:
                from ..models.session_management import UserSession
                user_session = UserSession.objects.filter(
                    session_key=session_key,
                    user=request.user,
                    is_active=True
                ).first()
        
        if user_session:
            user_session.extend_expiry(hours)
            
            # Log session extension
            from ..models.session_management import SessionSecurityEvent
            SessionSecurityEvent.log_event(
                'session_extended',
                user=request.user,
                session=user_session,
                request=request,
                extended_hours=hours or (24 if user_session.is_remember_me else 8)
            )
            
            return True
            
    except Exception as e:
        logger.error(f"Error extending session for user {request.user.id}: {e}")
    
    return False


def terminate_session(session_key, reason='admin'):
    """
    Terminate a specific session.
    
    Args:
        session_key: Django session key
        reason: Reason for termination
    
    Returns:
        bool: True if session was terminated successfully
    """
    try:
        from ..models.session_management import UserSession
        from django.contrib.sessions.models import Session
        
        # Terminate UserSession record
        user_session = UserSession.objects.filter(
            session_key=session_key,
            is_active=True
        ).first()
        
        if user_session:
            user_session.terminate(reason)
            
            # Delete Django session
            try:
                django_session = Session.objects.get(session_key=session_key)
                django_session.delete()
            except Session.DoesNotExist:
                pass
            
            return True
            
    except Exception as e:
        logger.error(f"Error terminating session {session_key}: {e}")
    
    return False


def get_user_sessions(user, active_only=True):
    """
    Get sessions for a user.
    
    Args:
        user: User object
        active_only: Whether to return only active sessions
    
    Returns:
        QuerySet: User sessions
    """
    from ..models.session_management import UserSession
    
    queryset = UserSession.objects.filter(user=user)
    
    if active_only:
        queryset = queryset.filter(is_active=True)
    
    return queryset.order_by('-last_activity')


def cleanup_expired_sessions():
    """
    Clean up expired sessions.
    
    Returns:
        int: Number of sessions cleaned up
    """
    from ..models.session_management import UserSession
    return UserSession.cleanup_expired_sessions()
