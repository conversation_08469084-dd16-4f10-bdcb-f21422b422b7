# --- Django Imports ---
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings
import re
import logging
from datetime import timed<PERSON>ta
from ipaddress import ip_address, ip_network

logger = logging.getLogger(__name__)


def get_client_ip(request):
    """
    Get the real client IP address from request.
    
    Handles various proxy configurations and headers.
    """
    # Check for forwarded headers (common in load balancers/proxies)
    forwarded_headers = [
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP',
        'HTTP_CF_CONNECTING_IP',  # Cloudflare
        'HTTP_X_CLUSTER_CLIENT_IP',
    ]
    
    for header in forwarded_headers:
        ip_list = request.META.get(header)
        if ip_list:
            # Take the first IP in the list (original client)
            ip = ip_list.split(',')[0].strip()
            if is_valid_ip(ip):
                return ip
    
    # Fallback to REMOTE_ADDR
    return request.META.get('REMOTE_ADDR', '127.0.0.1')


def is_valid_ip(ip_string):
    """Check if string is a valid IP address."""
    try:
        ip_address(ip_string)
        return True
    except ValueError:
        return False


def is_private_ip(ip_string):
    """Check if IP address is private/internal."""
    try:
        ip = ip_address(ip_string)
        return ip.is_private or ip.is_loopback or ip.is_link_local
    except ValueError:
        return False


def is_suspicious_request(request):
    """
    Determine if a request appears suspicious.
    
    Factors considered:
    - Unusual user agent patterns
    - Suspicious headers
    - Request patterns
    - Geographic anomalies
    """
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    
    # Check for suspicious user agent patterns
    suspicious_ua_patterns = [
        r'bot',
        r'crawler',
        r'spider',
        r'scraper',
        r'curl',
        r'wget',
        r'python',
        r'requests',
        r'urllib',
        r'scanner',
        r'test',
    ]
    
    for pattern in suspicious_ua_patterns:
        if re.search(pattern, user_agent):
            return True
    
    # Check for missing or unusual headers
    if not user_agent:
        return True
    
    # Check for suspicious header combinations
    accept_header = request.META.get('HTTP_ACCEPT', '')
    if not accept_header or 'text/html' not in accept_header:
        # Non-browser request to authentication pages
        if any(path in request.path for path in ['/login/', '/signup/', '/password/']):
            return True
    
    # Check for rapid requests from same IP
    client_ip = get_client_ip(request)
    request_count_key = f"request_count:{client_ip}"
    request_count = cache.get(request_count_key, 0)
    
    if request_count > 50:  # More than 50 requests per minute
        return True
    
    cache.set(request_count_key, request_count + 1, 60)  # 1 minute window
    
    return False


def is_bot_request(request):
    """
    Determine if request is from a bot or automated system.
    
    More specific than is_suspicious_request, focuses on legitimate bots.
    """
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    
    # Known bot patterns
    bot_patterns = [
        r'googlebot',
        r'bingbot',
        r'slurp',  # Yahoo
        r'duckduckbot',
        r'baiduspider',
        r'yandexbot',
        r'facebookexternalhit',
        r'twitterbot',
        r'linkedinbot',
        r'whatsapp',
        r'telegrambot',
        r'applebot',
        r'amazonbot',
    ]
    
    for pattern in bot_patterns:
        if re.search(pattern, user_agent):
            return True
    
    return False


def check_rate_limit(request):
    """
    Check rate limits for the request.
    
    Returns dict with 'blocked' status and additional info.
    """
    client_ip = get_client_ip(request)
    path = request.path
    method = request.method
    
    # Define rate limits for different endpoints
    rate_limits = {
        '/accounts/login/': {'limit': 5, 'window': 300},  # 5 attempts per 5 minutes
        '/accounts/signup/': {'limit': 3, 'window': 3600},  # 3 signups per hour
        '/accounts/password/reset/': {'limit': 3, 'window': 3600},  # 3 resets per hour
        '/accounts/email/verification/resend/': {'limit': 3, 'window': 300},  # 3 resends per 5 minutes
    }
    
    # Check if path has rate limiting
    rate_limit_config = None
    for limited_path, config in rate_limits.items():
        if path.startswith(limited_path):
            rate_limit_config = config
            break
    
    if not rate_limit_config:
        return {'blocked': False}
    
    # Create cache key
    cache_key = f"rate_limit:{client_ip}:{path}:{method}"
    
    # Get current count
    current_count = cache.get(cache_key, 0)
    
    if current_count >= rate_limit_config['limit']:
        # Rate limit exceeded
        ttl = cache.ttl(cache_key)
        return {
            'blocked': True,
            'limit': rate_limit_config['limit'],
            'window': rate_limit_config['window'],
            'retry_after': ttl if ttl > 0 else rate_limit_config['window'],
            'current_count': current_count,
        }
    
    # Increment counter
    cache.set(cache_key, current_count + 1, rate_limit_config['window'])
    
    return {
        'blocked': False,
        'limit': rate_limit_config['limit'],
        'window': rate_limit_config['window'],
        'current_count': current_count + 1,
    }


def log_security_event(event_type, user=None, request=None, details=None, is_suspicious=False):
    """
    Log a security event.
    
    Args:
        event_type: Type of security event
        user: User object (if applicable)
        request: HTTP request object (if applicable)
        details: Additional event details
        is_suspicious: Whether event is flagged as suspicious
    """
    try:
        from ..models.session_management import SessionSecurityEvent
        
        event_data = {
            'event_type': event_type,
            'user': user,
            'is_suspicious': is_suspicious,
        }
        
        if request:
            event_data.update({
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        if details:
            event_data['details'] = details
        
        # Create the event
        event = SessionSecurityEvent.objects.create(**event_data)
        
        # Log to Django logger as well
        log_level = logging.WARNING if is_suspicious else logging.INFO
        logger.log(
            log_level,
            f"Security event: {event_type}",
            extra={
                'event_id': event.id,
                'user_id': user.id if user else None,
                'ip_address': event_data.get('ip_address'),
                'is_suspicious': is_suspicious,
                'details': details,
            }
        )
        
        return event
        
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")
        return None


def check_password_strength(password):
    """
    Check password strength and return score and feedback.
    
    Returns:
        dict: {
            'score': int (0-100),
            'feedback': list of strings,
            'is_strong': bool
        }
    """
    score = 0
    feedback = []
    
    # Length check
    if len(password) >= 8:
        score += 20
    else:
        feedback.append("Password should be at least 8 characters long")
    
    if len(password) >= 12:
        score += 10
    
    # Character variety checks
    if re.search(r'[a-z]', password):
        score += 15
    else:
        feedback.append("Include lowercase letters")
    
    if re.search(r'[A-Z]', password):
        score += 15
    else:
        feedback.append("Include uppercase letters")
    
    if re.search(r'\d', password):
        score += 15
    else:
        feedback.append("Include numbers")
    
    if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        score += 15
    else:
        feedback.append("Include special characters")
    
    # Pattern checks
    if not re.search(r'(.)\1{2,}', password):  # No 3+ repeated characters
        score += 5
    else:
        feedback.append("Avoid repeating characters")
    
    if not re.search(r'(012|123|234|345|456|567|678|789|890)', password):  # No sequential numbers
        score += 5
    else:
        feedback.append("Avoid sequential numbers")
    
    # Common password check (basic)
    common_passwords = [
        'password', '123456', 'qwerty', 'abc123', 'password123',
        'admin', 'letmein', 'welcome', 'monkey', 'dragon'
    ]
    
    if password.lower() not in common_passwords:
        score += 10
    else:
        feedback.append("Avoid common passwords")
    
    return {
        'score': min(score, 100),
        'feedback': feedback,
        'is_strong': score >= 70
    }


def validate_email_security(email):
    """
    Validate email for security concerns.
    
    Returns:
        dict: {
            'is_valid': bool,
            'warnings': list of strings,
            'is_disposable': bool
        }
    """
    warnings = []
    is_disposable = False
    
    # Basic email format validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return {
            'is_valid': False,
            'warnings': ['Invalid email format'],
            'is_disposable': False
        }
    
    domain = email.split('@')[1].lower()
    
    # Check for disposable email domains (basic list)
    disposable_domains = [
        '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
        'mailinator.com', 'yopmail.com', 'temp-mail.org',
        'throwaway.email', 'getnada.com', 'maildrop.cc'
    ]
    
    if domain in disposable_domains:
        is_disposable = True
        warnings.append("Disposable email addresses are not recommended")
    
    # Check for suspicious patterns
    if re.search(r'\d{4,}', email):  # Many consecutive numbers
        warnings.append("Email contains suspicious number patterns")
    
    if len(email.split('@')[0]) < 3:  # Very short local part
        warnings.append("Email local part is very short")
    
    return {
        'is_valid': True,
        'warnings': warnings,
        'is_disposable': is_disposable
    }


def get_security_recommendations(user):
    """
    Get security recommendations for a user.
    
    Returns:
        list: List of recommendation dictionaries
    """
    recommendations = []
    
    # Check if user has password
    if not user.has_usable_password():
        recommendations.append({
            'type': 'critical',
            'title': 'Set a Password',
            'description': 'You only have social login. Set a password as backup.',
            'action_url': '/accounts/password/set/',
            'action_text': 'Set Password'
        })
    
    # Check for social accounts
    from allauth.socialaccount.models import SocialAccount
    social_accounts = SocialAccount.objects.filter(user=user)
    
    if not social_accounts.exists():
        recommendations.append({
            'type': 'medium',
            'title': 'Connect Social Accounts',
            'description': 'Link social accounts for easier sign-in.',
            'action_url': '/accounts/social/',
            'action_text': 'Manage Social Accounts'
        })
    
    # Check last password change (if we track this)
    if hasattr(user, 'last_password_change'):
        if user.last_password_change:
            days_since_change = (timezone.now() - user.last_password_change).days
            if days_since_change > 90:
                recommendations.append({
                    'type': 'medium',
                    'title': 'Update Password',
                    'description': f'Password last changed {days_since_change} days ago.',
                    'action_url': '/accounts/password/change/',
                    'action_text': 'Change Password'
                })
    
    # Check for multiple active sessions
    from ..models.session_management import UserSession
    active_sessions = UserSession.objects.filter(user=user, is_active=True).count()
    
    if active_sessions > 3:
        recommendations.append({
            'type': 'low',
            'title': 'Review Active Sessions',
            'description': f'You have {active_sessions} active sessions.',
            'action_url': '/accounts/sessions/',
            'action_text': 'Manage Sessions'
        })
    
    return recommendations
