# --- Standard Library Imports ---
import logging
from typing import Dict, Any, Optional

# --- Django Imports ---
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _

# --- Third-party Imports ---
from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.models import SocialAccount

# --- Local App Imports ---
from .models import CustomerProfile, ServiceProviderProfile

# --- Setup ---
User = get_user_model()
logger = logging.getLogger(__name__)


class CozyWishAccountAdapter(DefaultAccountAdapter):
    """
    Custom account adapter for role-based user creation and management.
    
    Features:
    - Role assignment during signup
    - Automatic profile creation based on role
    - Custom validation for role-based signup
    - Maintains existing business logic for email verification
    - Supports both customer and service provider registration
    """
    
    def save_user(self, request: HttpRequest, user: User, form, commit: bool = True) -> User:
        """
        Save user with role assignment and profile creation.

        This method handles:
        - Role assignment based on signup context
        - Profile creation for customers and service providers
        - Email verification workflow
        - Transaction safety for data integrity

        Args:
            request: HTTP request object
            user: User instance to save
            form: Signup form instance
            commit: Whether to save to database

        Returns:
            User: The saved user instance
        """
        # Extract email from form if not set on user
        if not user.email and hasattr(form, 'cleaned_data') and 'email' in form.cleaned_data:
            user.email = form.cleaned_data['email']

        # Extract first/last name from form if available
        if hasattr(form, 'cleaned_data'):
            if 'first_name' in form.cleaned_data and form.cleaned_data['first_name']:
                user.first_name = form.cleaned_data['first_name']
            if 'last_name' in form.cleaned_data and form.cleaned_data['last_name']:
                user.last_name = form.cleaned_data['last_name']

        # Determine role based on request context or form data
        role = self._determine_user_role(request, form)
        user.role = role

        # Set email verification status based on role
        if role == User.SERVICE_PROVIDER:
            # Service providers require email verification
            user.is_active = False
        else:
            # Customers are active immediately (can be changed based on requirements)
            user.is_active = True

        if commit:
            with transaction.atomic():
                user.save()
                self._create_user_profile(user, form)

        return user

    def populate_username(self, request: HttpRequest, user: User) -> None:
        """
        Populate username field. Since our CustomUser doesn't use username,
        we override this to do nothing.

        Args:
            request: HTTP request object
            user: User instance
        """
        # Our CustomUser model doesn't have a username field, so we skip this
        pass
    
    def _determine_user_role(self, request: HttpRequest, form) -> str:
        """
        Determine user role based on request context or form data.

        Priority order:
        1. Explicit role in form data (from our custom forms)
        2. Form instance role attribute (from our custom forms)
        3. URL path analysis
        4. Session data
        5. Default to customer

        Args:
            request: HTTP request object
            form: Signup form instance

        Returns:
            str: User role (customer, service_provider, or admin)
        """
        # Check if form has role field in cleaned_data (highest priority)
        if hasattr(form, 'cleaned_data') and form.cleaned_data and 'role' in form.cleaned_data:
            role = form.cleaned_data['role']
            if role in [User.CUSTOMER, User.SERVICE_PROVIDER, User.ADMIN]:
                return role

        # Check form instance for role attribute (set by our custom forms)
        if hasattr(form, 'role'):
            role = form.role
            if role in [User.CUSTOMER, User.SERVICE_PROVIDER, User.ADMIN]:
                return role

        # Analyze URL path to determine signup type
        if request and hasattr(request, 'path'):
            path = request.path.lower()
            if any(keyword in path for keyword in ['provider', 'business', 'service']):
                return User.SERVICE_PROVIDER
            elif any(keyword in path for keyword in ['customer', 'client']):
                return User.CUSTOMER

        # Check session for role preference
        if request and hasattr(request, 'session'):
            session_role = request.session.get('signup_role')
            if session_role in [User.CUSTOMER, User.SERVICE_PROVIDER, User.ADMIN]:
                return session_role

        # Default to customer role
        return User.CUSTOMER
    
    def _create_user_profile(self, user: User, form) -> None:
        """
        Create appropriate profile based on user role.
        
        Args:
            user: User instance
            form: Signup form instance
        """
        try:
            if user.role == User.CUSTOMER:
                self._create_customer_profile(user, form)
            elif user.role == User.SERVICE_PROVIDER:
                self._create_service_provider_profile(user, form)
            # Admin users don't need profiles by default
                
        except Exception as e:
            logger.error(f"Failed to create profile for user {user.email}: {str(e)}")
            raise
    
    def _create_customer_profile(self, user: User, form) -> CustomerProfile:
        """
        Create customer profile with form data.
        
        Args:
            user: User instance
            form: Signup form instance
            
        Returns:
            CustomerProfile: Created profile instance
        """
        profile_data = {}
        
        # Extract profile data from form if available
        if hasattr(form, 'cleaned_data'):
            cleaned_data = form.cleaned_data
            
            # Map form fields to profile fields
            field_mapping = {
                'first_name': 'first_name',
                'last_name': 'last_name',
                'phone_number': 'phone_number',
                'gender': 'gender',
                'birth_month': 'birth_month',
                'birth_year': 'birth_year',
                'address': 'address',
                'city': 'city',
                'zip_code': 'zip_code',
            }
            
            for form_field, profile_field in field_mapping.items():
                if form_field in cleaned_data and cleaned_data[form_field]:
                    profile_data[profile_field] = cleaned_data[form_field]
        
        # Create profile
        profile = CustomerProfile.objects.create(user=user, **profile_data)
        logger.info(f"Created customer profile for user {user.email}")
        return profile
    
    def _create_service_provider_profile(self, user: User, form) -> ServiceProviderProfile:
        """
        Create service provider profile with form data.
        
        Args:
            user: User instance
            form: Signup form instance
            
        Returns:
            ServiceProviderProfile: Created profile instance
        """
        profile_data = {}
        
        # Extract profile data from form if available
        if hasattr(form, 'cleaned_data'):
            cleaned_data = form.cleaned_data
            
            # Map form fields to profile fields
            field_mapping = {
                'business_name': 'legal_name',
                'legal_name': 'legal_name',
                'display_name': 'display_name',
                'business_phone_number': 'phone',
                'phone': 'phone',
                'contact_person_name': 'contact_name',
                'contact_name': 'contact_name',
                'business_address': 'address',
                'address': 'address',
                'city': 'city',
                'state': 'state',
                'county': 'county',
                'zip_code': 'zip_code',
                'ein': 'ein',
                'website': 'website',
                'instagram': 'instagram',
                'facebook': 'facebook',
                'description': 'description',
            }
            
            for form_field, profile_field in field_mapping.items():
                if form_field in cleaned_data and cleaned_data[form_field]:
                    profile_data[profile_field] = cleaned_data[form_field]
        
        # Ensure required fields have defaults
        if 'legal_name' not in profile_data:
            profile_data['legal_name'] = f"Business for {user.email}"
        if 'phone' not in profile_data:
            profile_data['phone'] = '+**********'  # Default placeholder
        if 'contact_name' not in profile_data:
            profile_data['contact_name'] = user.get_full_name() or user.email.split('@')[0]
        if 'address' not in profile_data:
            profile_data['address'] = 'Address not provided'
        if 'city' not in profile_data:
            profile_data['city'] = 'City not provided'
        if 'state' not in profile_data:
            profile_data['state'] = 'CA'  # Default state
        if 'zip_code' not in profile_data:
            profile_data['zip_code'] = '00000'
        
        # Create profile
        profile = ServiceProviderProfile.objects.create(user=user, **profile_data)
        logger.info(f"Created service provider profile for user {user.email}")
        return profile

    def clean_email(self, email: str) -> str:
        """
        Clean and validate email address.

        Args:
            email: Email address to clean

        Returns:
            str: Cleaned email address
        """
        # Call parent method for basic cleaning
        email = super().clean_email(email)

        # Additional custom validation can be added here
        if email:
            email = email.lower().strip()

        return email

    def is_open_for_signup(self, request: HttpRequest) -> bool:
        """
        Check if signup is open.

        Args:
            request: HTTP request object

        Returns:
            bool: True if signup is allowed
        """
        # You can add custom logic here to control signup availability
        # For example, disable signup during maintenance or for specific user types
        return super().is_open_for_signup(request)

    def get_login_redirect_url(self, request: HttpRequest) -> str:
        """
        Get redirect URL after login based on user role and preferences.

        Args:
            request: HTTP request object

        Returns:
            str: Redirect URL
        """
        user = request.user

        if user.is_authenticated:
            # Check for stored next URL from session
            next_url = request.session.get('login_next_url')
            if next_url:
                # Clear the session variable and use the stored URL
                del request.session['login_next_url']
                request.session.save()
                return next_url

            # Role-based redirects
            if user.role == User.SERVICE_PROVIDER:
                return '/dashboard/provider/'  # Service provider dashboard
            elif user.role == User.CUSTOMER:
                return '/dashboard/customer/'  # Customer dashboard
            elif user.role == User.ADMIN:
                return '/admin-panel/'  # Admin panel

        # Default redirect
        return super().get_login_redirect_url(request)

    def get_signup_redirect_url(self, request: HttpRequest) -> str:
        """
        Get redirect URL after signup based on user role.

        Args:
            request: HTTP request object

        Returns:
            str: Redirect URL
        """
        user = request.user

        if user.is_authenticated:
            # Check for stored next URL from session
            next_url = request.session.get('signup_next_url')
            if next_url:
                # Clear the session variable and use the stored URL
                del request.session['signup_next_url']
                request.session.save()
                return next_url

            # Role-based redirects for new signups
            if user.role == User.SERVICE_PROVIDER:
                # For service providers, redirect to profile completion
                return '/accounts/provider/profile/'
            elif user.role == User.CUSTOMER:
                # For customers, redirect to dashboard
                return '/dashboard/customer/'
            elif user.role == User.ADMIN:
                return '/admin-panel/'

        # Default redirect
        return super().get_signup_redirect_url(request)

    def get_email_confirmation_redirect_url(self, request):
        """
        Custom redirect URL after email confirmation.

        Redirects to custom success page with role-based next steps.
        """
        return '/accounts/email/verification/success/'

    def send_confirmation_mail(self, request, emailconfirmation, signup):
        """
        Override to use custom email templates and add tracking.
        """
        # Log email confirmation request
        logger.info(
            "Email confirmation requested",
            extra={
                'user_id': emailconfirmation.email_address.user.id,
                'email': emailconfirmation.email_address.email,
                'signup': signup,
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
                'event': 'email_confirmation_requested'
            }
        )

        # Call parent method to send email
        return super().send_confirmation_mail(request, emailconfirmation, signup)


class CozyWishSocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom social account adapter for role-based social authentication.

    Features:
    - Role assignment for social signups
    - Profile creation from social data
    - Custom validation for social account role assignment
    - Integration with existing user management
    """

    def populate_user(self, request: HttpRequest, sociallogin, data: Dict[str, Any]) -> User:
        """
        Populate user instance from social account data.

        Args:
            request: HTTP request object
            sociallogin: Social login instance
            data: Social account data

        Returns:
            User: Populated user instance
        """
        # Call parent method to handle basic user population
        user = super().populate_user(request, sociallogin, data)

        # Determine role for social signup
        role = self._determine_social_user_role(request, sociallogin, data)
        user.role = role

        # Extract additional user data from social providers
        self._extract_social_user_data(user, sociallogin, data)

        return user

    def save_user(self, request: HttpRequest, sociallogin, form=None) -> User:
        """
        Save user from social login with profile creation.

        Args:
            request: HTTP request object
            sociallogin: Social login instance
            form: Optional form instance (our custom social signup form)

        Returns:
            User: Saved user instance
        """
        # Get the user instance
        user = sociallogin.user

        # Determine role from form if available, otherwise use existing logic
        if form and hasattr(form, 'cleaned_data') and 'role' in form.cleaned_data:
            user.role = form.cleaned_data['role']
        elif form and hasattr(form, 'role'):
            user.role = form.role
        else:
            # Use existing role determination logic
            user.role = self._determine_social_user_role(request, sociallogin, sociallogin.account.extra_data)

        # Save user with transaction safety
        with transaction.atomic():
            # Call parent method to save user
            user = super().save_user(request, sociallogin, form)

            # Create appropriate profile
            self._create_social_user_profile(user, sociallogin)

        return user

    def _determine_social_user_role(self, request: HttpRequest, sociallogin, data: Dict[str, Any]) -> str:
        """
        Determine user role for social signup.

        Args:
            request: HTTP request object
            sociallogin: Social login instance
            data: Social account data

        Returns:
            str: User role
        """
        # Check session for role preference (from our custom social signup form)
        if request and hasattr(request, 'session'):
            session_role = request.session.get('social_signup_role')
            if session_role in [User.CUSTOMER, User.SERVICE_PROVIDER]:
                return session_role

        # Analyze URL path to determine signup type
        if request and hasattr(request, 'path'):
            path = request.path.lower()
            if any(keyword in path for keyword in ['provider', 'business', 'service']):
                return User.SERVICE_PROVIDER

        # Check social account data for business indicators
        if self._is_business_account(sociallogin, data):
            return User.SERVICE_PROVIDER

        # Default to customer role for social signups
        return User.CUSTOMER

    def _is_business_account(self, sociallogin, data: Dict[str, Any]) -> bool:
        """
        Check if social account appears to be a business account.

        Args:
            sociallogin: Social login instance
            data: Social account data

        Returns:
            bool: True if appears to be business account
        """
        # Check for business indicators in social data
        business_keywords = ['business', 'company', 'corp', 'llc', 'inc', 'ltd', 'service']

        # Check name fields
        name_fields = ['name', 'company', 'organization']
        for field in name_fields:
            if field in data and data[field]:
                name_lower = str(data[field]).lower()
                if any(keyword in name_lower for keyword in business_keywords):
                    return True

        # Check email domain for business patterns
        if 'email' in data and data['email']:
            email = str(data['email']).lower()
            business_domains = ['business', 'company', 'corp', 'services']
            if any(domain in email for domain in business_domains):
                return True

        return False

    def _extract_social_user_data(self, user: User, sociallogin, data: Dict[str, Any]) -> None:
        """
        Extract additional user data from social providers.

        Args:
            user: User instance
            sociallogin: Social login instance
            data: Social account data
        """
        # Extract first and last name
        if 'first_name' in data and data['first_name']:
            user.first_name = data['first_name']
        if 'last_name' in data and data['last_name']:
            user.last_name = data['last_name']

        # Handle full name if first/last not available
        if 'name' in data and data['name'] and not (user.first_name and user.last_name):
            name_parts = str(data['name']).split()
            if len(name_parts) >= 2:
                user.first_name = name_parts[0]
                user.last_name = ' '.join(name_parts[1:])
            elif len(name_parts) == 1:
                user.first_name = name_parts[0]

    def _create_social_user_profile(self, user: User, sociallogin) -> None:
        """
        Create user profile from social login data.

        Args:
            user: User instance
            sociallogin: Social login instance
        """
        try:
            # Get social account data
            social_account = sociallogin.account
            extra_data = social_account.extra_data

            if user.role == User.CUSTOMER:
                self._create_social_customer_profile(user, extra_data)
            elif user.role == User.SERVICE_PROVIDER:
                self._create_social_service_provider_profile(user, extra_data)

        except Exception as e:
            logger.error(f"Failed to create social profile for user {user.email}: {str(e)}")
            # Don't raise exception to avoid breaking social login flow

    def _create_social_customer_profile(self, user: User, extra_data: Dict[str, Any]) -> CustomerProfile:
        """
        Create customer profile from social data.

        Args:
            user: User instance
            extra_data: Social account extra data

        Returns:
            CustomerProfile: Created profile instance
        """
        profile_data = {
            'first_name': user.first_name,
            'last_name': user.last_name,
        }

        # Extract additional data from social providers
        if 'gender' in extra_data:
            gender_map = {'male': 'M', 'female': 'F'}
            gender = str(extra_data['gender']).lower()
            if gender in gender_map:
                profile_data['gender'] = gender_map[gender]

        # Create profile
        profile = CustomerProfile.objects.create(user=user, **profile_data)
        logger.info(f"Created social customer profile for user {user.email}")
        return profile

    def _create_social_service_provider_profile(self, user: User, extra_data: Dict[str, Any]) -> ServiceProviderProfile:
        """
        Create service provider profile from social data.

        Args:
            user: User instance
            extra_data: Social account extra data

        Returns:
            ServiceProviderProfile: Created profile instance
        """
        # Extract business name from social data
        business_name = None
        if 'company' in extra_data and extra_data['company']:
            business_name = extra_data['company']
        elif 'organization' in extra_data and extra_data['organization']:
            business_name = extra_data['organization']
        elif 'name' in extra_data and extra_data['name']:
            business_name = extra_data['name']

        if not business_name:
            business_name = f"Business for {user.get_full_name() or user.email}"

        profile_data = {
            'legal_name': business_name,
            'contact_name': user.get_full_name() or user.email.split('@')[0],
            'phone': '+**********',  # Default placeholder
            'address': 'Address not provided',
            'city': 'City not provided',
            'state': 'CA',  # Default state
            'zip_code': '00000',
        }

        # Extract website if available
        if 'website' in extra_data and extra_data['website']:
            profile_data['website'] = extra_data['website']

        # Create profile
        profile = ServiceProviderProfile.objects.create(user=user, **profile_data)
        logger.info(f"Created social service provider profile for user {user.email}")
        return profile

    def is_open_for_signup(self, request: HttpRequest, sociallogin) -> bool:
        """
        Check if social signup is open.

        Args:
            request: HTTP request object
            sociallogin: Social login instance

        Returns:
            bool: True if social signup is allowed
        """
        # You can add custom logic here to control social signup availability
        return super().is_open_for_signup(request, sociallogin)

    def authentication_error(self, request: HttpRequest, provider_id: str, error=None, exception=None, extra_context=None):
        """
        Handle social authentication errors.

        Args:
            request: HTTP request object
            provider_id: Social provider ID
            error: Error message
            exception: Exception instance
            extra_context: Additional context
        """
        logger.error(f"Social authentication error for provider {provider_id}: {error}")
        return super().authentication_error(request, provider_id, error, exception, extra_context)
