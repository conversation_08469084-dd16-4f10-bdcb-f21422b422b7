"""
Template tags for enhanced form validation and UX features.

These tags add modern validation attributes, ARIA labels, and progressive
enhancement support to Django forms.
"""

from django import template
from django.forms import <PERSON>und<PERSON>ield
from django.utils.safestring import mark_safe
from django.utils.html import format_html
import json

register = template.Library()


@register.filter
def add_validation_attrs(field, validation_type=None):
    """
    Add validation attributes to a form field.
    
    Usage: {{ field|add_validation_attrs:"email" }}
    """
    if not isinstance(field, BoundField):
        return field
    
    # Get or determine validation type
    if not validation_type:
        validation_type = get_field_validation_type(field)
    
    # Add validation attributes
    attrs = field.field.widget.attrs.copy()
    
    # Add basic validation attributes
    attrs.update({
        'data-validation': validation_type,
        'data-validation-realtime': 'true',
        'data-field-name': field.name,
    })
    
    # Add type-specific attributes
    if validation_type == 'email':
        attrs.update({
            'data-validation-pattern': r'^[^\s@]+@[^\s@]+\.[^\s@]+$',
            'data-validation-message': 'Please enter a valid email address',
            'autocomplete': 'email',
        })
    elif validation_type == 'password':
        attrs.update({
            'data-validation-strength': 'true',
            'data-validation-message': 'Password must meet security requirements',
            'autocomplete': 'new-password' if 'new' in field.name.lower() else 'current-password',
        })
    elif validation_type == 'phone':
        attrs.update({
            'data-phone-format': 'true',
            'data-validation-pattern': r'^[\+]?[1-9][\d]{0,15}$',
            'data-validation-message': 'Please enter a valid phone number',
            'autocomplete': 'tel',
        })
    elif validation_type == 'url':
        attrs.update({
            'data-validation-pattern': r'^https?:\/\/.+\..+',
            'data-validation-message': 'Please enter a valid URL',
        })
    
    # Add required indicator
    if field.field.required:
        attrs.update({
            'data-required': 'true',
            'aria-required': 'true',
        })
    
    # Add ARIA label if not present
    if 'aria-label' not in attrs:
        attrs['aria-label'] = field.label or field.name.replace('_', ' ').title()
    
    # Apply attributes to widget
    field.field.widget.attrs.update(attrs)
    
    return field


@register.filter
def add_enhanced_attrs(field, config=None):
    """
    Add enhanced UX attributes to a form field.
    
    Usage: {{ field|add_enhanced_attrs:"auto-save,profile-completion" }}
    """
    if not isinstance(field, BoundField):
        return field
    
    attrs = field.field.widget.attrs.copy()
    
    if config:
        features = [f.strip() for f in config.split(',')]
        
        if 'auto-save' in features:
            attrs['data-auto-save'] = 'true'
        
        if 'profile-completion' in features:
            attrs['data-completion-field'] = 'true'
            # Add completion weight based on field importance
            weight = get_field_completion_weight(field.name)
            attrs['data-completion-weight'] = str(weight)
        
        if 'smart-default' in features:
            attrs['data-smart-default'] = get_smart_default_type(field.name)
        
        if 'progressive-disclosure' in features:
            attrs['data-disclosure-trigger'] = 'true'
    
    field.field.widget.attrs.update(attrs)
    return field


@register.inclusion_tag('accounts/components/validation_container.html')
def validation_container(field, show_strength=False, show_help=False):
    """
    Render a validation container with feedback elements.
    
    Usage: {% validation_container field show_strength=True show_help=True %}
    """
    validation_type = get_field_validation_type(field)
    
    return {
        'field': field,
        'validation_type': validation_type,
        'show_strength': show_strength and validation_type == 'password',
        'show_help': show_help,
        'help_text': get_field_help_text(field, validation_type),
    }


@register.inclusion_tag('accounts/components/form_enhancements.html')
def form_enhancements(form, features=None):
    """
    Add form-level enhancements.
    
    Usage: {% form_enhancements form "ajax-validation,auto-save,step-indicator" %}
    """
    features_list = []
    if features:
        features_list = [f.strip() for f in features.split(',')]
    
    return {
        'form': form,
        'features': features_list,
        'ajax_validation': 'ajax-validation' in features_list,
        'auto_save': 'auto-save' in features_list,
        'step_indicator': 'step-indicator' in features_list,
        'profile_completion': 'profile-completion' in features_list,
    }


@register.inclusion_tag('accounts/components/progressive_disclosure.html')
def progressive_disclosure(trigger_field, target_selector, trigger_value=None):
    """
    Setup progressive disclosure for form fields.
    
    Usage: {% progressive_disclosure form.role ".business-fields" "service_provider" %}
    """
    return {
        'trigger_field': trigger_field,
        'target_selector': target_selector,
        'trigger_value': trigger_value or 'checked',
    }


@register.filter
def add_dynamic_attrs(field, config):
    """
    Add dynamic field visibility attributes.
    
    Usage: {{ field|add_dynamic_attrs:"show:.business-fields,value:service_provider" }}
    """
    if not isinstance(field, BoundField):
        return field
    
    try:
        # Parse configuration
        config_dict = {}
        for item in config.split(','):
            if ':' in item:
                key, value = item.split(':', 1)
                config_dict[key.strip()] = value.strip()
        
        # Add dynamic attributes
        attrs = field.field.widget.attrs.copy()
        
        if 'show' in config_dict:
            attrs['data-disclosure-trigger'] = 'true'
            attrs['data-disclosure-target'] = config_dict['show']
            if 'value' in config_dict:
                attrs['data-disclosure-value'] = config_dict['value']
        
        field.field.widget.attrs.update(attrs)
    except Exception:
        # Silently fail if configuration is invalid
        pass
    
    return field


@register.filter
def add_autocomplete_attrs(field, source_url):
    """
    Add autocomplete attributes to a form field.
    
    Usage: {{ field|add_autocomplete_attrs:"/api/cities/" }}
    """
    if not isinstance(field, BoundField):
        return field
    
    attrs = field.field.widget.attrs.copy()
    attrs.update({
        'data-autocomplete': source_url,
        'data-autocomplete-min-length': '2',
        'data-autocomplete-delay': '300',
        'autocomplete': 'off',  # Disable browser autocomplete
    })
    
    field.field.widget.attrs.update(attrs)
    return field


@register.filter
def add_file_upload_attrs(field, config=None):
    """
    Add file upload enhancement attributes.
    
    Usage: {{ field|add_file_upload_attrs:"progress,preview,max-size:5MB" }}
    """
    if not isinstance(field, BoundField):
        return field
    
    attrs = field.field.widget.attrs.copy()
    
    # Default file upload attributes
    attrs.update({
        'data-upload-progress': 'true',
        'data-upload-url': '/upload/',
    })
    
    if config:
        features = [f.strip() for f in config.split(',')]
        
        for feature in features:
            if feature == 'progress':
                attrs['data-upload-progress'] = 'true'
            elif feature == 'preview':
                attrs['data-preview-container'] = f'#{field.name}-preview'
            elif feature.startswith('max-size:'):
                size = feature.split(':', 1)[1]
                attrs['data-max-size'] = convert_size_to_bytes(size)
            elif feature.startswith('types:'):
                types = feature.split(':', 1)[1]
                attrs['data-allowed-types'] = types
    
    field.field.widget.attrs.update(attrs)
    return field


@register.simple_tag
def csrf_token_meta():
    """
    Generate CSRF token meta tag for JavaScript access.
    
    Usage: {% csrf_token_meta %}
    """
    from django.middleware.csrf import get_token
    from django.template.context import RequestContext
    
    # This will be properly implemented with request context
    return format_html('<meta name="csrf-token" content="">')


@register.simple_tag
def form_validation_config(form):
    """
    Generate JavaScript configuration for form validation.
    
    Usage: {% form_validation_config form %}
    """
    config = {
        'validation_enabled': True,
        'realtime_validation': True,
        'ajax_validation': False,
        'auto_save': False,
        'fields': {}
    }
    
    # Add field-specific configuration
    for field_name, field in form.fields.items():
        field_config = {
            'required': field.required,
            'validation_type': get_field_validation_type_from_field(field),
        }
        
        # Add widget-specific configuration
        if hasattr(field.widget, 'attrs'):
            attrs = field.widget.attrs
            if 'data-validation' in attrs:
                field_config['validation_type'] = attrs['data-validation']
            if 'data-validation-realtime' in attrs:
                field_config['realtime'] = attrs['data-validation-realtime'] == 'true'
        
        config['fields'][field_name] = field_config
    
    return format_html(
        '<script type="application/json" id="form-validation-config">{}</script>',
        json.dumps(config)
    )


# Helper functions

def get_field_validation_type(field):
    """Determine validation type from field name and widget."""
    field_name = field.name.lower()
    widget_type = field.field.widget.__class__.__name__.lower()
    
    if 'email' in field_name or 'emailinput' in widget_type:
        return 'email'
    elif 'password' in field_name or 'passwordinput' in widget_type:
        return 'password'
    elif 'phone' in field_name or field_name in ['tel', 'telephone']:
        return 'phone'
    elif 'url' in field_name or 'urlinput' in widget_type:
        return 'url'
    elif 'date' in field_name or 'dateinput' in widget_type:
        return 'date'
    elif 'number' in field_name or 'numberinput' in widget_type:
        return 'number'
    else:
        return 'text'


def get_field_validation_type_from_field(field):
    """Determine validation type from Django field object."""
    field_type = field.__class__.__name__.lower()
    
    if 'email' in field_type:
        return 'email'
    elif 'url' in field_type:
        return 'url'
    elif 'phone' in field_type:
        return 'phone'
    elif 'date' in field_type:
        return 'date'
    elif 'integer' in field_type or 'decimal' in field_type:
        return 'number'
    else:
        return 'text'


def get_field_completion_weight(field_name):
    """Get completion weight for profile completion tracking."""
    weights = {
        'email': 20,
        'first_name': 10,
        'last_name': 10,
        'phone_number': 15,
        'profile_picture': 10,
        'business_name': 15,
        'business_description': 10,
        'address': 10,
        'city': 5,
        'zip_code': 5,
    }
    return weights.get(field_name, 5)


def get_smart_default_type(field_name):
    """Get smart default type for field."""
    if 'zip' in field_name.lower():
        return 'zip'
    elif 'business_name' in field_name.lower():
        return 'business-name'
    elif 'phone' in field_name.lower():
        return 'phone'
    else:
        return 'general'


def get_field_help_text(field, validation_type):
    """Get contextual help text for field."""
    help_texts = {
        'email': 'We\'ll never share your email address with anyone else.',
        'password': 'Password must be at least 8 characters with uppercase, lowercase, number, and special character.',
        'phone': 'Enter your phone number. We\'ll format it automatically.',
        'url': 'Enter a complete URL starting with http:// or https://',
    }
    
    return help_texts.get(validation_type, field.help_text if hasattr(field, 'help_text') else '')


def convert_size_to_bytes(size_str):
    """Convert size string like '5MB' to bytes."""
    size_str = size_str.upper()
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)  # Assume bytes
