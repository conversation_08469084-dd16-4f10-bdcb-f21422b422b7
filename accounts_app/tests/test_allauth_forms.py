# --- Standard Library Imports ---
from unittest.mock import Mock, patch

# --- Django Imports ---
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

# --- Third-party Imports ---
import pytest
from allauth.socialaccount.models import SocialLogin, SocialAccount

# --- Local App Imports ---
from accounts_app.allauth_forms import (
    CozyWishSignupForm,
    CozyWishSocialSignupForm,
    CozyWishLoginForm,
)
from accounts_app.models import CustomerProfile, ServiceProviderProfile

# --- Setup ---
User = get_user_model()


class CozyWishSignupFormTests(TestCase):
    """Test cases for CozyWishSignupForm."""
    
    def setUp(self):
        """Set up test dependencies."""
        self.factory = RequestFactory()
    
    def test_customer_signup_form_valid(self):
        """Test valid customer signup form."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_service_provider_signup_form_valid(self):
        """Test valid service provider signup form."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            'business_name': 'Test Business',
            'contact_name': 'John Doe',
            'phone': '+**********',
            'address': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345',
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
    
    def test_service_provider_missing_required_fields(self):
        """Test service provider signup with missing required fields."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            # Missing business fields
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        
        # Check that required fields have errors
        required_fields = ['business_name', 'contact_name', 'phone', 'address', 'city', 'state', 'zip_code']
        for field in required_fields:
            self.assertIn(field, form.errors)
    
    def test_duplicate_email_validation(self):
        """Test email uniqueness validation."""
        # Create existing user
        User.objects.create_user(email='<EMAIL>', password='testpass123!')
        
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_terms_agreement_required(self):
        """Test that terms agreement is required."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.CUSTOMER,
            'agree_to_terms': False,
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)
    
    def test_phone_normalization(self):
        """Test phone number normalization."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            'business_name': 'Test Business',
            'contact_name': 'John Doe',
            'phone': '(*************',  # Format that needs normalization
            'address': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345',
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['phone'], '+**********')
    
    def test_form_save_sets_role_attribute(self):
        """Test that form save method sets role attribute for adapter."""
        form_data = {
            'email': '<EMAIL>',
            'password1': 'testpass123!',
            'password2': 'testpass123!',
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
            'business_name': 'Test Business',
            'contact_name': 'John Doe',
            'phone': '+**********',
            'address': '123 Main St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '12345',
        }
        form = CozyWishSignupForm(data=form_data)
        self.assertTrue(form.is_valid())

        request = self.factory.post('/accounts/signup/')
        user = form.save(request)

        # Check that role attribute is set on form
        self.assertEqual(form.role, User.SERVICE_PROVIDER)
        # Check that user was created with correct role
        self.assertEqual(user.role, User.SERVICE_PROVIDER)
        self.assertEqual(user.email, '<EMAIL>')


class CozyWishSocialSignupFormTests(TestCase):
    """Test cases for CozyWishSocialSignupForm."""

    def setUp(self):
        """Set up test dependencies."""
        self.factory = RequestFactory()
        # Create a mock sociallogin object
        self.mock_sociallogin = Mock()
        self.mock_sociallogin.account = Mock()
        self.mock_sociallogin.account.extra_data = {}

    def test_social_signup_form_valid(self):
        """Test valid social signup form."""
        form_data = {
            'role': User.CUSTOMER,
            'agree_to_terms': True,
        }
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=self.mock_sociallogin)
        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")

    def test_social_signup_terms_required(self):
        """Test that terms agreement is required for social signup."""
        form_data = {
            'role': User.CUSTOMER,
            'agree_to_terms': False,
        }
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=self.mock_sociallogin)
        self.assertFalse(form.is_valid())
        self.assertIn('agree_to_terms', form.errors)

    def test_social_signup_save_sets_role(self):
        """Test that social signup form save sets role attribute."""
        # Create a mock user for the sociallogin
        mock_user = User(email='<EMAIL>', role=User.CUSTOMER)
        self.mock_sociallogin.user = mock_user

        form_data = {
            'role': User.SERVICE_PROVIDER,
            'agree_to_terms': True,
        }
        form = CozyWishSocialSignupForm(data=form_data, sociallogin=self.mock_sociallogin)
        self.assertTrue(form.is_valid())

        request = self.factory.post('/accounts/social/signup/')
        user = form.save(request)

        # Check that role attribute is set on form
        self.assertEqual(form.role, User.SERVICE_PROVIDER)
        # Check that user role was updated
        self.assertEqual(user.role, User.SERVICE_PROVIDER)


class CozyWishLoginFormTests(TestCase):
    """Test cases for CozyWishLoginForm."""
    
    def setUp(self):
        """Set up test dependencies."""
        self.factory = RequestFactory()
        # Create test users
        self.active_customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123!',
            role=User.CUSTOMER,
            is_active=True
        )
        self.inactive_provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123!',
            role=User.SERVICE_PROVIDER,
            is_active=False
        )
    
    def test_login_form_styling(self):
        """Test that login form has proper styling."""
        form = CozyWishLoginForm()
        
        # Check that login field has proper attributes
        login_widget = form.fields['login'].widget
        self.assertIn('form-control', login_widget.attrs.get('class', ''))
        self.assertEqual(form.fields['login'].label, 'Email Address')
        
        # Check that password field has proper attributes
        password_widget = form.fields['password'].widget
        self.assertIn('form-control', password_widget.attrs.get('class', ''))
    
    @patch('allauth.account.forms.LoginForm.clean')
    def test_login_form_inactive_provider_message(self, mock_parent_clean):
        """Test custom message for inactive service provider."""
        # Mock parent clean to set the user
        mock_parent_clean.return_value = {}
        
        form_data = {
            'login': '<EMAIL>',
            'password': 'testpass123!',
        }
        form = CozyWishLoginForm(data=form_data)
        form.user = self.inactive_provider
        
        with self.assertRaises(ValidationError) as cm:
            form.clean()
        
        self.assertIn('verify your email', str(cm.exception))
