"""
Tests for Django Allauth URL integration and conflict resolution.

This module tests the URL routing between custom authentication URLs
and django-allauth URLs to ensure proper precedence and no conflicts.
"""

from django.test import TestCase, Client
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.messages.middleware import MessageMiddleware
from django.http import HttpRequest

from accounts_app.models import CustomUser, CustomerProfile, ServiceProviderProfile


User = get_user_model()


class AllauthURLIntegrationTest(TestCase):
    """Test URL integration between custom auth and allauth."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=self.customer)
        
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Provider',
            phone='+**********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='TS',
            zip_code='12345'
        )
    
    def test_url_namespacing(self):
        """Test that URL namespacing works correctly."""
        # Test custom URLs with namespace
        customer_signup_url = reverse('accounts_app:customer_signup')
        self.assertEqual(customer_signup_url, '/accounts/customer/signup/')
        
        provider_signup_url = reverse('accounts_app:service_provider_signup')
        self.assertEqual(provider_signup_url, '/accounts/provider/signup/')
        
        # Test allauth integration URLs
        customer_allauth_url = reverse('accounts_app:customer_signup_allauth')
        self.assertEqual(customer_allauth_url, '/accounts/customer/signup/allauth/')
        
        provider_allauth_url = reverse('accounts_app:provider_signup_allauth')
        self.assertEqual(provider_allauth_url, '/accounts/provider/signup/allauth/')
    
    def test_url_precedence(self):
        """Test that custom URLs take precedence over allauth URLs."""
        # Test that specific custom URLs are resolved correctly
        resolver = resolve('/accounts/customer/signup/')
        self.assertEqual(resolver.view_name, 'accounts_app:customer_signup')
        
        resolver = resolve('/accounts/provider/signup/')
        self.assertEqual(resolver.view_name, 'accounts_app:service_provider_signup')
        
        # Test that allauth URLs are still accessible
        resolver = resolve('/accounts/login/')
        self.assertEqual(resolver.view_name, 'account_login')
        
        resolver = resolve('/accounts/signup/')
        self.assertEqual(resolver.view_name, 'account_signup')
    
    def test_role_based_signup_redirects(self):
        """Test that role-based signup views redirect to allauth correctly."""
        # Test customer signup redirect
        response = self.client.get('/accounts/customer/signup/allauth/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/signup/', response.url)
        
        # Test provider signup redirect
        response = self.client.get('/accounts/provider/signup/allauth/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/signup/', response.url)
    
    def test_role_based_login_redirects(self):
        """Test that role-based login views redirect to allauth correctly."""
        # Test customer login redirect
        response = self.client.get('/accounts/customer/login/allauth/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
        
        # Test provider login redirect
        response = self.client.get('/accounts/provider/login/allauth/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
    
    def test_authenticated_user_redirects(self):
        """Test that authenticated users are redirected appropriately."""
        # Test customer redirect
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/customer/signup/allauth/')
        self.assertEqual(response.status_code, 302)
        # Should redirect to customer profile or dashboard
        
        self.client.logout()
        
        # Test provider redirect
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/provider/signup/allauth/')
        self.assertEqual(response.status_code, 302)
        # Should redirect to provider profile or dashboard
    
    def test_session_role_setting(self):
        """Test that role is properly set in session for allauth adapter."""
        # Test customer signup sets role in session
        response = self.client.get('/accounts/customer/signup/allauth/')
        session = self.client.session
        self.assertEqual(session.get('signup_role'), User.CUSTOMER)
        
        # Test provider signup sets role in session
        response = self.client.get('/accounts/provider/signup/allauth/')
        session = self.client.session
        self.assertEqual(session.get('signup_role'), User.SERVICE_PROVIDER)
    
    def test_post_auth_redirect_view(self):
        """Test the post-authentication redirect view."""
        # Test unauthenticated access
        response = self.client.get('/accounts/redirect/post-auth/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
        
        # Test authenticated customer
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/redirect/post-auth/')
        self.assertEqual(response.status_code, 302)
        # Should redirect to customer dashboard
        
        self.client.logout()
        
        # Test authenticated provider
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/redirect/post-auth/')
        self.assertEqual(response.status_code, 302)
        # Should redirect to provider dashboard
    
    def test_role_switch_redirect_view(self):
        """Test the role switch redirect view."""
        # Test unauthenticated access
        response = self.client.get('/accounts/redirect/role-switch/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)
        
        # Test authenticated users get appropriate redirects
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/redirect/role-switch/')
        self.assertEqual(response.status_code, 302)
        
        self.client.logout()
        
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/redirect/role-switch/')
        self.assertEqual(response.status_code, 302)
    
    def test_backward_compatibility(self):
        """Test that existing URLs still work."""
        # Test legacy customer URLs
        response = self.client.get('/accounts/customer/signup/')
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get('/accounts/customer/login/')
        self.assertEqual(response.status_code, 200)
        
        # Test legacy provider URLs
        response = self.client.get('/accounts/provider/signup/')
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get('/accounts/provider/login/')
        self.assertEqual(response.status_code, 200)
    
    def test_allauth_urls_accessible(self):
        """Test that allauth URLs are still accessible."""
        # Test allauth login
        response = self.client.get('/accounts/login/')
        self.assertEqual(response.status_code, 200)
        
        # Test allauth signup
        response = self.client.get('/accounts/signup/')
        self.assertEqual(response.status_code, 200)
        
        # Test allauth logout
        response = self.client.get('/accounts/logout/')
        self.assertEqual(response.status_code, 200)


class URLConflictResolutionTest(TestCase):
    """Test URL conflict resolution between custom and allauth URLs."""
    
    def test_no_url_conflicts(self):
        """Test that there are no URL conflicts."""
        # List of custom URLs that should not conflict with allauth
        custom_urls = [
            '/accounts/for-business/',
            '/accounts/customer/signup/',
            '/accounts/customer/login/',
            '/accounts/provider/signup/',
            '/accounts/provider/login/',
            '/accounts/customer/profile/',
            '/accounts/provider/profile/',
        ]
        
        # List of allauth URLs that should be accessible
        allauth_urls = [
            '/accounts/login/',
            '/accounts/signup/',
            '/accounts/logout/',
            '/accounts/password/reset/',
            '/accounts/password/change/',
        ]
        
        # Test that all URLs resolve correctly
        for url in custom_urls:
            try:
                resolver = resolve(url)
                self.assertIsNotNone(resolver)
            except Exception as e:
                self.fail(f"Custom URL {url} failed to resolve: {e}")
        
        for url in allauth_urls:
            try:
                resolver = resolve(url)
                self.assertIsNotNone(resolver)
            except Exception as e:
                self.fail(f"Allauth URL {url} failed to resolve: {e}")


class UnifiedLoginIntegrationTest(TestCase):
    """Test unified login integration between allauth and custom flows."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )
        CustomerProfile.objects.create(user=self.customer)

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )
        ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Provider',
            phone='+**********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='TS',
            zip_code='12345'
        )

    def test_unified_login_redirect_view(self):
        """Test the unified login redirect view."""
        # Test unauthenticated access
        response = self.client.get('/accounts/login/unified/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)

        # Test with next parameter
        response = self.client.get('/accounts/login/unified/?next=/dashboard/')
        self.assertEqual(response.status_code, 302)
        session = self.client.session
        self.assertEqual(session.get('login_next_url'), '/dashboard/')

    def test_enhanced_unified_login_view(self):
        """Test the enhanced unified login view."""
        # Test GET request
        response = self.client.get('/accounts/login/enhanced/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)

        # Test POST request
        response = self.client.post('/accounts/login/enhanced/', {'login': 'test'})
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url)

    def test_unified_logout_integration_view(self):
        """Test the unified logout integration view."""
        # Test unauthenticated access
        response = self.client.get('/accounts/logout/unified/')
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/')

        # Test authenticated customer
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get('/accounts/logout/unified/')
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/logout/', response.url)

        # Check session context
        session = self.client.session
        self.assertEqual(session.get('logout_user_type'), 'customer')
        self.assertEqual(session.get('logout_user_email'), '<EMAIL>')

    def test_authenticated_user_redirects_unified(self):
        """Test that authenticated users are redirected appropriately in unified views."""
        # Test customer
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.get('/accounts/login/unified/')
        self.assertEqual(response.status_code, 302)
        # Should redirect to post-auth redirect

        response = self.client.get('/accounts/login/enhanced/')
        self.assertEqual(response.status_code, 302)
        # Should redirect to post-auth redirect

        self.client.logout()

        # Test provider
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.get('/accounts/login/unified/')
        self.assertEqual(response.status_code, 302)

        response = self.client.get('/accounts/login/enhanced/')
        self.assertEqual(response.status_code, 302)

    def test_session_context_preservation(self):
        """Test that session context is properly preserved in unified views."""
        # Test login source tracking
        response = self.client.get('/accounts/login/unified/')
        session = self.client.session
        self.assertEqual(session.get('login_source'), 'unified_redirect')

        response = self.client.get('/accounts/login/enhanced/')
        session = self.client.session
        self.assertEqual(session.get('login_source'), 'unified_enhanced')

    def test_security_measures_maintained(self):
        """Test that security measures are maintained in unified views."""
        # Test CSRF protection (views should have @csrf_protect)
        # Test cache prevention (views should have @never_cache)

        # Test that views handle security properly
        response = self.client.get('/accounts/login/unified/')
        self.assertEqual(response.status_code, 302)

        response = self.client.get('/accounts/login/enhanced/')
        self.assertEqual(response.status_code, 302)

        response = self.client.get('/accounts/logout/unified/')
        self.assertEqual(response.status_code, 302)

    def test_backward_compatibility_with_unified_views(self):
        """Test that unified views maintain backward compatibility."""
        # Test that existing functionality still works
        response = self.client.get('/accounts/customer/login/')
        self.assertEqual(response.status_code, 200)

        response = self.client.get('/accounts/provider/login/')
        self.assertEqual(response.status_code, 200)

        # Test that new unified views work
        response = self.client.get('/accounts/login/unified/')
        self.assertEqual(response.status_code, 302)

        response = self.client.get('/accounts/login/enhanced/')
        self.assertEqual(response.status_code, 302)
