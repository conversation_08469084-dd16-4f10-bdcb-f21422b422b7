# --- Standard Library Imports ---
from unittest.mock import Mock, patch

# --- Django Imports ---
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model

# --- Third-party Imports ---
import pytest
from allauth.socialaccount.models import SocialAccount, SocialApp
from allauth.socialaccount import app_settings

# --- Local App Imports ---
from accounts_app.allauth_adapters import CozyWishAccountAdapter, CozyWishSocialAccountAdapter
from accounts_app.models import CustomerProfile, ServiceProviderProfile
from accounts_app.forms import CustomerSignupForm, ServiceProviderSignupForm

# --- Setup ---
User = get_user_model()


class CozyWishAccountAdapterTests(TestCase):
    """Test cases for CozyWishAccountAdapter."""
    
    def setUp(self):
        """Set up test dependencies."""
        self.factory = RequestFactory()
        self.adapter = CozyWishAccountAdapter()
    
    def test_determine_user_role_from_url_customer(self):
        """Test role determination from customer URL."""
        request = self.factory.get('/accounts/customer/signup/')
        form = Mock()
        form.cleaned_data = {}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.CUSTOMER)

    def test_determine_user_role_from_url_provider(self):
        """Test role determination from provider URL."""
        request = self.factory.get('/accounts/provider/signup/')
        form = Mock()
        form.cleaned_data = {}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.SERVICE_PROVIDER)

    def test_determine_user_role_from_form_data(self):
        """Test role determination from form data."""
        request = self.factory.get('/accounts/signup/')
        form = Mock()
        form.cleaned_data = {'role': User.SERVICE_PROVIDER}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.SERVICE_PROVIDER)

    def test_determine_user_role_default(self):
        """Test default role determination."""
        request = self.factory.get('/accounts/signup/')
        form = Mock()
        form.cleaned_data = {}
        # Remove the role attribute from the mock
        del form.role

        role = self.adapter._determine_user_role(request, form)
        self.assertEqual(role, User.CUSTOMER)
    
    def test_save_user_customer(self):
        """Test saving customer user with profile creation."""
        request = self.factory.get('/accounts/customer/signup/')
        user = User(email='<EMAIL>')

        # Create a simple form-like object instead of Mock
        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'first_name': 'John',
                    'last_name': 'Doe',
                    'phone_number': '+**********'
                }

        form = SimpleForm()

        saved_user = self.adapter.save_user(request, user, form, commit=True)

        self.assertEqual(saved_user.role, User.CUSTOMER)
        self.assertTrue(saved_user.is_active)
        self.assertTrue(hasattr(saved_user, 'customer_profile'))
        self.assertEqual(saved_user.customer_profile.first_name, 'John')
        self.assertEqual(saved_user.customer_profile.last_name, 'Doe')

    def test_save_user_service_provider(self):
        """Test saving service provider user with profile creation."""
        request = self.factory.get('/accounts/provider/signup/')
        user = User(email='<EMAIL>', first_name='Jane', last_name='Smith')

        # Create a simple form-like object instead of Mock
        class SimpleForm:
            def __init__(self):
                self.cleaned_data = {
                    'email': '<EMAIL>',
                    'business_name': 'Test Business',
                    'business_phone_number': '+**********',
                    'contact_person_name': 'Jane Smith',
                    'business_address': '123 Test St',
                    'city': 'Test City',
                    'state': 'CA',
                    'zip_code': '12345'
                }

        form = SimpleForm()

        saved_user = self.adapter.save_user(request, user, form, commit=True)

        self.assertEqual(saved_user.role, User.SERVICE_PROVIDER)
        self.assertFalse(saved_user.is_active)  # Requires email verification
        self.assertTrue(hasattr(saved_user, 'service_provider_profile'))
        self.assertEqual(saved_user.service_provider_profile.legal_name, 'Test Business')
        self.assertEqual(saved_user.service_provider_profile.phone, '+**********')
    
    def test_clean_email(self):
        """Test email cleaning functionality."""
        email = '  <EMAIL>  '
        cleaned = self.adapter.clean_email(email)
        self.assertEqual(cleaned, '<EMAIL>')
    
    def test_get_login_redirect_url_customer(self):
        """Test login redirect for customer."""
        user = User.objects.create_user('<EMAIL>', 'password', role=User.CUSTOMER)
        request = self.factory.get('/')
        request.user = user
        
        redirect_url = self.adapter.get_login_redirect_url(request)
        self.assertEqual(redirect_url, '/dashboard/')
    
    def test_get_login_redirect_url_service_provider(self):
        """Test login redirect for service provider."""
        user = User.objects.create_user('<EMAIL>', 'password', role=User.SERVICE_PROVIDER)
        request = self.factory.get('/')
        request.user = user
        
        redirect_url = self.adapter.get_login_redirect_url(request)
        self.assertEqual(redirect_url, '/dashboard/')
    
    def test_get_login_redirect_url_admin(self):
        """Test login redirect for admin."""
        user = User.objects.create_user('<EMAIL>', 'password', role=User.ADMIN)
        request = self.factory.get('/')
        request.user = user
        
        redirect_url = self.adapter.get_login_redirect_url(request)
        self.assertEqual(redirect_url, '/admin-panel/')


class CozyWishSocialAccountAdapterTests(TestCase):
    """Test cases for CozyWishSocialAccountAdapter."""
    
    def setUp(self):
        """Set up test dependencies."""
        self.factory = RequestFactory()
        self.adapter = CozyWishSocialAccountAdapter()
    
    def test_determine_social_user_role_default(self):
        """Test default social user role determination."""
        request = self.factory.get('/accounts/social/signup/')
        sociallogin = Mock()
        data = {'email': '<EMAIL>', 'name': 'John Doe'}
        
        role = self.adapter._determine_social_user_role(request, sociallogin, data)
        self.assertEqual(role, User.CUSTOMER)
    
    def test_determine_social_user_role_business_url(self):
        """Test social user role determination from business URL."""
        request = self.factory.get('/accounts/provider/social/signup/')
        sociallogin = Mock()
        data = {'email': '<EMAIL>', 'name': 'John Doe'}
        
        role = self.adapter._determine_social_user_role(request, sociallogin, data)
        self.assertEqual(role, User.SERVICE_PROVIDER)
    
    def test_is_business_account_by_name(self):
        """Test business account detection by name."""
        sociallogin = Mock()
        data = {'name': 'ABC Business Services', 'email': '<EMAIL>'}
        
        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertTrue(is_business)
    
    def test_is_business_account_by_email(self):
        """Test business account detection by email domain."""
        sociallogin = Mock()
        data = {'name': 'John Doe', 'email': '<EMAIL>'}
        
        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertTrue(is_business)
    
    def test_is_business_account_personal(self):
        """Test personal account detection."""
        sociallogin = Mock()
        data = {'name': 'John Doe', 'email': '<EMAIL>'}
        
        is_business = self.adapter._is_business_account(sociallogin, data)
        self.assertFalse(is_business)
    
    def test_extract_social_user_data(self):
        """Test extracting user data from social providers."""
        user = User(email='<EMAIL>')
        sociallogin = Mock()
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'name': 'John Doe'
        }
        
        self.adapter._extract_social_user_data(user, sociallogin, data)
        
        self.assertEqual(user.first_name, 'John')
        self.assertEqual(user.last_name, 'Doe')
    
    def test_extract_social_user_data_full_name_only(self):
        """Test extracting user data when only full name is available."""
        user = User(email='<EMAIL>')
        sociallogin = Mock()
        data = {'name': 'John Michael Doe'}
        
        self.adapter._extract_social_user_data(user, sociallogin, data)
        
        self.assertEqual(user.first_name, 'John')
        self.assertEqual(user.last_name, 'Michael Doe')
    
    @patch('accounts_app.allauth_adapters.CustomerProfile.objects.create')
    def test_create_social_customer_profile(self, mock_create):
        """Test creating customer profile from social data."""
        user = User(email='<EMAIL>', first_name='John', last_name='Doe')
        extra_data = {'gender': 'male'}
        
        mock_profile = Mock()
        mock_create.return_value = mock_profile
        
        profile = self.adapter._create_social_customer_profile(user, extra_data)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        self.assertEqual(call_args[1]['first_name'], 'John')
        self.assertEqual(call_args[1]['last_name'], 'Doe')
        self.assertEqual(call_args[1]['gender'], 'M')
    
    @patch('accounts_app.allauth_adapters.ServiceProviderProfile.objects.create')
    def test_create_social_service_provider_profile(self, mock_create):
        """Test creating service provider profile from social data."""
        user = User(email='<EMAIL>', first_name='Jane', last_name='Smith')
        extra_data = {'company': 'Test Company', 'website': 'https://testcompany.com'}
        
        mock_profile = Mock()
        mock_create.return_value = mock_profile
        
        profile = self.adapter._create_social_service_provider_profile(user, extra_data)
        
        mock_create.assert_called_once()
        call_args = mock_create.call_args
        self.assertEqual(call_args[1]['legal_name'], 'Test Company')
        self.assertEqual(call_args[1]['website'], 'https://testcompany.com')
        self.assertEqual(call_args[1]['contact_name'], 'Jane Smith')
