# --- Standard Library Imports ---
from decimal import Decimal
from unittest import mock

# --- Third-Party Imports ---
import pytest
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.test import TestCase

# --- Local App Imports ---
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
    LoginHistory,
)


class CustomUserEdgeCaseTests(TestCase):
    """Edge case tests for CustomUser model."""

    def test_create_user_with_empty_email(self):
        """Test creating user with empty email raises error."""
        with pytest.raises(ValueError, match="The Email field must be set"):
            CustomUser.objects.create_user('', 'password123')

    def test_create_user_with_none_email(self):
        """Test creating user with None email raises error."""
        with pytest.raises(ValueError, match="The Email field must be set"):
            CustomUser.objects.create_user(None, 'password123')

    def test_create_user_with_whitespace_email(self):
        """Test creating user with whitespace email gets normalized."""
        # Whitespace emails get normalized by Django's normalize_email
        user = CustomUser.objects.create_user('   <EMAIL>   ', 'password123')
        assert user.email == '<EMAIL>'

    def test_create_user_with_invalid_email_format(self):
        """Test creating user with invalid email format raises error."""
        with pytest.raises(ValidationError):
            user = CustomUser(email='invalid-email', password='password123')
            user.full_clean()

    def test_create_user_with_extremely_long_email(self):
        """Test creating user with extremely long email raises error."""
        long_email = 'a' * 250 + '@example.com'  # Over 254 character limit
        with pytest.raises(ValidationError):
            user = CustomUser(email=long_email, password='password123')
            user.full_clean()

    def test_create_duplicate_email_users(self):
        """Test creating users with duplicate emails raises error."""
        CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        with pytest.raises(IntegrityError):
            CustomUser.objects.create_user('<EMAIL>', 'password456')

    def test_create_user_with_case_insensitive_duplicate_email(self):
        """Test creating users with case-insensitive duplicate emails."""
        CustomUser.objects.create_user('<EMAIL>', 'password123')

        # Our enhanced model normalizes the entire email to lowercase for allauth compatibility
        # This should raise an IntegrityError because the normalized email already exists
        with pytest.raises(IntegrityError):
            CustomUser.objects.create_user('<EMAIL>', 'password456')

    def test_user_role_validation(self):
        """Test user role validation with invalid role."""
        with pytest.raises(ValidationError):
            user = CustomUser(
                email='<EMAIL>',
                password='password123',
                role='INVALID_ROLE'
            )
            user.full_clean()

    def test_user_string_representation_with_long_email(self):
        """Test user string representation with very long email."""
        long_email = '<EMAIL>'
        user = CustomUser.objects.create_user(long_email, 'password123')
        assert str(user) == long_email

    def test_user_is_active_default(self):
        """Test user is_active field defaults to True."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        assert user.is_active is True

    def test_user_date_joined_auto_now_add(self):
        """Test user date_joined is automatically set."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        assert user.date_joined is not None

    def test_user_last_login_initially_none(self):
        """Test user last_login is initially None."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        assert user.last_login is None


class CustomerProfileEdgeCaseTests(TestCase):
    """Edge case tests for CustomerProfile model."""

    def test_customer_profile_with_duplicate_user(self):
        """Test creating customer profile with duplicate user raises error."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        CustomerProfile.objects.create(user=user)
        
        with pytest.raises(IntegrityError):
            CustomerProfile.objects.create(user=user)

    def test_customer_profile_with_service_provider_user(self):
        """Test creating customer profile with service provider user."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        
        # Should be allowed but might cause logical issues
        profile = CustomerProfile.objects.create(user=user)
        assert profile.user.role == CustomUser.SERVICE_PROVIDER

    def test_customer_profile_phone_validation_invalid_format(self):
        """Test customer profile phone validation with invalid format."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        
        with pytest.raises(ValidationError):
            profile = CustomerProfile(user=user, phone_number='123')  # Invalid phone
            profile.full_clean()

    def test_customer_profile_phone_validation_too_long(self):
        """Test customer profile phone validation with too long number."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        
        with pytest.raises(ValidationError):
            profile = CustomerProfile(user=user, phone_number='+1' + '1' * 20)  # Too long
            profile.full_clean()

    def test_customer_profile_with_none_user(self):
        """Test customer profile creation with None user raises error."""
        with pytest.raises(IntegrityError):
            CustomerProfile.objects.create(user=None)

    def test_customer_profile_string_representation_with_no_name(self):
        """Test customer profile string representation when user has no name fields."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        profile = CustomerProfile.objects.create(user=user)
        
        # Should fall back to email
        assert '<EMAIL>' in str(profile)

    def test_customer_profile_address_max_length(self):
        """Test customer profile address field max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        
        with pytest.raises(ValidationError):
            profile = CustomerProfile(user=user, address='A' * 256)  # Over max_length=255
            profile.full_clean()

    def test_customer_profile_city_max_length(self):
        """Test customer profile city field max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        
        with pytest.raises(ValidationError):
            profile = CustomerProfile(user=user, city='A' * 101)  # Over max_length=100
            profile.full_clean()

    def test_customer_profile_first_name_max_length(self):
        """Test customer profile first name field max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        
        with pytest.raises(ValidationError):
            profile = CustomerProfile(user=user, first_name='A' * 101)  # Over max_length=100
            profile.full_clean()

    def test_customer_profile_zip_code_max_length(self):
        """Test customer profile zip code max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.CUSTOMER)
        
        with pytest.raises(ValidationError):
            profile = CustomerProfile(user=user, zip_code='A' * 11)  # Over max_length=10
            profile.full_clean()


class ServiceProviderProfileEdgeCaseTests(TestCase):
    """Edge case tests for ServiceProviderProfile model."""

    def test_service_provider_profile_with_duplicate_user(self):
        """Test creating service provider profile with duplicate user raises error."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        with pytest.raises(IntegrityError):
            ServiceProviderProfile.objects.create(
                user=user,
                legal_name='Another Business',
                phone='+***********',
                contact_name='Another Contact',
                address='456 Another St',
                city='Another City',
                state='NY',
                zip_code='67890'
            )

    def test_service_provider_profile_legal_name_max_length(self):
        """Test service provider profile legal name max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        
        with pytest.raises(ValidationError):
            profile = ServiceProviderProfile(
                user=user,
                legal_name='A' * 201,  # Over max_length=200
                phone='+***********',
                contact_name='Test Contact',
                address='123 Test St',
                city='Test City',
                state='CA',
                zip_code='12345'
            )
            profile.full_clean()

    def test_service_provider_profile_phone_validation_invalid(self):
        """Test service provider profile phone validation with invalid format."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        
        with pytest.raises(ValidationError):
            profile = ServiceProviderProfile(
                user=user,
                legal_name='Test Business',
                phone='123',  # Invalid phone
                contact_name='Test Contact',
                address='123 Test St',
                city='Test City',
                state='CA',
                zip_code='12345'
            )
            profile.full_clean()

    def test_service_provider_profile_contact_name_max_length(self):
        """Test service provider profile contact name max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        
        with pytest.raises(ValidationError):
            profile = ServiceProviderProfile(
                user=user,
                legal_name='Test Business',
                phone='+***********',
                contact_name='A' * 101,  # Over max_length=100
                address='123 Test St',
                city='Test City',
                state='CA',
                zip_code='12345'
            )
            profile.full_clean()

    def test_service_provider_profile_zip_code_max_length(self):
        """Test service provider profile zip code max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        
        with pytest.raises(ValidationError):
            profile = ServiceProviderProfile(
                user=user,
                legal_name='Test Business',
                phone='+***********',
                contact_name='Test Contact',
                address='123 Test St',
                city='Test City',
                state='CA',
                zip_code='A' * 11  # Over max_length=10
            )
            profile.full_clean()

    def test_service_provider_profile_is_public_default(self):
        """Test service provider profile is_public field defaults to True."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        assert profile.is_public is True

    def test_service_provider_profile_created_auto_now_add(self):
        """Test service provider profile created is automatically set."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        assert profile.created is not None

    def test_service_provider_profile_updated_auto_now(self):
        """Test service provider profile updated is automatically updated."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        original_updated = profile.updated
        
        # Update the profile
        profile.legal_name = 'Updated Business'
        profile.save()
        
        assert profile.updated > original_updated


class TeamMemberEdgeCaseTests(TestCase):
    """Edge case tests for TeamMember model."""

    def test_team_member_max_count_enforcement(self):
        """Test team member max count enforcement."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        # Create max number of team members
        max_count = TeamMember.max_count()
        for i in range(max_count):
            TeamMember.objects.create(
                service_provider=profile,
                name=f'Team Member {i}',
                position='Staff',
                is_active=True
            )
        
        # Try to create one more - should be allowed by default but might have business logic validation
        team_member = TeamMember.objects.create(
            service_provider=profile,
            name='Extra Member',
            position='Extra Staff',
            is_active=True
        )
        assert team_member.id is not None

    def test_team_member_name_max_length(self):
        """Test team member name max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        with pytest.raises(ValidationError):
            team_member = TeamMember(
                service_provider=profile,
                name='A' * 101,  # Over max_length=100
                position='Staff',
                is_active=True
            )
            team_member.full_clean()

    def test_team_member_position_max_length(self):
        """Test team member position max length validation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        with pytest.raises(ValidationError):
            team_member = TeamMember(
                service_provider=profile,
                name='John Doe',
                position='A' * 101,  # Over max_length=100
                is_active=True
            )
            team_member.full_clean()

    def test_team_member_with_none_service_provider(self):
        """Test team member creation with None service provider raises error."""
        with pytest.raises(IntegrityError):
            TeamMember.objects.create(
                service_provider=None,
                name='John Doe',
                position='Staff',
                is_active=True
            )

    def test_team_member_is_active_default(self):
        """Test team member is_active field defaults to True."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        team_member = TeamMember.objects.create(
            service_provider=profile,
            name='John Doe',
            position='Staff'
        )
        assert team_member.is_active is True

    def test_team_member_string_representation(self):
        """Test team member string representation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        team_member = TeamMember.objects.create(
            service_provider=profile,
            name='John Doe',
            position='Massage Therapist',
            is_active=True
        )
        
        # Check the actual string representation format
        expected_str = f"{team_member.name} ({team_member.position})"
        assert str(team_member) == expected_str

    def test_team_member_created_auto_now_add(self):
        """Test team member created is automatically set."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        team_member = TeamMember.objects.create(
            service_provider=profile,
            name='John Doe',
            position='Staff',
            is_active=True
        )
        assert team_member.created is not None


class LoginHistoryEdgeCaseTests(TestCase):
    """Edge case tests for LoginHistory model."""

    def test_login_history_with_none_user(self):
        """Test login history creation with None user raises error."""
        with pytest.raises(IntegrityError):
            LoginHistory.objects.create(
                user=None,
                ip_address='***********',
                user_agent='Test Browser',
                is_successful=True
            )

    def test_login_history_ip_address_max_length(self):
        """Test login history IP address with very long IPv6 address."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        # Test with valid IPv6 address (should work)
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='2001:0db8:85a3:0000:0000:8a2e:0370:7334',
            user_agent='Test Browser',
            is_successful=True
        )
        assert login_history.ip_address == '2001:0db8:85a3:0000:0000:8a2e:0370:7334'

    def test_login_history_user_agent_very_long(self):
        """Test login history with very long user agent."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        long_user_agent = 'A' * 500  # Very long user agent
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='***********',
            user_agent=long_user_agent,
            is_successful=True
        )
        assert login_history.user_agent == long_user_agent

    def test_login_history_timestamp_auto_now_add(self):
        """Test login history timestamp is automatically set."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='***********',
            user_agent='Test Browser',
            is_successful=True
        )
        assert login_history.timestamp is not None

    def test_login_history_string_representation(self):
        """Test login history string representation."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='***********',
            user_agent='Test Browser',
            is_successful=True
        )
        
        expected_str = f"{user.email} @ {login_history.timestamp}"
        assert str(login_history) == expected_str

    def test_login_history_ipv6_address(self):
        """Test login history with IPv6 address."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='2001:0db8:85a3:0000:0000:8a2e:0370:7334',
            user_agent='Test Browser',
            is_successful=True
        )
        assert login_history.ip_address == '2001:0db8:85a3:0000:0000:8a2e:0370:7334'

    def test_login_history_empty_user_agent(self):
        """Test login history with empty user agent."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123')
        
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='***********',
            user_agent='',  # Empty user agent
            is_successful=True
        )
        assert login_history.user_agent == ''


class ModelConstraintTests(TestCase):
    """Tests for database constraints and integrity."""

    def test_cascade_delete_behavior(self):
        """Test cascade delete behavior for related models."""
        user = CustomUser.objects.create_user('<EMAIL>', 'password123', role=CustomUser.SERVICE_PROVIDER)
        profile = ServiceProviderProfile.objects.create(
            user=user,
            legal_name='Test Business',
            phone='+***********',
            contact_name='Test Contact',
            address='123 Test St',
            city='Test City',
            state='CA',
            zip_code='12345'
        )
        
        # Create team member
        team_member = TeamMember.objects.create(
            service_provider=profile,
            name='John Doe',
            position='Staff',
            is_active=True
        )
        
        # Create login history
        login_history = LoginHistory.objects.create(
            user=user,
            ip_address='***********',
            user_agent='Test Browser',
            is_successful=True
        )
        
        # Delete user should cascade to related objects
        user.delete()
        
        # Check that related objects were deleted
        assert not ServiceProviderProfile.objects.filter(id=profile.id).exists()
        assert not TeamMember.objects.filter(id=team_member.id).exists()
        assert not LoginHistory.objects.filter(id=login_history.id).exists()

    def test_unique_constraint_violation_with_transaction(self):
        """Test unique constraint violation within transaction."""
        with pytest.raises(IntegrityError):
            with transaction.atomic():
                CustomUser.objects.create_user('<EMAIL>', 'password123')
                CustomUser.objects.create_user('<EMAIL>', 'password456') 