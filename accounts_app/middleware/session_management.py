# --- Django Imports ---
from django.contrib.auth import logout
from django.contrib.sessions.models import Session
from django.http import JsonResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
import logging
from datetime import timedelta

# --- Local App Imports ---
from ..models.session_management import UserSession, SessionSecurityEvent
from ..utils.session_utils import (
    get_device_info,
    get_location_info,
    is_suspicious_login,
    should_warn_timeout,
)

logger = logging.getLogger(__name__)


class EnhancedSessionMiddleware(MiddlewareMixin):
    """
    Enhanced session management middleware.
    
    Features:
    - Session tracking and metadata
    - Timeout warnings
    - Concurrent session management
    - Security monitoring
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming request for session management."""
        if not request.user.is_authenticated:
            return None
        
        # Get or create user session record
        user_session = self._get_or_create_user_session(request)
        
        if user_session:
            # Check if session is expired
            if user_session.is_expired:
                self._handle_expired_session(request, user_session)
                return None
            
            # Update session activity
            user_session.update_activity()
            
            # Check for timeout warning
            if should_warn_timeout(user_session):
                request.session['session_timeout_warning'] = True
            
            # Store session info in request
            request.user_session = user_session
        
        return None
    
    def process_response(self, request, response):
        """Process response for session management."""
        # Add session timeout warning to AJAX responses
        if (hasattr(request, 'user') and 
            request.user.is_authenticated and 
            request.session.get('session_timeout_warning')):
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # Add timeout warning to AJAX responses
                if hasattr(response, 'json') and response.get('Content-Type') == 'application/json':
                    try:
                        import json
                        data = json.loads(response.content)
                        data['session_timeout_warning'] = True
                        response.content = json.dumps(data)
                    except (json.JSONDecodeError, AttributeError):
                        pass
        
        return response
    
    def _get_or_create_user_session(self, request):
        """Get or create UserSession record for current session."""
        try:
            session_key = request.session.session_key
            if not session_key:
                return None
            
            # Try to get existing session
            try:
                user_session = UserSession.objects.get(
                    session_key=session_key,
                    user=request.user,
                    is_active=True
                )
                return user_session
            except UserSession.DoesNotExist:
                pass
            
            # Create new session record
            device_info = get_device_info(request)
            location_info = get_location_info(request)
            
            # Check for remember me
            is_remember_me = request.session.get('remember_me', False)
            
            # Set expiry time
            if is_remember_me:
                expires_at = timezone.now() + timedelta(days=30)
            else:
                expires_at = timezone.now() + timedelta(hours=8)
            
            user_session = UserSession.objects.create(
                user=request.user,
                session_key=session_key,
                device_name=device_info.get('device_name', ''),
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                location=location_info.get('location', ''),
                is_remember_me=is_remember_me,
                expires_at=expires_at
            )
            
            # Log session creation
            SessionSecurityEvent.log_event(
                'login_success',
                user=request.user,
                session=user_session,
                request=request,
                device_info=device_info,
                location_info=location_info
            )
            
            # Check for suspicious activity
            if is_suspicious_login(request.user, request):
                SessionSecurityEvent.log_event(
                    'suspicious_location',
                    user=request.user,
                    session=user_session,
                    request=request,
                    is_suspicious=True
                )
            
            return user_session
            
        except Exception as e:
            logger.error(
                "Error in session management",
                extra={
                    'user_id': request.user.id if request.user.is_authenticated else None,
                    'error': str(e),
                    'event': 'session_management_error'
                }
            )
            return None
    
    def _handle_expired_session(self, request, user_session):
        """Handle expired session."""
        # Terminate the session
        user_session.terminate('timeout')
        
        # Log timeout event
        SessionSecurityEvent.log_event(
            'session_timeout',
            user=request.user,
            session=user_session,
            request=request
        )
        
        # Logout user
        logout(request)
        
        # Add message about timeout
        request.session['timeout_message'] = (
            "Your session has expired for security reasons. Please sign in again."
        )


class ConcurrentSessionMiddleware(MiddlewareMixin):
    """
    Middleware to handle concurrent session limits.
    
    Features:
    - Limit concurrent sessions per user
    - Terminate oldest sessions when limit exceeded
    - Security notifications for concurrent logins
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.max_sessions = getattr(settings, 'MAX_CONCURRENT_SESSIONS', 5)
        super().__init__(get_response)
    
    def process_request(self, request):
        """Check for concurrent session limits."""
        if not request.user.is_authenticated:
            return None
        
        # Get active sessions for user
        active_sessions = UserSession.get_active_sessions_for_user(request.user)
        
        if active_sessions.count() > self.max_sessions:
            # Terminate oldest sessions
            sessions_to_terminate = active_sessions[self.max_sessions:]
            
            for session in sessions_to_terminate:
                session.terminate('security')
                
                # Log security termination
                SessionSecurityEvent.log_event(
                    'security_termination',
                    user=request.user,
                    session=session,
                    request=request,
                    reason='concurrent_session_limit'
                )
            
            # Log concurrent login event
            SessionSecurityEvent.log_event(
                'concurrent_login',
                user=request.user,
                request=request,
                active_sessions_count=active_sessions.count()
            )
        
        return None


class SessionTimeoutWarningMiddleware(MiddlewareMixin):
    """
    Middleware to provide session timeout warnings.
    
    Features:
    - JavaScript-based timeout warnings
    - Automatic session extension
    - Graceful timeout handling
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.warning_time = getattr(settings, 'SESSION_TIMEOUT_WARNING_MINUTES', 5)
        super().__init__(get_response)
    
    def process_request(self, request):
        """Add timeout warning data to request."""
        if not request.user.is_authenticated:
            return None
        
        user_session = getattr(request, 'user_session', None)
        if not user_session:
            return None
        
        # Calculate time until timeout
        time_until_expiry = user_session.time_until_expiry
        if time_until_expiry:
            total_seconds = int(time_until_expiry.total_seconds())
            warning_seconds = self.warning_time * 60
            
            # Add timeout info to session
            request.session['session_timeout_info'] = {
                'expires_in': total_seconds,
                'warning_at': max(0, total_seconds - warning_seconds),
                'extend_url': reverse('accounts_app:extend_session'),
            }
        
        return None


class RememberMeMiddleware(MiddlewareMixin):
    """
    Middleware to handle remember me functionality.
    
    Features:
    - Extended session duration for remember me
    - Secure cookie handling
    - Automatic session extension
    """
    
    def process_request(self, request):
        """Handle remember me functionality."""
        if not request.user.is_authenticated:
            return None
        
        # Check if this is a remember me session
        is_remember_me = request.session.get('remember_me', False)
        
        if is_remember_me:
            # Extend session expiry
            user_session = getattr(request, 'user_session', None)
            if user_session and user_session.is_remember_me:
                # Check if we need to extend the session
                time_until_expiry = user_session.time_until_expiry
                if time_until_expiry and time_until_expiry.total_seconds() < (7 * 24 * 3600):  # Less than 7 days
                    user_session.extend_expiry(hours=24 * 30)  # Extend to 30 days
        
        return None
