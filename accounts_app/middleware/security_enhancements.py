# --- Django Imports ---
from django.contrib.auth import logout
from django.core.cache import cache
from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.urls import reverse
import logging
import hashlib
import json
from datetime import timedelta
from ipaddress import ip_address, ip_network

# --- Local App Imports ---
from ..models.session_management import SessionSecurityEvent
from ..utils.security_utils import (
    is_suspicious_request,
    get_client_ip,
    log_security_event,
    check_rate_limit,
    is_bot_request,
)

logger = logging.getLogger(__name__)


class SecurityEnhancementMiddleware(MiddlewareMixin):
    """
    Comprehensive security middleware for authentication flows.
    
    Features:
    - Request fingerprinting
    - Suspicious activity detection
    - Rate limiting integration
    - Security logging
    - Bot detection
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.protected_paths = [
            '/accounts/login/',
            '/accounts/signup/',
            '/accounts/password/reset/',
            '/accounts/email/verification/',
        ]
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming request for security checks."""
        # Skip security checks for certain paths
        if self._should_skip_security_check(request):
            return None
        
        # Get client information
        client_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Create request fingerprint
        request_fingerprint = self._create_request_fingerprint(request)
        request.security_fingerprint = request_fingerprint
        
        # Check for bot requests
        if is_bot_request(request):
            request.is_bot_request = True
            # Apply stricter rate limiting for bots
            if self._is_protected_path(request.path):
                return self._handle_bot_request(request)
        
        # Check for suspicious activity
        if is_suspicious_request(request):
            self._log_suspicious_activity(request, client_ip, user_agent)
            
            # Apply additional security measures for suspicious requests
            if self._is_protected_path(request.path):
                return self._handle_suspicious_request(request)
        
        # Check rate limits for protected paths
        if self._is_protected_path(request.path):
            rate_limit_result = check_rate_limit(request)
            if rate_limit_result['blocked']:
                return self._handle_rate_limit_exceeded(request, rate_limit_result)
        
        return None
    
    def process_response(self, request, response):
        """Process response for security enhancements."""
        # Add security headers
        response = self._add_security_headers(request, response)
        
        # Log authentication events
        if hasattr(request, 'user') and request.user.is_authenticated:
            self._log_authentication_event(request, response)
        
        return response
    
    def _should_skip_security_check(self, request):
        """Check if security checks should be skipped for this request."""
        # Skip for static files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return True
        
        # Skip for admin paths (they have their own security)
        if request.path.startswith('/admin/'):
            return True
        
        # Skip for API health checks
        if request.path in ['/health/', '/ping/', '/status/']:
            return True
        
        return False
    
    def _is_protected_path(self, path):
        """Check if path is protected and needs enhanced security."""
        return any(path.startswith(protected) for protected in self.protected_paths)
    
    def _create_request_fingerprint(self, request):
        """Create a unique fingerprint for the request."""
        fingerprint_data = {
            'ip': get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'accept_language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
            'accept_encoding': request.META.get('HTTP_ACCEPT_ENCODING', ''),
        }
        
        fingerprint_string = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.sha256(fingerprint_string.encode()).hexdigest()[:16]
    
    def _handle_bot_request(self, request):
        """Handle requests identified as bots."""
        # Log bot request
        log_security_event(
            'bot_request_detected',
            request=request,
            details={'user_agent': request.META.get('HTTP_USER_AGENT', '')}
        )
        
        # Apply stricter rate limiting
        cache_key = f"bot_rate_limit:{get_client_ip(request)}"
        request_count = cache.get(cache_key, 0)
        
        if request_count >= 5:  # 5 requests per hour for bots
            return HttpResponseForbidden("Rate limit exceeded for automated requests")
        
        cache.set(cache_key, request_count + 1, 3600)  # 1 hour
        return None
    
    def _handle_suspicious_request(self, request):
        """Handle suspicious requests."""
        # For now, just log and continue
        # In production, you might want to add CAPTCHA or additional verification
        return None
    
    def _handle_rate_limit_exceeded(self, request, rate_limit_result):
        """Handle rate limit exceeded."""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'retry_after': rate_limit_result.get('retry_after', 60),
                'message': 'Too many requests. Please try again later.'
            }, status=429)
        
        # Redirect to rate limit page
        return redirect('accounts_app:rate_limit_exceeded')
    
    def _log_suspicious_activity(self, request, client_ip, user_agent):
        """Log suspicious activity."""
        log_security_event(
            'suspicious_request',
            request=request,
            details={
                'ip': client_ip,
                'user_agent': user_agent,
                'path': request.path,
                'method': request.method,
            }
        )
    
    def _add_security_headers(self, request, response):
        """Add security headers to response."""
        # Only add headers for HTML responses
        content_type = response.get('Content-Type', '')
        if not content_type.startswith('text/html'):
            return response
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSP header for authentication pages
        if self._is_protected_path(request.path):
            response['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'"
            )
        
        return response
    
    def _log_authentication_event(self, request, response):
        """Log authentication-related events."""
        # Only log for authentication paths
        if not self._is_protected_path(request.path):
            return
        
        # Determine event type based on path and response
        event_type = None
        if '/login/' in request.path and response.status_code == 302:
            event_type = 'login_attempt'
        elif '/signup/' in request.path and response.status_code == 302:
            event_type = 'signup_attempt'
        elif '/password/reset/' in request.path:
            event_type = 'password_reset_attempt'
        
        if event_type:
            log_security_event(
                event_type,
                user=request.user if request.user.is_authenticated else None,
                request=request,
                details={
                    'status_code': response.status_code,
                    'fingerprint': getattr(request, 'security_fingerprint', ''),
                }
            )


class CSRFEnhancementMiddleware(MiddlewareMixin):
    """
    Enhanced CSRF protection middleware.
    
    Features:
    - Additional CSRF validation
    - Custom error handling
    - Logging of CSRF failures
    """
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """Enhanced CSRF validation."""
        # Skip for non-POST requests
        if request.method != 'POST':
            return None
        
        # Skip for API endpoints that use other authentication
        if request.path.startswith('/api/'):
            return None
        
        # Additional CSRF validation for sensitive operations
        if self._is_sensitive_operation(request):
            return self._validate_enhanced_csrf(request)
        
        return None
    
    def _is_sensitive_operation(self, request):
        """Check if this is a sensitive operation requiring enhanced CSRF protection."""
        sensitive_paths = [
            '/accounts/password/change/',
            '/accounts/email/change/',
            '/accounts/delete/',
            '/accounts/sessions/terminate/',
            '/accounts/social/unlink/',
        ]
        
        return any(request.path.startswith(path) for path in sensitive_paths)
    
    def _validate_enhanced_csrf(self, request):
        """Perform enhanced CSRF validation."""
        # Check for additional CSRF token in headers
        csrf_header = request.META.get('HTTP_X_CSRF_TOKEN')
        csrf_form = request.POST.get('csrfmiddlewaretoken')
        
        if not csrf_header or not csrf_form or csrf_header != csrf_form:
            log_security_event(
                'csrf_validation_failed',
                request=request,
                details={
                    'path': request.path,
                    'has_header_token': bool(csrf_header),
                    'has_form_token': bool(csrf_form),
                    'tokens_match': csrf_header == csrf_form if csrf_header and csrf_form else False,
                }
            )
            
            # Return custom CSRF error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'CSRF validation failed',
                    'message': 'Security token validation failed. Please refresh the page and try again.'
                }, status=403)
        
        return None


class LoginAttemptTrackingMiddleware(MiddlewareMixin):
    """
    Middleware to track and limit login attempts.
    
    Features:
    - Track failed login attempts
    - Progressive delays
    - Account lockout protection
    - Suspicious pattern detection
    """
    
    def process_response(self, request, response):
        """Track login attempts based on response."""
        # Only track login attempts
        if not request.path.endswith('/login/') or request.method != 'POST':
            return response
        
        client_ip = get_client_ip(request)
        email = request.POST.get('email', '').lower().strip()
        
        # Determine if login was successful
        is_success = response.status_code == 302  # Redirect indicates success
        
        if is_success:
            self._handle_successful_login(request, client_ip, email)
        else:
            self._handle_failed_login(request, client_ip, email)
        
        return response
    
    def _handle_successful_login(self, request, client_ip, email):
        """Handle successful login attempt."""
        # Clear failed attempt counters
        cache.delete(f"failed_login_ip:{client_ip}")
        cache.delete(f"failed_login_email:{email}")
        
        # Log successful login
        log_security_event(
            'login_success',
            request=request,
            details={
                'email': email,
                'ip': client_ip,
            }
        )
    
    def _handle_failed_login(self, request, client_ip, email):
        """Handle failed login attempt."""
        # Increment failure counters
        ip_key = f"failed_login_ip:{client_ip}"
        email_key = f"failed_login_email:{email}"
        
        ip_failures = cache.get(ip_key, 0) + 1
        email_failures = cache.get(email_key, 0) + 1
        
        # Set counters with expiry
        cache.set(ip_key, ip_failures, 3600)  # 1 hour
        cache.set(email_key, email_failures, 3600)  # 1 hour
        
        # Log failed login
        log_security_event(
            'login_failed',
            request=request,
            details={
                'email': email,
                'ip': client_ip,
                'ip_failures': ip_failures,
                'email_failures': email_failures,
            }
        )
        
        # Check for suspicious patterns
        if ip_failures >= 10 or email_failures >= 5:
            log_security_event(
                'suspicious_login_pattern',
                request=request,
                details={
                    'email': email,
                    'ip': client_ip,
                    'ip_failures': ip_failures,
                    'email_failures': email_failures,
                },
                is_suspicious=True
            )
