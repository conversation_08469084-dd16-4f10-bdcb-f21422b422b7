{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Bookings Management - CozyWish{% endblock %}

{% block dashboard_title %}Bookings Management{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-calendar-day"></i> Today's Bookings
    </a>
    <a href="{% url 'booking_cart_app:provider_availability_list' %}" class="btn btn-sm btn-outline-secondary">
        <i class="fas fa-clock"></i> Manage Availability
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item active" aria-current="page">Bookings Management</li>
    </ol>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>Bookings Management</h4>
                    <p class="mb-0 text-muted">Manage customer bookings for your venue</p>
                </div>
                <div class="card-body">
                    {% if bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Customer</th>
                                        <th>Date</th>
                                        <th>Services</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in bookings %}
                                    <tr>
                                        <td>
                                            <strong>{{ booking.booking_id|truncatechars:8 }}</strong>
                                            <br>
                                            <small class="text-muted">{{ booking.created_at|date:"M d, Y" }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ booking.customer.get_full_name|default:booking.customer.email }}</strong>
                                            <br>
                                            <small class="text-muted">{{ booking.customer.email }}</small>
                                        </td>
                                        <td>
                                            {{ booking.booking_date|date:"F d, Y" }}
                                            <br>
                                            <small class="text-muted">
                                                {% for item in booking.items.all|slice:":1" %}
                                                    {{ item.selected_time_slot }}
                                                {% endfor %}
                                            </small>
                                        </td>
                                        <td>
                                            {% for item in booking.items.all %}
                                                <div>{{ item.service.service_title }} ({{ item.quantity }})</div>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            <span class="badge badge-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <strong>${{ booking.total_price }}</strong>
                                        </td>
                                        <td>
                                            <a href="{% url 'booking_cart_app:provider_booking_detail' booking.slug %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                            {% if booking.status == 'pending' %}
                                            <a href="{% url 'booking_cart_app:provider_accept_booking' booking.slug %}" 
                                               class="btn btn-sm btn-success ml-1">
                                                Accept
                                            </a>
                                            <a href="{% url 'booking_cart_app:provider_decline_booking' booking.slug %}" 
                                               class="btn btn-sm btn-danger ml-1">
                                                Decline
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5>No bookings yet</h5>
                            <p class="text-muted">You don't have any bookings yet. Customers will see your services once they're active.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
