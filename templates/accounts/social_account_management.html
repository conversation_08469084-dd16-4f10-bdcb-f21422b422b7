{% extends "base.html" %}
{% load static %}
{% load socialaccount %}

{% block title %}Social Account Management - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .social-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .social-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .social-header {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .social-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
    }
    
    .social-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }
    
    .social-subtitle {
        color: #525252;
        font-size: 1rem;
        margin: 0;
    }
    
    .provider-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .provider-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        position: relative;
    }
    
    .provider-card.connected {
        border-color: #28a745;
        background: #f8fff9;
    }
    
    .provider-card.connected::before {
        content: "Connected";
        position: absolute;
        top: -10px;
        right: 1rem;
        background: #28a745;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .provider-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .provider-logo {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.2rem;
        color: white;
    }
    
    .provider-google { background: #db4437; }
    .provider-facebook { background: #3b5998; }
    .provider-twitter { background: #1da1f2; }
    .provider-linkedin { background: #0077b5; }
    
    .provider-name {
        color: #2F160F;
        font-weight: 600;
        font-size: 1.1rem;
        margin: 0;
    }
    
    .provider-status {
        color: #525252;
        font-size: 0.9rem;
        margin: 0;
    }
    
    .provider-details {
        margin: 1rem 0;
        font-size: 0.9rem;
        color: #525252;
    }
    
    .provider-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .btn-connect {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-connect:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-sync {
        background: #ffc107;
        color: #212529;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-sync:hover {
        background: #e0a800;
        transform: translateY(-1px);
    }
    
    .btn-unlink {
        background: #dc3545;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-unlink:hover {
        background: #c82333;
        transform: translateY(-1px);
    }
    
    .conflicts-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .conflicts-section h3 {
        color: #856404;
        font-family: 'Poppins', sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .conflict-item {
        background: white;
        border: 1px solid #fed7aa;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .conflict-item:last-child {
        margin-bottom: 0;
    }
    
    .conflict-type {
        font-weight: 600;
        color: #92400e;
        margin-bottom: 0.5rem;
    }
    
    .conflict-details {
        color: #92400e;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .btn-resolve {
        background: #f59e0b;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-resolve:hover {
        background: #d97706;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .security-info {
        background: #e7f3ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
    }
    
    .security-info h4 {
        color: #0369a1;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .security-info ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .security-info li {
        padding: 0.5rem 0;
        color: #0369a1;
        position: relative;
        padding-left: 1.5rem;
    }
    
    .security-info li:before {
        content: "🔒";
        position: absolute;
        left: 0;
        top: 0.5rem;
    }
    
    .alert {
        border-radius: 8px;
        border: none;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
    }
    
    .alert-warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .alert-danger {
        background: #f8d7da;
        color: #721c24;
    }
    
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="social-container">
    <div class="social-card">
        <div class="social-header">
            <div class="social-icon">
                🔗
            </div>
            <div>
                <h1 class="social-title">Social Account Management</h1>
                <p class="social-subtitle">Connect and manage your social media accounts</p>
            </div>
        </div>
        
        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        
        <!-- Conflicts Section -->
        {% if conflicts %}
            <div class="conflicts-section">
                <h3>⚠️ Account Conflicts Detected</h3>
                <p>We found some conflicts between your social accounts and your CozyWish account that need your attention.</p>
                
                {% for conflict in conflicts %}
                    <div class="conflict-item">
                        <div class="conflict-type">
                            {% if conflict.type == 'email_conflict' %}
                                Email Address Conflict
                            {% endif %}
                        </div>
                        <div class="conflict-details">
                            {% if conflict.type == 'email_conflict' %}
                                Your {{ conflict.provider|title }} account uses email <strong>{{ conflict.social_email }}</strong>, 
                                but your CozyWish account uses <strong>{{ user.email }}</strong>.
                            {% endif %}
                        </div>
                        <a href="{% url 'accounts_app:social_account_conflict_resolution' conflict.account.id %}?type={{ conflict.type }}" 
                           class="btn-resolve">
                            Resolve Conflict
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        <!-- Connected Accounts -->
        <h2 style="color: #2F160F; font-family: 'Poppins', sans-serif; font-size: 1.3rem; margin-bottom: 1rem;">
            Your Connected Accounts
        </h2>
        
        <div class="provider-grid">
            {% for provider in available_providers %}
                <div class="provider-card {% if provider.is_connected %}connected{% endif %}">
                    <div class="provider-header">
                        <div class="provider-logo provider-{{ provider.id }}">
                            {% if provider.id == 'google' %}🔍
                            {% elif provider.id == 'facebook' %}📘
                            {% elif provider.id == 'twitter' %}🐦
                            {% elif provider.id == 'linkedin' %}💼
                            {% else %}🔗{% endif %}
                        </div>
                        <div>
                            <h3 class="provider-name">{{ provider.name }}</h3>
                            <p class="provider-status">
                                {% if provider.is_connected %}
                                    Connected
                                {% else %}
                                    Not connected
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    {% if provider.is_connected %}
                        {% for account in social_accounts %}
                            {% if account.provider == provider.id %}
                                <div class="provider-details">
                                    <strong>Account:</strong> {{ account.extra_data.email|default:account.extra_data.name|default:"Unknown" }}<br>
                                    <strong>Connected:</strong> {{ account.date_joined|date:"M j, Y" }}<br>
                                    {% if account.extra_data.name %}
                                        <strong>Name:</strong> {{ account.extra_data.name }}<br>
                                    {% endif %}
                                </div>
                                
                                <div class="provider-actions">
                                    <button type="button" class="btn-sync" onclick="syncAccountData('{{ account.id }}')">
                                        🔄 Sync Data
                                    </button>
                                    {% if can_disconnect %}
                                        <button type="button" class="btn-unlink" onclick="unlinkAccount('{{ account.id }}', '{{ provider.name }}')">
                                            🔓 Unlink
                                        </button>
                                    {% endif %}
                                </div>
                                {% break %}
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        <div class="provider-details">
                            Connect your {{ provider.name }} account to enable quick sign-in and sync your profile information.
                        </div>
                        
                        <div class="provider-actions">
                            {% provider_login_url provider.id next="/accounts/social/" as login_url %}
                            <a href="{{ login_url }}" class="btn-connect">
                                🔗 Connect {{ provider.name }}
                            </a>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        
        <!-- Security Information -->
        <div class="security-info">
            <h4>🔒 Security & Privacy</h4>
            <ul>
                <li>We only access basic profile information from your social accounts</li>
                <li>You can unlink any social account at any time</li>
                <li>Your social account data is kept secure and private</li>
                <li>We never post to your social media without your explicit permission</li>
                <li>Linking accounts makes sign-in faster and more convenient</li>
            </ul>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // CSRF token for AJAX requests
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    window.unlinkAccount = function(accountId, providerName) {
        if (!confirm(`Are you sure you want to unlink your ${providerName} account?`)) {
            return;
        }
        
        fetch('{% url "accounts_app:unlink_social_account" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: `account_id=${accountId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Reload page to update UI
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message, 'danger');
            }
        })
        .catch(error => {
            showMessage('An error occurred while unlinking the account.', 'danger');
        });
    };
    
    window.syncAccountData = function(accountId) {
        // For now, sync all available fields
        const syncFields = ['first_name', 'last_name', 'profile_picture'];
        
        if (!confirm('This will update your profile with information from your social account. Continue?')) {
            return;
        }
        
        const formData = new FormData();
        formData.append('account_id', accountId);
        syncFields.forEach(field => formData.append('sync_fields', field));
        
        fetch('{% url "accounts_app:sync_social_account_data" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                if (data.updated_fields && data.updated_fields.length > 0) {
                    // Reload page to show updated information
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                }
            } else {
                showMessage(data.message, 'warning');
            }
        })
        .catch(error => {
            showMessage('An error occurred while syncing account data.', 'danger');
        });
    };
    
    function showMessage(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        const container = document.querySelector('.social-container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
});
</script>

{% csrf_token %}
{% endblock %}
