{% comment %}
Reusable authentication header component for CozyWish authentication forms

Usage:
{% include 'accounts/components/auth_header.html' with icon='fas fa-user' title='Welcome Back' subtitle='Sign in to your CozyWish account' %}

Parameters:
- icon: FontAwesome icon class (required)
- title: Main heading text (required)
- subtitle: Subtitle text (optional)
- header_style: Style variant ('default', 'minimal', 'gradient') - defaults to 'default'
- icon_style: Icon style ('circle', 'square', 'minimal') - defaults to 'circle'
- title_size: Title size ('small', 'medium', 'large') - defaults to 'medium'
- text_align: Text alignment ('left', 'center', 'right') - defaults to 'center'
- show_background: Whether to show gradient background (defaults to True)
{% endcomment %}

<div class="auth-header auth-header-{{ header_style|default:'default' }} text-{{ text_align|default:'center' }}">
    <div class="auth-header-content">
        {% if icon %}
            <div class="auth-icon auth-icon-{{ icon_style|default:'circle' }}">
                <i class="{{ icon }}"></i>
            </div>
        {% endif %}
        
        {% if title %}
            <h1 class="auth-title auth-title-{{ title_size|default:'medium' }}">{{ title }}</h1>
        {% endif %}
        
        {% if subtitle %}
            <p class="auth-subtitle">{{ subtitle }}</p>
        {% endif %}
    </div>
</div>

<style>
    /* Authentication Header Component Styles */
    .auth-header {
        padding: 3rem 3rem 2rem;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    /* Default header with gradient background */
    .auth-header-default {
        background: var(--cw-gradient-card-subtle);
    }

    /* Minimal header without background */
    .auth-header-minimal {
        background: white;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    /* Gradient header with stronger gradient */
    .auth-header-gradient {
        background: linear-gradient(135deg, var(--cw-accent-light) 0%, var(--cw-brand-accent) 100%);
    }

    .auth-header-content {
        position: relative;
        z-index: 1;
    }

    /* Icon Styles */
    .auth-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        background: white;
        border: 2px solid var(--cw-brand-primary);
    }

    .auth-icon i {
        color: var(--cw-brand-primary);
        font-size: 2rem;
    }

    /* Circle icon style */
    .auth-icon-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
    }

    /* Square icon style */
    .auth-icon-square {
        width: 80px;
        height: 80px;
        border-radius: 0.5rem;
    }

    /* Minimal icon style */
    .auth-icon-minimal {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 1px solid var(--cw-brand-primary);
    }

    .auth-icon-minimal i {
        font-size: 1.5rem;
    }

    /* Title Styles */
    .auth-title {
        color: var(--cw-brand-primary);
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
        font-family: var(--cw-font-heading);
    }

    .auth-title-small {
        font-size: 1.875rem;
    }

    .auth-title-medium {
        font-size: 2.5rem;
    }

    .auth-title-large {
        font-size: 3rem;
    }

    /* Subtitle Styles */
    .auth-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 0;
        line-height: 1.6;
        font-weight: 400;
    }

    /* Text Alignment */
    .text-left .auth-icon {
        margin-left: 0;
        margin-right: auto;
    }

    .text-right .auth-icon {
        margin-left: auto;
        margin-right: 0;
    }

    .text-center .auth-icon {
        margin-left: auto;
        margin-right: auto;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .auth-header {
            padding: 2.5rem 2.5rem 1.5rem;
        }

        .auth-title-small {
            font-size: 1.625rem;
        }

        .auth-title-medium {
            font-size: 2rem;
        }

        .auth-title-large {
            font-size: 2.5rem;
        }

        .auth-subtitle {
            font-size: 1rem;
        }
    }

    @media (max-width: 576px) {
        .auth-header {
            padding: 2rem 2rem 1.5rem;
        }

        .auth-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 1rem;
        }

        .auth-icon i {
            font-size: 1.5rem;
        }

        .auth-icon-minimal {
            width: 48px;
            height: 48px;
        }

        .auth-icon-minimal i {
            font-size: 1.25rem;
        }

        .auth-title-small {
            font-size: 1.5rem;
        }

        .auth-title-medium {
            font-size: 1.875rem;
        }

        .auth-title-large {
            font-size: 2.25rem;
        }

        .auth-subtitle {
            font-size: 0.95rem;
        }
    }

    /* Dark mode support (if needed) */
    @media (prefers-color-scheme: dark) {
        .auth-header-minimal {
            background: #1a1a1a;
            border-bottom-color: #333;
        }

        .auth-icon {
            background: #1a1a1a;
        }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .auth-icon {
            border-width: 3px;
        }

        .auth-header {
            border-bottom-width: 2px;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .auth-header {
            transition: none;
        }
    }
</style>

{% comment %}
Example usage in templates:

<!-- Basic usage -->
{% include 'accounts/components/auth_header.html' with icon='fas fa-user' title='Welcome Back' subtitle='Sign in to your CozyWish account' %}

<!-- Minimal style -->
{% include 'accounts/components/auth_header.html' with icon='fas fa-user-plus' title='Join CozyWish' subtitle='Create your account' header_style='minimal' %}

<!-- Large title with square icon -->
{% include 'accounts/components/auth_header.html' with icon='fas fa-store' title='Business Login' subtitle='Sign in to your business account' title_size='large' icon_style='square' %}

<!-- Left-aligned header -->
{% include 'accounts/components/auth_header.html' with icon='fas fa-lock' title='Reset Password' subtitle='Enter your email to reset your password' text_align='left' %}

<!-- Without subtitle -->
{% include 'accounts/components/auth_header.html' with icon='fas fa-sign-out-alt' title='Sign Out' %}

<!-- Gradient background -->
{% include 'accounts/components/auth_header.html' with icon='fas fa-envelope' title='Check Your Email' subtitle='We sent you a confirmation link' header_style='gradient' %}
{% endcomment %}
