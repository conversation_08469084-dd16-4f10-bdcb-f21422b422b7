{% comment %}
Form-level enhancements component

Provides:
- Form validation summary
- Auto-save indicator
- Step indicators
- Profile completion tracking
- Progressive enhancement setup

Usage:
{% form_enhancements form "ajax-validation,auto-save,step-indicator" %}
{% endcomment %}

{% load form_validation_tags %}

<!-- Form validation summary -->
<div class="form-validation-summary d-none" role="alert" aria-live="polite"></div>

{% if profile_completion %}
    <!-- Profile completion indicator -->
    <div class="profile-completion" data-profile-completion="true">
        <div class="completion-header">
            <h6 class="completion-title">Profile Completion</h6>
            <span class="completion-percentage">0%</span>
        </div>
        <div class="completion-bar">
            <div class="completion-fill" style="width: 0%"></div>
        </div>
        <p class="completion-message">Complete your profile to get the most out of CozyWish.</p>
    </div>
{% endif %}

{% if step_indicator %}
    <!-- Step indicator (will be populated by JavaScript) -->
    <ol class="form-steps" data-step-indicator="true"></ol>
{% endif %}

<!-- Auto-save indicator -->
{% if auto_save %}
    <div id="auto-save-indicator" class="auto-save-indicator">
        <i class="fas fa-save me-2"></i>
        <span class="auto-save-text">Saved</span>
    </div>
{% endif %}

<!-- Form validation configuration -->
{% form_validation_config form %}

<!-- Progressive enhancement attributes -->
<script type="application/json" id="form-enhancement-config">
{
    "features": {{ features|safe }},
    "ajax_validation": {{ ajax_validation|yesno:"true,false" }},
    "auto_save": {{ auto_save|yesno:"true,false" }},
    "step_indicator": {{ step_indicator|yesno:"true,false" }},
    "profile_completion": {{ profile_completion|yesno:"true,false" }}
}
</script>

<!-- Add enhanced form attributes -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (!form) return;
    
    const config = JSON.parse(document.getElementById('form-enhancement-config').textContent);
    
    // Add form-level attributes
    form.setAttribute('data-validation', 'enhanced');
    
    if (config.ajax_validation) {
        form.setAttribute('data-ajax-validation', 'true');
    }
    
    if (config.auto_save) {
        form.setAttribute('data-auto-save', 'true');
        form.setAttribute('data-auto-save-interval', '30000'); // 30 seconds
    }
    
    if (config.step_indicator) {
        form.setAttribute('data-step-form', 'true');
    }
    
    // Add enhanced submit handling
    form.setAttribute('data-enhanced-submit', 'true');
    
    // Add progressive enhancement class
    document.documentElement.classList.add('js-enhanced');
});
</script>
