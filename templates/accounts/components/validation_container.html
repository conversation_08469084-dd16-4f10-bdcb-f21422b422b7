{% comment %}
Validation container component for enhanced form fields

Provides:
- Validation feedback elements
- Password strength indicator
- Contextual help
- Progressive enhancement support

Usage:
{% validation_container field show_strength=True show_help=True %}
{% endcomment %}

<div class="field-container" data-field="{{ field.name }}">
    <!-- Main field input -->
    {{ field }}
    
    <!-- Validation feedback -->
    <div class="validation-feedback" role="alert" aria-live="polite"></div>
    <div class="valid-feedback"></div>
    
    {% if show_strength and validation_type == 'password' %}
        <!-- Password strength indicator -->
        <div class="password-strength-indicator mt-2">
            <div class="strength-bar">
                <div class="strength-fill"></div>
            </div>
            <div class="strength-text text-muted small">
                Enter a password to see strength
            </div>
            <div class="strength-requirements small text-muted mt-1">
                <div class="requirement" data-requirement="length">
                    <i class="fas fa-circle text-muted"></i> At least 8 characters
                </div>
                <div class="requirement" data-requirement="uppercase">
                    <i class="fas fa-circle text-muted"></i> One uppercase letter
                </div>
                <div class="requirement" data-requirement="lowercase">
                    <i class="fas fa-circle text-muted"></i> One lowercase letter
                </div>
                <div class="requirement" data-requirement="number">
                    <i class="fas fa-circle text-muted"></i> One number
                </div>
                <div class="requirement" data-requirement="special">
                    <i class="fas fa-circle text-muted"></i> One special character
                </div>
            </div>
        </div>
    {% endif %}
    
    {% if show_help and help_text %}
        <!-- Contextual help -->
        <div class="field-help">
            <button type="button" class="field-help-trigger" aria-label="Help">
                <i class="fas fa-question-circle"></i>
            </button>
            <div class="field-help-content" role="tooltip">
                {{ help_text }}
            </div>
        </div>
    {% endif %}
    
    <!-- Smart default feedback (will be added by JavaScript) -->
    <div class="smart-default-feedback"></div>
    
    <!-- Auto-complete dropdown (will be added by JavaScript) -->
    <div class="autocomplete-dropdown"></div>
    
    <!-- File upload progress (for file fields) -->
    {% if field.field.widget.input_type == 'file' %}
        <div class="upload-progress-container"></div>
        <div id="{{ field.name }}-preview" class="file-preview-container mt-2"></div>
    {% endif %}
</div>
