{% comment %}
Progressive disclosure component

Sets up conditional field visibility based on form selections.

Usage:
{% progressive_disclosure form.role ".business-fields" "service_provider" %}
{% endcomment %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const triggerField = document.querySelector('[name="{{ trigger_field.name }}"]');
    const targets = document.querySelectorAll('{{ target_selector }}');
    
    if (!triggerField || targets.length === 0) return;
    
    function handleDisclosure() {
        let shouldShow = false;
        
        if (triggerField.type === 'checkbox') {
            shouldShow = triggerField.checked;
        } else if (triggerField.type === 'radio') {
            const checkedRadio = document.querySelector('[name="{{ trigger_field.name }}"]:checked');
            shouldShow = checkedRadio && checkedRadio.value === '{{ trigger_value }}';
        } else if (triggerField.tagName === 'SELECT') {
            shouldShow = triggerField.value === '{{ trigger_value }}';
        } else {
            shouldShow = triggerField.value === '{{ trigger_value }}';
        }
        
        targets.forEach(target => {
            if (shouldShow) {
                target.style.display = '';
                target.classList.add('disclosure-visible');
                target.classList.remove('disclosure-hidden');
                
                // Enable fields inside
                const fields = target.querySelectorAll('input, select, textarea');
                fields.forEach(field => {
                    field.disabled = false;
                    if (field.hasAttribute('data-required-when-visible')) {
                        field.required = true;
                    }
                });
                
                // Animate in
                target.style.opacity = '0';
                target.style.transform = 'translateY(-10px)';
                
                requestAnimationFrame(() => {
                    target.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    target.style.opacity = '1';
                    target.style.transform = 'translateY(0)';
                });
            } else {
                target.classList.add('disclosure-hidden');
                target.classList.remove('disclosure-visible');
                
                // Disable fields inside
                const fields = target.querySelectorAll('input, select, textarea');
                fields.forEach(field => {
                    field.disabled = true;
                    field.required = false;
                });
                
                // Animate out
                target.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                target.style.opacity = '0';
                target.style.transform = 'translateY(-10px)';
                
                setTimeout(() => {
                    target.style.display = 'none';
                }, 300);
            }
        });
    }
    
    // Setup event listeners
    if (triggerField.type === 'checkbox' || triggerField.type === 'radio') {
        triggerField.addEventListener('change', handleDisclosure);
        
        // For radio buttons, listen to all radios with the same name
        if (triggerField.type === 'radio') {
            const allRadios = document.querySelectorAll('[name="{{ trigger_field.name }}"]');
            allRadios.forEach(radio => {
                radio.addEventListener('change', handleDisclosure);
            });
        }
    } else {
        triggerField.addEventListener('input', handleDisclosure);
    }
    
    // Initial state
    handleDisclosure();
});
</script>
