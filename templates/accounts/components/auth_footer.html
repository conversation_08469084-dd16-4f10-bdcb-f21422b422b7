{% comment %}
Reusable authentication footer component for CozyWish authentication forms

Usage:
{% include 'accounts/components/auth_footer.html' with show_links=True show_help=True %}

Parameters:
- show_links: Whether to show navigation links (defaults to True)
- show_help: Whether to show help/support links (defaults to True)
- show_branding: Whether to show CozyWish branding (defaults to True)
- custom_links: List of custom links to display
- footer_style: Style variant ('default', 'minimal', 'compact') - defaults to 'default'
- text_align: Text alignment ('left', 'center', 'right') - defaults to 'center'
{% endcomment %}

<div class="auth-footer auth-footer-{{ footer_style|default:'default' }} text-{{ text_align|default:'center' }}">
    {% if show_links|default:True %}
        <div class="auth-footer-links">
            <!-- Primary Navigation Links -->
            <div class="auth-footer-nav">
                {% url 'home' as home_url %}
                {% if home_url %}
                    <a href="{{ home_url }}" class="auth-footer-link">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                {% endif %}

                {% url 'about' as about_url %}
                {% if about_url %}
                    <a href="{{ about_url }}" class="auth-footer-link">
                        <i class="fas fa-info-circle me-1"></i>About
                    </a>
                {% endif %}

                {% url 'contact' as contact_url %}
                {% if contact_url %}
                    <a href="{{ contact_url }}" class="auth-footer-link">
                        <i class="fas fa-envelope me-1"></i>Contact
                    </a>
                {% endif %}
            </div>

            <!-- Authentication Links -->
            <div class="auth-footer-auth">
                {% if not user.is_authenticated %}
                    {% url 'account_login' as login_url %}
                    {% if login_url %}
                        <a href="{{ login_url }}" class="auth-footer-link">
                            <i class="fas fa-sign-in-alt me-1"></i>Sign In
                        </a>
                    {% endif %}

                    {% url 'account_signup' as signup_url %}
                    {% if signup_url %}
                        <a href="{{ signup_url }}" class="auth-footer-link">
                            <i class="fas fa-user-plus me-1"></i>Sign Up
                        </a>
                    {% endif %}
                {% else %}
                    {% url 'dashboard' as dashboard_url %}
                    {% if dashboard_url %}
                        <a href="{{ dashboard_url }}" class="auth-footer-link">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    {% endif %}

                    {% url 'account_logout' as logout_url %}
                    {% if logout_url %}
                        <a href="{{ logout_url }}" class="auth-footer-link">
                            <i class="fas fa-sign-out-alt me-1"></i>Sign Out
                        </a>
                    {% endif %}
                {% endif %}
            </div>

            <!-- Custom Links -->
            {% if custom_links %}
                <div class="auth-footer-custom">
                    {% for link in custom_links %}
                        <a href="{{ link.url }}" class="auth-footer-link">
                            {% if link.icon %}<i class="{{ link.icon }} me-1"></i>{% endif %}
                            {{ link.text }}
                        </a>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    {% endif %}

    {% if show_help|default:True %}
        <div class="auth-footer-help">
            <div class="auth-footer-help-links">
                {% url 'help' as help_url %}
                {% if help_url %}
                    <a href="{{ help_url }}" class="auth-footer-help-link">
                        <i class="fas fa-question-circle me-1"></i>Help Center
                    </a>
                {% endif %}

                {% url 'privacy' as privacy_url %}
                {% if privacy_url %}
                    <a href="{{ privacy_url }}" class="auth-footer-help-link">
                        <i class="fas fa-shield-alt me-1"></i>Privacy Policy
                    </a>
                {% endif %}

                {% url 'terms' as terms_url %}
                {% if terms_url %}
                    <a href="{{ terms_url }}" class="auth-footer-help-link">
                        <i class="fas fa-file-contract me-1"></i>Terms of Service
                    </a>
                {% endif %}
            </div>
        </div>
    {% endif %}

    {% if show_branding|default:True %}
        <div class="auth-footer-branding">
            <p class="auth-footer-brand-text">
                <i class="fas fa-heart text-danger me-1"></i>
                Made with love by <strong>CozyWish</strong>
            </p>
            <p class="auth-footer-copyright">
                © {% now "Y" %} CozyWish. All rights reserved.
            </p>
        </div>
    {% endif %}
</div>

<style>
    /* Authentication Footer Component Styles */
    .auth-footer {
        padding: 2rem 0 1rem;
        border-top: 1px solid var(--cw-neutral-200);
        margin-top: 2rem;
    }

    /* Default footer style */
    .auth-footer-default {
        background: var(--cw-accent-light);
    }

    /* Minimal footer style */
    .auth-footer-minimal {
        background: white;
        padding: 1.5rem 0 1rem;
    }

    /* Compact footer style */
    .auth-footer-compact {
        background: white;
        padding: 1rem 0 0.5rem;
        border-top: none;
    }

    /* Footer Links Section */
    .auth-footer-links {
        margin-bottom: 1.5rem;
    }

    .auth-footer-nav,
    .auth-footer-auth,
    .auth-footer-custom {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .auth-footer-link {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        font-size: 0.9rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
    }

    .auth-footer-link:hover {
        color: var(--cw-brand-light);
        background: rgba(47, 22, 15, 0.05);
        text-decoration: none;
    }

    /* Help Links Section */
    .auth-footer-help {
        margin-bottom: 1.5rem;
    }

    .auth-footer-help-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .auth-footer-help-link {
        color: var(--cw-neutral-600);
        text-decoration: none;
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
    }

    .auth-footer-help-link:hover {
        color: var(--cw-brand-primary);
        background: rgba(47, 22, 15, 0.05);
        text-decoration: none;
    }

    /* Branding Section */
    .auth-footer-branding {
        border-top: 1px solid var(--cw-neutral-200);
        padding-top: 1rem;
    }

    .auth-footer-brand-text {
        color: var(--cw-brand-primary);
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .auth-footer-copyright {
        color: var(--cw-neutral-600);
        font-size: 0.8rem;
        margin-bottom: 0;
    }

    /* Text Alignment */
    .text-left .auth-footer-nav,
    .text-left .auth-footer-auth,
    .text-left .auth-footer-custom,
    .text-left .auth-footer-help-links {
        justify-content: flex-start;
    }

    .text-right .auth-footer-nav,
    .text-right .auth-footer-auth,
    .text-right .auth-footer-custom,
    .text-right .auth-footer-help-links {
        justify-content: flex-end;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .auth-footer-nav,
        .auth-footer-auth,
        .auth-footer-custom,
        .auth-footer-help-links {
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .text-left .auth-footer-nav,
        .text-left .auth-footer-auth,
        .text-left .auth-footer-custom,
        .text-left .auth-footer-help-links {
            align-items: flex-start;
        }

        .text-right .auth-footer-nav,
        .text-right .auth-footer-auth,
        .text-right .auth-footer-custom,
        .text-right .auth-footer-help-links {
            align-items: flex-end;
        }
    }

    @media (max-width: 576px) {
        .auth-footer {
            padding: 1.5rem 0 1rem;
        }

        .auth-footer-compact {
            padding: 1rem 0 0.5rem;
        }

        .auth-footer-link,
        .auth-footer-help-link {
            font-size: 0.85rem;
        }

        .auth-footer-brand-text {
            font-size: 0.85rem;
        }

        .auth-footer-copyright {
            font-size: 0.75rem;
        }
    }

    /* Accessibility improvements */
    .auth-footer-link:focus,
    .auth-footer-help-link:focus {
        outline: 2px solid var(--cw-brand-primary);
        outline-offset: 2px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .auth-footer {
            border-top-width: 2px;
        }

        .auth-footer-branding {
            border-top-width: 2px;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .auth-footer-link,
        .auth-footer-help-link {
            transition: none;
        }
    }
</style>

{% comment %}
Example usage in templates:

<!-- Basic usage -->
{% include 'accounts/components/auth_footer.html' %}

<!-- Minimal style without help links -->
{% include 'accounts/components/auth_footer.html' with footer_style='minimal' show_help=False %}

<!-- Compact style with custom links -->
{% include 'accounts/components/auth_footer.html' with footer_style='compact' custom_links=my_custom_links %}

<!-- Left-aligned footer -->
{% include 'accounts/components/auth_footer.html' with text_align='left' %}

<!-- Only branding -->
{% include 'accounts/components/auth_footer.html' with show_links=False show_help=False %}

Custom links example:
{% with custom_links='[{"url": "/business/", "text": "For Business", "icon": "fas fa-store"}]'|from_json %}
    {% include 'accounts/components/auth_footer.html' with custom_links=custom_links %}
{% endwith %}
{% endcomment %}
