{% comment %}
Reusable form validation component for CozyWish authentication forms

Usage:
{% include 'accounts/components/form_errors.html' with form=form show_non_field_errors=True %}

Parameters:
- form: The form object to display errors for (required)
- show_non_field_errors: Whether to show non-field errors (defaults to True)
- show_field_errors: Whether to show individual field errors (defaults to True)
- error_style: Style variant ('default', 'minimal', 'compact', 'inline') - defaults to 'default'
- show_icons: Whether to show error icons (defaults to True)
- group_errors: Whether to group all errors together (defaults to False)
- max_errors: Maximum number of errors to show per field (optional)
{% endcomment %}

{% if form.errors %}
    <div class="auth-form-errors auth-form-errors-{{ error_style|default:'default' }}">
        
        {% if show_non_field_errors|default:True and form.non_field_errors %}
            <!-- Non-field errors (general form errors) -->
            <div class="auth-error-section auth-error-non-field">
                <div class="auth-error-header">
                    {% if show_icons|default:True %}
                        <i class="fas fa-exclamation-triangle auth-error-icon"></i>
                    {% endif %}
                    <h6 class="auth-error-title">Please correct the following errors:</h6>
                </div>
                <ul class="auth-error-list">
                    {% for error in form.non_field_errors %}
                        <li class="auth-error-item">{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}

        {% if show_field_errors|default:True and not group_errors %}
            <!-- Individual field errors -->
            {% for field in form %}
                {% if field.errors %}
                    <div class="auth-error-section auth-error-field" data-field="{{ field.name }}">
                        <div class="auth-error-header">
                            {% if show_icons|default:True %}
                                <i class="fas fa-exclamation-circle auth-error-icon"></i>
                            {% endif %}
                            <span class="auth-error-field-label">{{ field.label|default:field.name|title }}:</span>
                        </div>
                        <ul class="auth-error-list">
                            {% for error in field.errors %}
                                {% if not max_errors or forloop.counter <= max_errors %}
                                    <li class="auth-error-item">{{ error }}</li>
                                {% endif %}
                            {% endfor %}
                            {% if max_errors and field.errors|length > max_errors %}
                                <li class="auth-error-item auth-error-more">
                                    and {{ field.errors|length|add:"-"|add:max_errors }} more error{{ field.errors|length|add:"-"|add:max_errors|pluralize }}...
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                {% endif %}
            {% endfor %}
        {% endif %}

        {% if group_errors and show_field_errors|default:True %}
            <!-- Grouped field errors -->
            <div class="auth-error-section auth-error-grouped">
                <div class="auth-error-header">
                    {% if show_icons|default:True %}
                        <i class="fas fa-exclamation-triangle auth-error-icon"></i>
                    {% endif %}
                    <h6 class="auth-error-title">Form Validation Errors:</h6>
                </div>
                <ul class="auth-error-list">
                    {% for field in form %}
                        {% if field.errors %}
                            {% for error in field.errors %}
                                {% if not max_errors or forloop.counter <= max_errors %}
                                    <li class="auth-error-item">
                                        <strong>{{ field.label|default:field.name|title }}:</strong> {{ error }}
                                    </li>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>

    <style>
        /* Form Errors Component Styles */
        .auth-form-errors {
            margin-bottom: 1.5rem;
        }

        .auth-error-section {
            background: #fff5f5;
            border: 2px solid #dc3545;
            border-radius: 0.5rem;
            padding: 1rem 1.25rem;
            margin-bottom: 1rem;
        }

        .auth-error-section:last-child {
            margin-bottom: 0;
        }

        /* Error Header */
        .auth-error-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .auth-error-icon {
            color: #dc3545;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .auth-error-title {
            color: #721c24;
            font-size: 0.95rem;
            font-weight: 600;
            margin: 0;
            font-family: var(--cw-font-heading);
        }

        .auth-error-field-label {
            color: #721c24;
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Error List */
        .auth-error-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .auth-error-item {
            color: #721c24;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 0.25rem;
            padding-left: 1rem;
            position: relative;
        }

        .auth-error-item:last-child {
            margin-bottom: 0;
        }

        .auth-error-item::before {
            content: "•";
            color: #dc3545;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .auth-error-more {
            font-style: italic;
            opacity: 0.8;
        }

        /* Style Variants */
        
        /* Minimal style */
        .auth-form-errors-minimal .auth-error-section {
            background: transparent;
            border: 1px solid #dc3545;
            border-left: 4px solid #dc3545;
            padding: 0.75rem 1rem;
        }

        .auth-form-errors-minimal .auth-error-header {
            margin-bottom: 0.5rem;
        }

        /* Compact style */
        .auth-form-errors-compact .auth-error-section {
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.5rem;
        }

        .auth-form-errors-compact .auth-error-header {
            margin-bottom: 0.25rem;
        }

        .auth-form-errors-compact .auth-error-item {
            font-size: 0.85rem;
            margin-bottom: 0.125rem;
        }

        /* Inline style */
        .auth-form-errors-inline .auth-error-section {
            background: transparent;
            border: none;
            padding: 0;
            margin-bottom: 0.5rem;
        }

        .auth-form-errors-inline .auth-error-header {
            margin-bottom: 0.25rem;
        }

        .auth-form-errors-inline .auth-error-list {
            display: inline;
        }

        .auth-form-errors-inline .auth-error-item {
            display: inline;
            margin-right: 1rem;
            padding-left: 0;
        }

        .auth-form-errors-inline .auth-error-item::before {
            display: none;
        }

        .auth-form-errors-inline .auth-error-item::after {
            content: " | ";
            color: #dc3545;
        }

        .auth-form-errors-inline .auth-error-item:last-child::after {
            display: none;
        }

        /* Non-field errors styling */
        .auth-error-non-field {
            border-color: #dc3545;
            background: #fff5f5;
        }

        /* Field-specific errors styling */
        .auth-error-field {
            border-color: #dc3545;
            background: #fff8f8;
        }

        /* Grouped errors styling */
        .auth-error-grouped {
            border-color: #dc3545;
            background: #fff5f5;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .auth-error-section {
                padding: 0.75rem 1rem;
            }

            .auth-error-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .auth-error-title,
            .auth-error-field-label {
                font-size: 0.85rem;
            }

            .auth-error-item {
                font-size: 0.85rem;
            }
        }

        /* Accessibility improvements */
        .auth-error-section {
            role: alert;
            aria-live: polite;
        }

        .auth-error-list {
            role: list;
        }

        .auth-error-item {
            role: listitem;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .auth-error-section {
                border-width: 3px;
            }

            .auth-error-item::before {
                font-size: 1.2em;
            }
        }

        /* Print styles */
        @media print {
            .auth-form-errors {
                background: white !important;
                color: black !important;
                border: 2px solid black !important;
            }

            .auth-error-icon {
                display: none;
            }
        }
    </style>
{% endif %}

{% comment %}
Example usage in templates:

<!-- Basic usage -->
{% include 'accounts/components/form_errors.html' with form=form %}

<!-- Only non-field errors -->
{% include 'accounts/components/form_errors.html' with form=form show_field_errors=False %}

<!-- Minimal style -->
{% include 'accounts/components/form_errors.html' with form=form error_style='minimal' %}

<!-- Compact style without icons -->
{% include 'accounts/components/form_errors.html' with form=form error_style='compact' show_icons=False %}

<!-- Grouped errors -->
{% include 'accounts/components/form_errors.html' with form=form group_errors=True %}

<!-- Inline style -->
{% include 'accounts/components/form_errors.html' with form=form error_style='inline' %}

<!-- Limit errors per field -->
{% include 'accounts/components/form_errors.html' with form=form max_errors=2 %}

JavaScript integration:
// Scroll to first error
const firstError = document.querySelector('.auth-error-section');
if (firstError) {
    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Focus first invalid field
const firstInvalidField = document.querySelector('.form-control.is-invalid');
if (firstInvalidField) {
    firstInvalidField.focus();
}
{% endcomment %}
