{% comment %}
Reusable loading states component for CozyWish authentication forms

Usage:
{% include 'accounts/components/loading.html' with loading_type='spinner' size='medium' text='Signing you in...' %}

Parameters:
- loading_type: Type of loading indicator ('spinner', 'dots', 'pulse', 'skeleton') - defaults to 'spinner'
- size: Size of loading indicator ('small', 'medium', 'large') - defaults to 'medium'
- text: Loading text to display (optional)
- show_overlay: Whether to show full-screen overlay (defaults to False)
- color: Color variant ('primary', 'secondary', 'light') - defaults to 'primary'
- position: Position ('center', 'inline', 'button') - defaults to 'center'
{% endcomment %}

<div class="auth-loading auth-loading-{{ loading_type|default:'spinner' }} auth-loading-{{ size|default:'medium' }} auth-loading-{{ color|default:'primary' }} auth-loading-{{ position|default:'center' }}" 
     {% if show_overlay %}data-overlay="true"{% endif %}>
     
    {% if show_overlay %}
        <div class="auth-loading-overlay"></div>
    {% endif %}
    
    <div class="auth-loading-content">
        {% if loading_type == 'spinner' or not loading_type %}
            <!-- Spinner Loading -->
            <div class="auth-spinner">
                <div class="auth-spinner-circle"></div>
            </div>
        {% elif loading_type == 'dots' %}
            <!-- Dots Loading -->
            <div class="auth-dots">
                <div class="auth-dot"></div>
                <div class="auth-dot"></div>
                <div class="auth-dot"></div>
            </div>
        {% elif loading_type == 'pulse' %}
            <!-- Pulse Loading -->
            <div class="auth-pulse">
                <div class="auth-pulse-circle"></div>
            </div>
        {% elif loading_type == 'skeleton' %}
            <!-- Skeleton Loading -->
            <div class="auth-skeleton">
                <div class="auth-skeleton-line auth-skeleton-line-long"></div>
                <div class="auth-skeleton-line auth-skeleton-line-medium"></div>
                <div class="auth-skeleton-line auth-skeleton-line-short"></div>
            </div>
        {% endif %}
        
        {% if text %}
            <div class="auth-loading-text">{{ text }}</div>
        {% endif %}
    </div>
</div>

<style>
    /* Authentication Loading Component Styles */
    .auth-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    /* Overlay */
    .auth-loading[data-overlay="true"] {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
    }

    .auth-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(2px);
    }

    .auth-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 1;
    }

    /* Position variants */
    .auth-loading-center {
        min-height: 200px;
    }

    .auth-loading-inline {
        min-height: auto;
        padding: 1rem;
    }

    .auth-loading-button {
        min-height: auto;
        padding: 0.5rem;
    }

    /* Size variants */
    .auth-loading-small .auth-spinner,
    .auth-loading-small .auth-dots,
    .auth-loading-small .auth-pulse {
        width: 24px;
        height: 24px;
    }

    .auth-loading-medium .auth-spinner,
    .auth-loading-medium .auth-dots,
    .auth-loading-medium .auth-pulse {
        width: 40px;
        height: 40px;
    }

    .auth-loading-large .auth-spinner,
    .auth-loading-large .auth-dots,
    .auth-loading-large .auth-pulse {
        width: 60px;
        height: 60px;
    }

    /* Spinner Loading */
    .auth-spinner {
        position: relative;
        display: inline-block;
    }

    .auth-spinner-circle {
        width: 100%;
        height: 100%;
        border: 3px solid rgba(47, 22, 15, 0.1);
        border-top: 3px solid var(--cw-brand-primary);
        border-radius: 50%;
        animation: auth-spin 1s linear infinite;
    }

    @keyframes auth-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Dots Loading */
    .auth-dots {
        display: flex;
        gap: 0.25rem;
        align-items: center;
        justify-content: center;
    }

    .auth-dot {
        width: 8px;
        height: 8px;
        background: var(--cw-brand-primary);
        border-radius: 50%;
        animation: auth-bounce 1.4s ease-in-out infinite both;
    }

    .auth-dot:nth-child(1) { animation-delay: -0.32s; }
    .auth-dot:nth-child(2) { animation-delay: -0.16s; }
    .auth-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes auth-bounce {
        0%, 80%, 100% {
            transform: scale(0);
        }
        40% {
            transform: scale(1);
        }
    }

    /* Pulse Loading */
    .auth-pulse {
        position: relative;
        display: inline-block;
    }

    .auth-pulse-circle {
        width: 100%;
        height: 100%;
        background: var(--cw-brand-primary);
        border-radius: 50%;
        animation: auth-pulse 1.5s ease-in-out infinite;
    }

    @keyframes auth-pulse {
        0% {
            transform: scale(0);
            opacity: 1;
        }
        100% {
            transform: scale(1);
            opacity: 0;
        }
    }

    /* Skeleton Loading */
    .auth-skeleton {
        width: 200px;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .auth-skeleton-line {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        border-radius: 6px;
        animation: auth-skeleton 1.5s ease-in-out infinite;
    }

    .auth-skeleton-line-long { width: 100%; }
    .auth-skeleton-line-medium { width: 75%; }
    .auth-skeleton-line-short { width: 50%; }

    @keyframes auth-skeleton {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    /* Loading Text */
    .auth-loading-text {
        color: var(--cw-brand-primary);
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
        margin-top: 0.5rem;
    }

    /* Color variants */
    .auth-loading-primary .auth-spinner-circle {
        border-top-color: var(--cw-brand-primary);
    }

    .auth-loading-primary .auth-dot,
    .auth-loading-primary .auth-pulse-circle {
        background: var(--cw-brand-primary);
    }

    .auth-loading-primary .auth-loading-text {
        color: var(--cw-brand-primary);
    }

    .auth-loading-secondary .auth-spinner-circle {
        border-top-color: var(--cw-neutral-600);
    }

    .auth-loading-secondary .auth-dot,
    .auth-loading-secondary .auth-pulse-circle {
        background: var(--cw-neutral-600);
    }

    .auth-loading-secondary .auth-loading-text {
        color: var(--cw-neutral-600);
    }

    .auth-loading-light .auth-spinner-circle {
        border-top-color: white;
        border-color: rgba(255, 255, 255, 0.3);
    }

    .auth-loading-light .auth-dot,
    .auth-loading-light .auth-pulse-circle {
        background: white;
    }

    .auth-loading-light .auth-loading-text {
        color: white;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .auth-loading-large .auth-spinner,
        .auth-loading-large .auth-dots,
        .auth-loading-large .auth-pulse {
            width: 48px;
            height: 48px;
        }

        .auth-skeleton {
            width: 150px;
        }

        .auth-loading-text {
            font-size: 0.85rem;
        }
    }

    /* Accessibility improvements */
    .auth-loading {
        role: status;
        aria-live: polite;
    }

    .auth-loading-content {
        aria-label: "Loading";
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .auth-spinner-circle,
        .auth-dot,
        .auth-pulse-circle,
        .auth-skeleton-line {
            animation: none;
        }

        .auth-spinner-circle {
            border-top-color: var(--cw-brand-primary);
            transform: rotate(45deg);
        }

        .auth-dot {
            transform: scale(1);
        }

        .auth-pulse-circle {
            transform: scale(0.8);
            opacity: 0.6;
        }
    }

    /* Print styles */
    @media print {
        .auth-loading {
            display: none;
        }
    }
</style>

{% comment %}
Example usage in templates:

<!-- Basic spinner -->
{% include 'accounts/components/loading.html' %}

<!-- Large spinner with text -->
{% include 'accounts/components/loading.html' with size='large' text='Signing you in...' %}

<!-- Dots loading -->
{% include 'accounts/components/loading.html' with loading_type='dots' text='Please wait...' %}

<!-- Pulse loading -->
{% include 'accounts/components/loading.html' with loading_type='pulse' size='small' %}

<!-- Skeleton loading -->
{% include 'accounts/components/loading.html' with loading_type='skeleton' %}

<!-- Full-screen overlay -->
{% include 'accounts/components/loading.html' with show_overlay=True text='Processing your request...' %}

<!-- Inline loading -->
{% include 'accounts/components/loading.html' with position='inline' size='small' text='Loading...' %}

<!-- Button loading -->
{% include 'accounts/components/loading.html' with position='button' size='small' color='light' %}

<!-- Secondary color -->
{% include 'accounts/components/loading.html' with color='secondary' text='Loading data...' %}

JavaScript integration:
// Show loading
document.querySelector('.auth-loading').style.display = 'flex';

// Hide loading
document.querySelector('.auth-loading').style.display = 'none';

// Show overlay loading
document.body.insertAdjacentHTML('beforeend', '{% include "accounts/components/loading.html" with show_overlay=True text="Processing..." %}');
{% endcomment %}
