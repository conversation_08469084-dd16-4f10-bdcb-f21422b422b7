{% comment %}
Reusable social login component for CozyWish authentication forms

Usage:
{% include 'accounts/components/social_login.html' with show_divider=True divider_text='or continue with' %}

Parameters:
- show_divider: Whether to show the divider above social buttons (defaults to True)
- divider_text: Text to display in the divider (defaults to 'or continue with')
- button_style: Style variant ('default', 'outline', 'minimal') - defaults to 'default'
- show_providers: List of providers to show (defaults to all available)
- redirect_url: Custom redirect URL after social login
{% endcomment %}

{% load socialaccount %}

{% get_providers as socialaccount_providers %}

{% if socialaccount_providers %}
    {% if show_divider|default:True %}
        <!-- Divider -->
        <div class="social-divider">
            <span class="social-divider-text">{{ divider_text|default:'or continue with' }}</span>
        </div>
    {% endif %}

    <!-- Social Login Buttons -->
    <div class="social-login-section">
        {% for provider in socialaccount_providers %}
            {% if not show_providers or provider.id in show_providers %}
                <a href="{% provider_login_url provider.id process='login' next=redirect_url %}" 
                   class="btn social-btn social-btn-{{ provider.id }} social-btn-{{ button_style|default:'default' }}"
                   aria-label="Sign in with {{ provider.name }}">
                    <i class="fab fa-{{ provider.id }} social-icon"></i>
                    <span class="social-text">Continue with {{ provider.name }}</span>
                </a>
            {% endif %}
        {% endfor %}
    </div>

    <style>
        /* Social Login Component Styles */
        .social-divider {
            position: relative;
            text-align: center;
            margin: 2rem 0 1.5rem;
        }

        .social-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--cw-neutral-200);
        }

        .social-divider-text {
            background: white;
            padding: 0 1rem;
            color: var(--cw-neutral-600);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .social-login-section {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        /* Base social button styles */
        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 0.95rem;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid;
            width: 100%;
        }

        .social-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--cw-shadow-md);
            text-decoration: none;
        }

        .social-icon {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .social-text {
            flex: 1;
            text-align: center;
        }

        /* Default style social buttons */
        .social-btn-default.social-btn-google {
            background: #4285f4;
            border-color: #4285f4;
            color: white;
        }

        .social-btn-default.social-btn-google:hover {
            background: #3367d6;
            border-color: #3367d6;
            color: white;
        }

        .social-btn-default.social-btn-facebook {
            background: #1877f2;
            border-color: #1877f2;
            color: white;
        }

        .social-btn-default.social-btn-facebook:hover {
            background: #166fe5;
            border-color: #166fe5;
            color: white;
        }

        .social-btn-default.social-btn-twitter {
            background: #1da1f2;
            border-color: #1da1f2;
            color: white;
        }

        .social-btn-default.social-btn-twitter:hover {
            background: #0d8bd9;
            border-color: #0d8bd9;
            color: white;
        }

        .social-btn-default.social-btn-github {
            background: #333;
            border-color: #333;
            color: white;
        }

        .social-btn-default.social-btn-github:hover {
            background: #24292e;
            border-color: #24292e;
            color: white;
        }

        .social-btn-default.social-btn-linkedin {
            background: #0077b5;
            border-color: #0077b5;
            color: white;
        }

        .social-btn-default.social-btn-linkedin:hover {
            background: #005885;
            border-color: #005885;
            color: white;
        }

        /* Outline style social buttons */
        .social-btn-outline {
            background: white;
            border-color: var(--cw-neutral-200);
            color: var(--cw-neutral-700);
        }

        .social-btn-outline:hover {
            background: var(--cw-accent-light);
            border-color: var(--cw-brand-accent);
            color: var(--cw-brand-primary);
        }

        .social-btn-outline .social-icon.fa-google {
            color: #4285f4;
        }

        .social-btn-outline .social-icon.fa-facebook {
            color: #1877f2;
        }

        .social-btn-outline .social-icon.fa-twitter {
            color: #1da1f2;
        }

        .social-btn-outline .social-icon.fa-github {
            color: #333;
        }

        .social-btn-outline .social-icon.fa-linkedin {
            color: #0077b5;
        }

        /* Minimal style social buttons */
        .social-btn-minimal {
            background: var(--cw-accent-light);
            border-color: var(--cw-brand-accent);
            color: var(--cw-brand-primary);
        }

        .social-btn-minimal:hover {
            background: var(--cw-brand-primary);
            border-color: var(--cw-brand-primary);
            color: white;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .social-btn {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .social-text {
                font-size: 0.9rem;
            }
        }

        /* Accessibility improvements */
        .social-btn:focus {
            outline: 2px solid var(--cw-brand-primary);
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .social-btn {
                border-width: 3px;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .social-btn {
                transition: none;
            }

            .social-btn:hover {
                transform: none;
            }
        }
    </style>
{% endif %}

{% comment %}
Example usage in templates:

<!-- Basic usage with all providers -->
{% include 'accounts/components/social_login.html' %}

<!-- With custom divider text -->
{% include 'accounts/components/social_login.html' with divider_text='or sign up with' %}

<!-- Without divider -->
{% include 'accounts/components/social_login.html' with show_divider=False %}

<!-- Outline style buttons -->
{% include 'accounts/components/social_login.html' with button_style='outline' %}

<!-- Only specific providers -->
{% include 'accounts/components/social_login.html' with show_providers='google,facebook' %}

<!-- With custom redirect -->
{% include 'accounts/components/social_login.html' with redirect_url='/dashboard/' %}
{% endcomment %}
