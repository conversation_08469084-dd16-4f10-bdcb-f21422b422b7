{% comment %}
Reusable message display component for CozyWish authentication forms

Usage:
{% include 'accounts/components/messages.html' with show_icons=True dismissible=True %}

Parameters:
- show_icons: Whether to show icons in messages (defaults to True)
- dismissible: Whether messages can be dismissed (defaults to True)
- message_style: Style variant ('default', 'minimal', 'compact') - defaults to 'default'
- auto_dismiss: Auto-dismiss timeout in milliseconds (optional)
- position: Message position ('top', 'bottom', 'inline') - defaults to 'inline'
{% endcomment %}

{% if messages %}
    <div class="auth-messages auth-messages-{{ message_style|default:'default' }} auth-messages-{{ position|default:'inline' }}">
        {% for message in messages %}
            <div class="alert auth-alert auth-alert-{{ message.tags }} {% if dismissible|default:True %}alert-dismissible{% endif %} fade show" 
                 role="alert"
                 {% if auto_dismiss %}data-auto-dismiss="{{ auto_dismiss }}"{% endif %}>
                
                {% if show_icons|default:True %}
                    <i class="auth-alert-icon 
                        {% if message.tags == 'error' or message.tags == 'danger' %}fas fa-exclamation-triangle
                        {% elif message.tags == 'success' %}fas fa-check-circle
                        {% elif message.tags == 'warning' %}fas fa-exclamation-circle
                        {% elif message.tags == 'info' %}fas fa-info-circle
                        {% else %}fas fa-bell
                        {% endif %}"></i>
                {% endif %}
                
                <div class="auth-alert-content">
                    {{ message }}
                </div>
                
                {% if dismissible|default:True %}
                    <button type="button" class="btn-close auth-alert-close" data-bs-dismiss="alert" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                {% endif %}
            </div>
        {% endfor %}
    </div>

    <style>
        /* Authentication Messages Component Styles */
        .auth-messages {
            margin-bottom: 1.5rem;
        }

        /* Message positioning */
        .auth-messages-top {
            position: fixed;
            top: 1rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1050;
            width: 90%;
            max-width: 500px;
        }

        .auth-messages-bottom {
            position: fixed;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1050;
            width: 90%;
            max-width: 500px;
        }

        .auth-messages-inline {
            position: relative;
        }

        /* Alert Base Styles */
        .auth-alert {
            border: 2px solid;
            border-radius: 0.5rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            font-size: 0.95rem;
            line-height: 1.5;
            position: relative;
        }

        .auth-alert:last-child {
            margin-bottom: 0;
        }

        /* Alert Icon */
        .auth-alert-icon {
            font-size: 1.1rem;
            margin-top: 0.1rem;
            flex-shrink: 0;
        }

        /* Alert Content */
        .auth-alert-content {
            flex: 1;
            font-weight: 500;
        }

        /* Close Button */
        .auth-alert-close {
            background: none;
            border: none;
            padding: 0;
            margin-left: auto;
            color: inherit;
            opacity: 0.7;
            cursor: pointer;
            transition: opacity 0.3s ease;
            flex-shrink: 0;
        }

        .auth-alert-close:hover {
            opacity: 1;
        }

        .auth-alert-close i {
            font-size: 0.9rem;
        }

        /* Success Messages */
        .auth-alert-success {
            background-color: #f0fff4;
            color: #155724;
            border-color: #28a745;
        }

        .auth-alert-success .auth-alert-icon {
            color: #28a745;
        }

        /* Error/Danger Messages */
        .auth-alert-error,
        .auth-alert-danger {
            background-color: #fff5f5;
            color: #721c24;
            border-color: #dc3545;
        }

        .auth-alert-error .auth-alert-icon,
        .auth-alert-danger .auth-alert-icon {
            color: #dc3545;
        }

        /* Warning Messages */
        .auth-alert-warning {
            background-color: #fffbf0;
            color: #856404;
            border-color: #ffc107;
        }

        .auth-alert-warning .auth-alert-icon {
            color: #ffc107;
        }

        /* Info Messages */
        .auth-alert-info {
            background-color: #f0f9ff;
            color: #0c5460;
            border-color: #17a2b8;
        }

        .auth-alert-info .auth-alert-icon {
            color: #17a2b8;
        }

        /* Default Messages */
        .auth-alert-debug,
        .auth-alert-default {
            background-color: var(--cw-accent-light);
            color: var(--cw-brand-primary);
            border-color: var(--cw-brand-accent);
        }

        .auth-alert-debug .auth-alert-icon,
        .auth-alert-default .auth-alert-icon {
            color: var(--cw-brand-primary);
        }

        /* Message Style Variants */
        
        /* Minimal style */
        .auth-messages-minimal .auth-alert {
            border: 1px solid;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
        }

        .auth-messages-minimal .auth-alert-icon {
            font-size: 1rem;
        }

        /* Compact style */
        .auth-messages-compact .auth-alert {
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
            border-radius: 0.25rem;
        }

        .auth-messages-compact .auth-alert-icon {
            font-size: 0.9rem;
        }

        /* Animation for auto-dismiss */
        .auth-alert[data-auto-dismiss] {
            animation: fadeInOut ease-in-out;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-10px); }
            10%, 90% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-10px); }
        }

        /* Responsive Adjustments */
        @media (max-width: 576px) {
            .auth-messages-top,
            .auth-messages-bottom {
                width: 95%;
                left: 2.5%;
                transform: none;
            }

            .auth-alert {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .auth-alert-icon {
                font-size: 1rem;
            }
        }

        /* Accessibility improvements */
        .auth-alert {
            outline: none;
        }

        .auth-alert:focus-within {
            outline: 2px solid var(--cw-brand-primary);
            outline-offset: 2px;
        }

        .auth-alert-close:focus {
            outline: 2px solid currentColor;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .auth-alert {
                border-width: 3px;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .auth-alert {
                animation: none;
            }

            .auth-alert-close {
                transition: none;
            }
        }

        /* Print styles */
        @media print {
            .auth-messages-top,
            .auth-messages-bottom {
                position: static;
                transform: none;
                width: 100%;
            }

            .auth-alert-close {
                display: none;
            }
        }
    </style>

    {% if auto_dismiss %}
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const autoDismissAlerts = document.querySelectorAll('[data-auto-dismiss]');
                autoDismissAlerts.forEach(function(alert) {
                    const timeout = parseInt(alert.dataset.autoDismiss);
                    if (timeout > 0) {
                        setTimeout(function() {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }, timeout);
                    }
                });
            });
        </script>
    {% endif %}
{% endif %}

{% comment %}
Example usage in templates:

<!-- Basic usage -->
{% include 'accounts/components/messages.html' %}

<!-- Minimal style without icons -->
{% include 'accounts/components/messages.html' with message_style='minimal' show_icons=False %}

<!-- Non-dismissible messages -->
{% include 'accounts/components/messages.html' with dismissible=False %}

<!-- Auto-dismiss after 5 seconds -->
{% include 'accounts/components/messages.html' with auto_dismiss=5000 %}

<!-- Top positioned messages -->
{% include 'accounts/components/messages.html' with position='top' %}

<!-- Compact style -->
{% include 'accounts/components/messages.html' with message_style='compact' %}

Note: This component works with Django's messages framework.
Add messages in your views like:
messages.success(request, 'Account created successfully!')
messages.error(request, 'Invalid email or password.')
messages.warning(request, 'Please verify your email address.')
messages.info(request, 'Password reset link sent to your email.')
{% endcomment %}
