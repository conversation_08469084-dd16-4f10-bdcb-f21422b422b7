{% extends "base.html" %}
{% load static %}

{% block title %}Rate Limit Exceeded - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .rate-limit-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
        text-align: center;
    }
    
    .rate-limit-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 3rem 2rem;
        margin-bottom: 1.5rem;
    }
    
    .rate-limit-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 3rem;
        animation: pulse 2s ease-in-out infinite;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    
    .rate-limit-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .rate-limit-subtitle {
        color: #525252;
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .rate-limit-details {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }
    
    .rate-limit-details h3 {
        color: #856404;
        font-family: 'Poppins', sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .rate-limit-details p {
        color: #856404;
        margin: 0.5rem 0;
        line-height: 1.6;
    }
    
    .countdown-timer {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .countdown-timer h4 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .countdown-display {
        font-size: 2rem;
        font-weight: 700;
        color: #f59e0b;
        margin: 1rem 0;
        font-family: 'Courier New', monospace;
    }
    
    .security-info {
        background: #e7f3ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }
    
    .security-info h4 {
        color: #0369a1;
        font-family: 'Poppins', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .security-info ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .security-info li {
        padding: 0.5rem 0;
        color: #0369a1;
        position: relative;
        padding-left: 1.5rem;
    }
    
    .security-info li:before {
        content: "🔒";
        position: absolute;
        left: 0;
        top: 0.5rem;
    }
    
    .action-buttons {
        margin-top: 2rem;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0.5rem;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary {
        background: white;
        color: #2F160F;
        border: 2px solid #2F160F;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0.5rem;
    }
    
    .btn-secondary:hover {
        background: #2F160F;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .help-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .help-section h5 {
        color: #2F160F;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .help-section p {
        color: #525252;
        margin: 0;
        line-height: 1.6;
    }
    
    .help-section a {
        color: #2F160F;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="rate-limit-container">
    <div class="rate-limit-card">
        <div class="rate-limit-icon">
            ⏳
        </div>
        
        <h1 class="rate-limit-title">Rate Limit Exceeded</h1>
        
        <p class="rate-limit-subtitle">
            You've made too many requests in a short period. Please wait before trying again.
        </p>
        
        <div class="rate-limit-details">
            <h3>🚦 What Happened?</h3>
            <p>Our security system detected multiple rapid requests from your connection. This could be due to:</p>
            <ul style="margin: 1rem 0; padding-left: 1.5rem;">
                <li>Multiple failed login attempts</li>
                <li>Rapid form submissions</li>
                <li>Automated requests or scripts</li>
                <li>Network issues causing duplicate requests</li>
            </ul>
        </div>
        
        <div class="countdown-timer">
            <h4>⏰ Time Until Reset</h4>
            <div class="countdown-display" id="countdown">
                Calculating...
            </div>
            <p style="color: #525252; margin: 0;">
                You can try again once the timer reaches zero.
            </p>
        </div>
        
        <div class="security-info">
            <h4>🔒 Why Do We Have Rate Limits?</h4>
            <ul>
                <li>Protect against brute force attacks</li>
                <li>Prevent automated abuse of our services</li>
                <li>Ensure fair usage for all users</li>
                <li>Maintain system performance and stability</li>
                <li>Comply with security best practices</li>
            </ul>
        </div>
        
        <div class="action-buttons">
            <button type="button" class="btn-primary" onclick="checkRateLimit()" id="retry-btn" disabled>
                🔄 Check if I Can Try Again
            </button>
            <a href="{% url 'venues_app:home' %}" class="btn-secondary">
                🏠 Return to Home
            </a>
        </div>
        
        <div class="help-section">
            <h5>🆘 Still Having Issues?</h5>
            <p>
                If you continue to experience rate limiting issues or believe this is an error, 
                please contact our support team at 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get retry time from URL parameters or default to 5 minutes
    const urlParams = new URLSearchParams(window.location.search);
    let retryAfter = parseInt(urlParams.get('retry_after')) || 300; // 5 minutes default
    
    const countdownElement = document.getElementById('countdown');
    const retryButton = document.getElementById('retry-btn');
    
    function updateCountdown() {
        if (retryAfter <= 0) {
            countdownElement.textContent = '00:00';
            retryButton.disabled = false;
            retryButton.textContent = '✅ Try Again Now';
            return;
        }
        
        const minutes = Math.floor(retryAfter / 60);
        const seconds = retryAfter % 60;
        countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        retryAfter--;
        setTimeout(updateCountdown, 1000);
    }
    
    // Start countdown
    updateCountdown();
    
    // Check rate limit function
    window.checkRateLimit = function() {
        if (retryAfter > 0) {
            alert('Please wait for the countdown to finish before trying again.');
            return;
        }
        
        // Redirect back to the original page or login
        const referrer = document.referrer;
        if (referrer && referrer.includes(window.location.hostname)) {
            window.location.href = referrer;
        } else {
            window.location.href = '{% url "account_login" %}';
        }
    };
    
    // Auto-enable retry button when countdown finishes
    setTimeout(() => {
        if (retryAfter <= 0) {
            retryButton.disabled = false;
            retryButton.textContent = '✅ Try Again Now';
        }
    }, retryAfter * 1000);
    
    // Track rate limit page view
    if (typeof gtag !== 'undefined') {
        gtag('event', 'rate_limit_exceeded', {
            'event_category': 'security',
            'event_label': 'rate_limit_page_view'
        });
    }
});
</script>
{% endblock %}
