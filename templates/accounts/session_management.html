{% extends "base.html" %}
{% load static %}

{% block title %}Session Management - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .session-container {
        max-width: 1000px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .session-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .session-header {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .session-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
    }
    
    .session-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }
    
    .session-subtitle {
        color: #525252;
        font-size: 1rem;
        margin: 0;
    }
    
    .session-item {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        position: relative;
    }
    
    .session-item.current {
        border-color: #28a745;
        background: #f8fff9;
    }
    
    .session-item.current::before {
        content: "Current Session";
        position: absolute;
        top: -10px;
        left: 1rem;
        background: #28a745;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .session-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .session-detail {
        display: flex;
        align-items: center;
        color: #525252;
        font-size: 0.9rem;
    }
    
    .session-detail-icon {
        margin-right: 0.5rem;
        font-size: 1rem;
    }
    
    .session-detail-label {
        font-weight: 600;
        margin-right: 0.5rem;
    }
    
    .session-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-terminate {
        background: #dc3545;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-terminate:hover {
        background: #c82333;
        transform: translateY(-1px);
    }
    
    .btn-terminate-all {
        background: #fd7e14;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }
    
    .btn-terminate-all:hover {
        background: #e8590c;
        transform: translateY(-2px);
    }
    
    .security-events {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
    }
    
    .security-events h3 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .event-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .event-item:last-child {
        border-bottom: none;
    }
    
    .event-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 0.875rem;
    }
    
    .event-success {
        background: #d4edda;
        color: #155724;
    }
    
    .event-warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .event-danger {
        background: #f8d7da;
        color: #721c24;
    }
    
    .event-info {
        flex: 1;
    }
    
    .event-type {
        font-weight: 600;
        color: #2F160F;
        font-size: 0.9rem;
    }
    
    .event-details {
        color: #525252;
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }
    
    .event-time {
        color: #6c757d;
        font-size: 0.8rem;
        white-space: nowrap;
    }
    
    .alert {
        border-radius: 8px;
        border: none;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
    }
    
    .alert-warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .alert-danger {
        background: #f8d7da;
        color: #721c24;
    }
    
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
    
    @media (max-width: 768px) {
        .session-info {
            grid-template-columns: 1fr;
        }
        
        .session-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="session-container">
    <div class="session-card">
        <div class="session-header">
            <div class="session-icon">
                🔐
            </div>
            <div>
                <h1 class="session-title">Session Management</h1>
                <p class="session-subtitle">Manage your active sessions and security settings</p>
            </div>
        </div>
        
        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        
        <!-- Terminate All Others Button -->
        {% if active_sessions.count > 1 %}
            <button type="button" class="btn-terminate-all" onclick="terminateAllOtherSessions()">
                🚫 Terminate All Other Sessions ({{ active_sessions.count|add:"-1" }})
            </button>
        {% endif %}
        
        <!-- Active Sessions -->
        <h2 style="color: #2F160F; font-family: 'Poppins', sans-serif; font-size: 1.3rem; margin-bottom: 1rem;">
            Active Sessions ({{ active_sessions.count }})
        </h2>
        
        {% for session in active_sessions %}
            <div class="session-item {% if session.session_key == current_session_key %}current{% endif %}" id="session-{{ session.session_id }}">
                <div class="session-info">
                    <div class="session-detail">
                        <span class="session-detail-icon">💻</span>
                        <span class="session-detail-label">Device:</span>
                        {{ session.device_name|default:"Unknown Device" }}
                    </div>
                    <div class="session-detail">
                        <span class="session-detail-icon">📍</span>
                        <span class="session-detail-label">Location:</span>
                        {{ session.location|default:"Unknown" }}
                    </div>
                    <div class="session-detail">
                        <span class="session-detail-icon">🌐</span>
                        <span class="session-detail-label">IP Address:</span>
                        {{ session.ip_address|default:"Unknown" }}
                    </div>
                    <div class="session-detail">
                        <span class="session-detail-icon">⏰</span>
                        <span class="session-detail-label">Last Activity:</span>
                        {{ session.last_activity|timesince }} ago
                    </div>
                    <div class="session-detail">
                        <span class="session-detail-icon">📅</span>
                        <span class="session-detail-label">Created:</span>
                        {{ session.created_at|date:"M j, Y g:i A" }}
                    </div>
                    <div class="session-detail">
                        <span class="session-detail-icon">⏳</span>
                        <span class="session-detail-label">Expires:</span>
                        {% if session.expires_at %}
                            {{ session.expires_at|timeuntil }}
                        {% else %}
                            Never
                        {% endif %}
                    </div>
                </div>
                
                {% if session.is_remember_me %}
                    <div style="margin: 0.5rem 0;">
                        <span style="background: #e7f3ff; color: #0369a1; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.75rem;">
                            🔒 Remember Me Session
                        </span>
                    </div>
                {% endif %}
                
                {% if session.session_key != current_session_key %}
                    <div class="session-actions">
                        <button type="button" class="btn-terminate" onclick="terminateSession('{{ session.session_id }}')">
                            🚫 Terminate Session
                        </button>
                    </div>
                {% else %}
                    <div style="margin-top: 1rem; color: #28a745; font-weight: 600; font-size: 0.9rem;">
                        ✅ This is your current session
                    </div>
                {% endif %}
            </div>
        {% empty %}
            <div class="alert alert-warning">
                No active sessions found.
            </div>
        {% endfor %}
    </div>
    
    <!-- Recent Security Events -->
    <div class="session-card">
        <div class="security-events">
            <h3>🔍 Recent Security Events</h3>
            
            {% for event in recent_events|slice:":10" %}
                <div class="event-item">
                    <div class="event-icon 
                        {% if event.event_type == 'login_success' %}event-success
                        {% elif event.event_type == 'login_failed' %}event-danger
                        {% elif event.event_type == 'logout' %}event-info
                        {% else %}event-warning{% endif %}">
                        {% if event.event_type == 'login_success' %}✅
                        {% elif event.event_type == 'login_failed' %}❌
                        {% elif event.event_type == 'logout' %}🚪
                        {% elif event.event_type == 'session_timeout' %}⏰
                        {% elif event.event_type == 'concurrent_login' %}👥
                        {% elif event.event_type == 'suspicious_location' %}🚨
                        {% elif event.event_type == 'password_change' %}🔑
                        {% else %}⚠️{% endif %}
                    </div>
                    <div class="event-info">
                        <div class="event-type">{{ event.get_event_type_display }}</div>
                        <div class="event-details">
                            {% if event.ip_address %}IP: {{ event.ip_address }}{% endif %}
                            {% if event.location %} • {{ event.location }}{% endif %}
                            {% if event.is_suspicious %} • <strong style="color: #dc3545;">Suspicious Activity</strong>{% endif %}
                        </div>
                    </div>
                    <div class="event-time">
                        {{ event.created_at|timesince }} ago
                    </div>
                </div>
            {% empty %}
                <div class="alert alert-info">
                    No security events recorded yet.
                </div>
            {% endfor %}
            
            {% if recent_events.count > 10 %}
                <div style="text-align: center; margin-top: 1rem;">
                    <a href="{% url 'accounts_app:session_security' %}" style="color: #2F160F; font-weight: 600;">
                        View All Security Events →
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // CSRF token for AJAX requests
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    window.terminateSession = function(sessionId) {
        if (!confirm('Are you sure you want to terminate this session?')) {
            return;
        }
        
        const sessionElement = document.getElementById(`session-${sessionId}`);
        sessionElement.classList.add('loading');
        
        fetch('{% url "accounts_app:terminate_session" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: `session_id=${sessionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                sessionElement.style.opacity = '0.5';
                sessionElement.innerHTML = '<div style="text-align: center; padding: 2rem; color: #28a745;">✅ Session terminated successfully</div>';
                setTimeout(() => {
                    sessionElement.remove();
                }, 2000);
                showMessage(data.message, 'success');
            } else {
                sessionElement.classList.remove('loading');
                showMessage(data.message, 'danger');
            }
        })
        .catch(error => {
            sessionElement.classList.remove('loading');
            showMessage('An error occurred while terminating the session.', 'danger');
        });
    };
    
    window.terminateAllOtherSessions = function() {
        if (!confirm('Are you sure you want to terminate all other sessions? This will sign you out from all other devices.')) {
            return;
        }
        
        const button = document.querySelector('.btn-terminate-all');
        button.disabled = true;
        button.textContent = 'Terminating...';
        
        fetch('{% url "accounts_app:terminate_session" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: 'action=terminate_all_others'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Reload page to update session list
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                button.disabled = false;
                button.textContent = '🚫 Terminate All Other Sessions';
                showMessage(data.message, 'danger');
            }
        })
        .catch(error => {
            button.disabled = false;
            button.textContent = '🚫 Terminate All Other Sessions';
            showMessage('An error occurred while terminating sessions.', 'danger');
        });
    };
    
    function showMessage(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        const container = document.querySelector('.session-container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
});
</script>

{% csrf_token %}
{% endblock %}
