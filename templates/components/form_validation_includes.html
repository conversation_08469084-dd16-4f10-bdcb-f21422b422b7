{% comment %}
Form Validation Includes Component

Include this component in templates that use enhanced form validation.
This provides all necessary CSS and JavaScript files for the form validation system.

Usage:
{% include 'components/form_validation_includes.html' %}

Optional parameters:
- include_test: Include test scripts (default: False)
- include_debug: Include debug scripts (default: False)
{% endcomment %}

{% load static %}

<!-- Form Validation CSS -->
<link rel="stylesheet" href="{% static 'css/form-validation.css' %}">

<!-- Form Validation JavaScript -->
<script src="{% static 'js/form-validation-framework.js' %}" defer></script>
<script src="{% static 'js/form-ux-enhancements.js' %}" defer></script>
<script src="{% static 'js/form-components.js' %}" defer></script>

{% if include_test %}
    <!-- Form Integration Tests (Development Only) -->
    <script src="{% static 'js/form-integration-test.js' %}" defer></script>
{% endif %}

{% if include_debug %}
    <!-- Form Debug Scripts -->
    <script>
        window.FORM_DEBUG = true;
        console.log('🔧 Form validation debug mode enabled');
    </script>
{% endif %}

<!-- Initialize form validation on page load -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add progressive enhancement class
    document.documentElement.classList.add('js-enhanced');
    
    // Initialize form validation if not already done
    if (!window.formValidationInitialized) {
        window.formValidationInitialized = true;
        
        // Log initialization in debug mode
        if (window.FORM_DEBUG) {
            console.log('🚀 Form validation system initialized');
        }
    }
});
</script>
