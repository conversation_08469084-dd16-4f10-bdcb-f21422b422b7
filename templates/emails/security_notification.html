{% extends "emails/base_email.html" %}

{% block email_title %}Security Alert: Password Reset Requested{% endblock %}

{% block header_gradient %}linear-gradient(135deg, #f59e0b 0%, #d97706 100%){% endblock %}
{% block button_gradient %}linear-gradient(135deg, #f59e0b 0%, #d97706 100%){% endblock %}

{% block header_icon %}🔔{% endblock %}
{% block header_title %}Security Alert{% endblock %}
{% block header_subtitle %}Password reset requested for your account{% endblock %}

{% block custom_styles %}
.security-alert {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    border-radius: 12px;
    padding: 24px;
    margin: 24px 0;
}

.security-alert h3 {
    color: #92400e;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.security-alert p {
    color: #92400e;
    margin: 8px 0;
    line-height: 1.6;
}

.request-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin: 20px 0;
    font-family: monospace;
    font-size: 14px;
}

.request-details h4 {
    color: #495057;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.request-details p {
    color: #495057;
    margin: 4px 0;
    word-break: break-all;
}

.action-required {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.action-required h4 {
    color: #991b1b;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.action-required p {
    color: #991b1b;
    margin: 8px 0;
    line-height: 1.6;
}

.security-tips {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.security-tips h4 {
    color: #0369a1;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.security-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.security-tips li {
    padding: 6px 0;
    color: #0369a1;
    position: relative;
    padding-left: 20px;
}

.security-tips li:before {
    content: "🔒";
    position: absolute;
    left: 0;
    top: 6px;
}
{% endblock %}

{% block email_content %}
<p><strong>Hello {{ user.get_full_name|default:user.email }},</strong></p>

<p>This is a security notification to inform you that a password reset was requested for your <strong>CozyWish</strong> account.</p>

<div class="security-alert">
    <h3>🔔 What Happened?</h3>
    <p>Someone requested a password reset for your account. If this was you, you can safely ignore this email.</p>
    <p><strong>If this wasn't you, your account may be at risk.</strong></p>
</div>
{% endblock %}

{% block action_section %}
<div class="request-details">
    <h4>📋 Request Details</h4>
    <p><strong>Time:</strong> {{ timestamp|date:"F j, Y g:i A T" }}</p>
    <p><strong>IP Address:</strong> {{ ip_address }}</p>
    <p><strong>User Agent:</strong> {{ user_agent|truncatechars:100 }}</p>
    <p><strong>Account:</strong> {{ user.email }}</p>
</div>

<div class="action-required">
    <h4>⚠️ If This Wasn't You</h4>
    <p>Take these steps immediately:</p>
    <ul style="margin: 12px 0; padding-left: 20px;">
        <li>Do not click any password reset links you didn't request</li>
        <li>Change your password immediately if you can still access your account</li>
        <li>Check your account for any unauthorized activity</li>
        <li>Contact our security <NAME_EMAIL></li>
    </ul>
</div>
{% endblock %}

{% block details_section %}
<div class="account-details">
    <div class="details-title">
        <span>🏠</span> Account Information
    </div>
    <ul class="details-list">
        <li>
            <span class="details-label">Email:</span>
            <span class="details-value">{{ user.email }}</span>
        </li>
        <li>
            <span class="details-label">Account Type:</span>
            <span class="details-value">{{ user.role|default:"Customer"|title }}</span>
        </li>
        <li>
            <span class="details-label">Last Login:</span>
            <span class="details-value">{{ user.last_login|date:"F j, Y g:i A"|default:"Unknown" }}</span>
        </li>
    </ul>
</div>

<div class="security-tips">
    <h4>🔒 Security Best Practices</h4>
    <ul>
        <li>Use a strong, unique password for your CozyWish account</li>
        <li>Enable two-factor authentication when available</li>
        <li>Never share your login credentials with anyone</li>
        <li>Log out of shared or public computers</li>
        <li>Regularly review your account activity</li>
        <li>Keep your email account secure</li>
    </ul>
</div>
{% endblock %}

{% block additional_content %}
<p><strong>What We're Doing:</strong></p>
<ul style="margin: 12px 0; padding-left: 20px; color: #525252;">
    <li>All password reset requests are logged and monitored</li>
    <li>Suspicious activity is automatically flagged for review</li>
    <li>We use secure, encrypted connections for all communications</li>
    <li>Our security team monitors for unusual patterns</li>
</ul>

<p>If you have any concerns about your account security, please don't hesitate to contact our security team at <a href="mailto:<EMAIL>" style="color: #2F160F;"><EMAIL></a></p>

<p>Stay secure,<br>The CozyWish Security Team</p>
{% endblock %}
