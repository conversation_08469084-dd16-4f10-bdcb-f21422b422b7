{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{{ action }} Amenity - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #2F160F;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    /* Form section description text */
    .form-section p,
    .form-section .text-muted,
    .form-section small {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 1.125rem;
        line-height: 1.6;
    }

    .form-section small {
        font-size: 0.875rem;
    }

    /* Form labels */
    .form-label {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Form check inputs */
    .form-check-input {
        margin-right: 0.5rem;
    }

    .form-check-label {
        font-weight: 500;
        color: #525252;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
    }

    /* Button styling */
    .btn-cw-primary {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid #525252;
        color: #525252;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-cw-secondary:hover {
        background: #525252;
        border-color: #525252;
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }

    /* Action buttons section */
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #fae1d7;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .form-section {
            padding: 1.5rem;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.75rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

    <!-- Back Navigation -->
    <div class="mb-4">
        <a href="{% url 'venues_app:manage_amenities' %}" class="btn-cw-secondary">
            <i class="fas fa-arrow-left"></i> Back to Amenities
        </a>
    </div>

    <!-- Amenity Form -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-star me-2"></i>{{ action }} Amenity
        </h4>
        
        <p class="text-muted mb-3">
            {% if action == 'Edit' %}
                Update the amenity information for {{ venue.venue_name }}.
            {% else %}
                Add a new amenity to showcase what your venue offers to customers.
            {% endif %}
        </p>

        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.amenity_type.id_for_label }}" class="form-label fw-bold">
                        Amenity Type <span class="text-danger">*</span>
                    </label>
                    {{ form.amenity_type }}
                    {% if form.amenity_type.errors %}
                        {% for error in form.amenity_type.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <small class="form-text text-muted">
                        Select the type of amenity you want to add to your venue.
                    </small>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="{{ form.custom_name.id_for_label }}" class="form-label fw-bold">
                        Custom Name
                    </label>
                    {{ form.custom_name }}
                    {% if form.custom_name.errors %}
                        {% for error in form.custom_name.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <small class="form-text text-muted">
                        Optional: Override the default amenity name with a custom one.
                    </small>
                </div>
            </div>

            <div class="mb-4">
                <div class="form-check">
                    {{ form.is_active }}
                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                        <i class="fas fa-toggle-on me-2"></i>Active (visible to customers)
                    </label>
                </div>
                {% if form.is_active.errors %}
                    {% for error in form.is_active.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Active amenities are displayed to customers when viewing your venue.
                </small>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{% url 'venues_app:manage_amenities' %}" class="btn-cw-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
                <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-save me-2"></i>{{ action }} Amenity
                </button>
            </div>
        </form>
    </div>

</div>
{% endblock %}
