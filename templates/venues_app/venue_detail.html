{% extends 'base.html' %}
{% load discount_tags i18n %}

{% block title %}{{ venue.venue_name }} - {{ venue.service_provider.business_name }}{% endblock %}

{% block extra_css %}
<style>
    /* CozyWish Design System - Venue Detail */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Breadcrumb Styling */
    .breadcrumb-cw {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-cw .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .breadcrumb-cw .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-cw .breadcrumb-item.active {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Card Styling */
    .card-cw {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-featured {
        border: 2px solid var(--cw-brand-primary);
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Badge Styling */
    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
    }

    .badge-cw-secondary {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    /* Accordion Styling */
    .accordion-cw .accordion-item {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .accordion-cw .accordion-button {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        font-weight: 600;
        border: none;
        padding: 1.25rem 1.5rem;
    }

    .accordion-cw .accordion-button:not(.collapsed) {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        box-shadow: none;
    }

    .accordion-cw .accordion-body {
        background: white;
        padding: 1.5rem;
    }

    /* Service Item Styling */
    .service-item {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: white;
        transition: all 0.2s ease;
    }

    .service-item:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
    }

    .service-name {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .service-description {
        color: var(--cw-neutral-600);
        margin-bottom: 1rem;
    }

    .price-info .text-success {
        color: var(--cw-brand-primary) !important;
    }

    /* Fixed Position Buttons */
    .fixed-action-btn {
        position: fixed;
        bottom: 2rem;
        z-index: 1000;
        border-radius: 50px;
        padding: 1rem 1.5rem;
        box-shadow: var(--cw-shadow-lg);
        border: 2px solid var(--cw-brand-primary);
        background: white;
        color: var(--cw-brand-primary);
        transition: all 0.2s ease;
    }

    .fixed-action-btn:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
    }

    .fixed-action-btn.favorite-active {
        background: var(--cw-brand-primary);
        color: white;
    }

    /* Team Members Styling */
    .team-member-card {
        text-align: center;
        padding: 1.5rem;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        background: white;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .team-member-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .team-member-image-container {
        width: 120px;
        height: 120px;
        margin-bottom: 1rem;
        flex-shrink: 0;
    }

    .team-member-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-image {
        border-color: var(--cw-brand-primary);
        transform: scale(1.05);
    }

    .team-member-placeholder {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: var(--cw-accent-light);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid var(--cw-brand-accent);
        color: var(--cw-neutral-600);
        font-size: 2.5rem;
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-placeholder {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
    }

    .team-member-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .team-member-name {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .team-member-position {
        color: var(--cw-neutral-600);
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Responsive Design for Team Members */
    @media (max-width: 768px) {
        .team-member-card {
            padding: 1rem;
        }
        
        .team-member-image-container {
            width: 100px;
            height: 100px;
        }
        
        .team-member-placeholder {
            font-size: 2rem;
        }
    }

    /* Enhanced venue detail styling with improved service cards */
    
    /* Services Showcase Styling */
    .services-showcase {
        margin: 3rem 0;
    }

    .services-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2.5rem;
        padding: 2rem;
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
    }

    .services-header-content h2.services-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin: 0 0 0.5rem 0;
        font-family: var(--cw-font-heading);
    }

    .services-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.125rem;
        margin: 0;
        line-height: 1.4;
    }

    .services-count {
        text-align: center;
        padding: 1rem;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
        min-width: 120px;
    }

    .count-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        line-height: 1;
    }

    .count-label {
        display: block;
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-top: 0.25rem;
    }

    /* Enhanced Services Grid */
    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
    }

    .service-card-enhanced {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .service-card-enhanced:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    /* Service Image Container */
    .service-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
        background: var(--cw-accent-light);
    }

    .service-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .service-card-enhanced:hover .service-image {
        transform: scale(1.05);
    }

    .service-image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--cw-accent-light) 0%, var(--cw-brand-accent) 100%);
        color: var(--cw-brand-primary);
        font-size: 3rem;
        opacity: 0.7;
    }

    /* Service Status Indicators */
    .service-indicators {
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .indicator-featured {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 0.5rem;
        border-radius: 50%;
        font-size: 0.875rem;
        box-shadow: var(--cw-shadow-sm);
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .indicator-discount {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        box-shadow: var(--cw-shadow-sm);
        white-space: nowrap;
    }

    /* Service Content */
    .service-content {
        padding: 1.5rem;
    }

    .service-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        gap: 1rem;
    }

    .service-name {
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        line-height: 1.3;
        font-family: var(--cw-font-heading);
        flex: 1;
    }

    .service-category-tag {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.8125rem;
        font-weight: 500;
        border: 1px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        gap: 0.375rem;
        white-space: nowrap;
    }

    .service-description {
        color: var(--cw-neutral-600);
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-size: 0.9375rem;
    }

    /* Service Details Grid */
    .service-details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .detail-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .detail-item:hover {
        background: white;
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-sm);
    }

    .detail-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: var(--cw-brand-primary);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .detail-content {
        flex: 1;
        min-width: 0;
    }

    .detail-label {
        color: var(--cw-neutral-600);
        font-size: 0.8125rem;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }

    /* Price Details */
    .price-current {
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
    }

    .price-original {
        font-size: 0.9375rem;
        color: var(--cw-neutral-500);
        text-decoration: line-through;
        margin-bottom: 0.125rem;
    }

    .price-discounted {
        font-size: 1.125rem;
        font-weight: 700;
        color: #dc2626;
        font-family: var(--cw-font-heading);
    }

    /* Duration Details */
    .duration-time {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
    }

    /* Availability Status */
    .status-available {
        font-size: 0.9375rem;
        font-weight: 600;
        color: #10b981;
    }

    .status-unavailable {
        font-size: 0.9375rem;
        font-weight: 600;
        color: #dc2626;
    }

    /* Booking Info */
    .booking-required {
        font-size: 0.9375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    .booking-walkin {
        font-size: 0.9375rem;
        font-weight: 600;
        color: #10b981;
    }

    /* Service Actions */
    .service-actions {
        display: flex;
        gap: 0.75rem;
    }

    .btn-service-primary {
        flex: 1;
        background: var(--cw-brand-primary);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .btn-service-primary:hover {
        background: var(--cw-brand-light);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-service-secondary {
        flex: 1;
        background: transparent;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .btn-service-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    /* Responsive Design for Service Cards */
    @media (max-width: 992px) {
        .services-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .services-header {
            flex-direction: column;
            text-align: center;
            gap: 1.5rem;
        }

        .services-header-content h2.services-title {
            font-size: 1.75rem;
        }

        .services-subtitle {
            font-size: 1rem;
        }
    }

    @media (max-width: 768px) {
        .services-showcase {
            margin: 2rem 0;
        }

        .services-header {
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .services-header-content h2.services-title {
            font-size: 1.5rem;
        }

        .services-grid {
            grid-template-columns: 1fr;
            gap: 1.25rem;
        }

        .service-content {
            padding: 1.25rem;
        }

        .service-header {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }

        .service-name {
            font-size: 1.25rem;
        }

        .service-details-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .service-actions {
            flex-direction: column;
        }

        .count-number {
            font-size: 1.75rem;
        }

        .services-count {
            min-width: 100px;
            padding: 0.75rem;
        }
    }

    @media (max-width: 480px) {
        .services-header {
            padding: 1rem;
        }

        .service-content {
            padding: 1rem;
        }

        .service-image-container {
            height: 150px;
        }

        .service-indicators {
            top: 0.75rem;
            right: 0.75rem;
        }

        .indicator-featured {
            width: 2rem;
            height: 2rem;
            padding: 0.375rem;
            font-size: 0.75rem;
        }

        .detail-item {
            padding: 0.75rem;
            gap: 0.5rem;
        }

        .detail-icon {
            width: 2rem;
            height: 2rem;
            font-size: 0.75rem;
        }

        .btn-service-primary,
        .btn-service-secondary {
            padding: 0.625rem 1rem;
            font-size: 0.8125rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Venue Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-cw">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_search' %}">Venues</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ venue.venue_name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Venue Information -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <!-- Venue Images Carousel -->
            <div id="venueCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                <div class="carousel-inner rounded overflow-hidden ratio ratio-21x9">
                    {% if primary_image %}
                    <div class="carousel-item active">
                        <img src="{{ primary_image.image.url }}" alt="{{ primary_image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% elif venue.main_image %}
                    <div class="carousel-item active">
                        <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% endif %}

                    {% for image in gallery_images %}
                    <div class="carousel-item {% if not primary_image and not venue.main_image and forloop.first %}active{% endif %}">
                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% empty %}
                    {% if not primary_image and not venue.main_image %}
                    <div class="carousel-item active">
                        <div class="bg-light d-flex align-items-center justify-content-center h-100">
                            <i class="fas fa-image fa-4x text-muted"></i>
                            <p class="text-muted mt-3">No images available</p>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                <!-- Carousel controls (only show if there are multiple images) -->
                {% if images.count > 1 %}
                <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>

                <!-- Carousel indicators -->
                <div class="carousel-indicators">
                    {% if primary_image or venue.main_image %}
                    <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Primary image"></button>
                    {% endif %}
                    {% for image in gallery_images %}
                    <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="{% if primary_image or venue.main_image %}{{ forloop.counter }}{% else %}{{ forloop.counter0 }}{% endif %}" aria-label="Image {{ forloop.counter }}"></button>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Venue Description -->
            <div class="card-cw card-cw-featured mb-4">
                <div class="card-body p-4">
                    <h1 class="card-title h2 mb-3">{{ venue.venue_name }}</h1>
                    <p class="text-muted mb-3">{{ venue.service_provider.business_name }}</p>

                    <!-- Categories -->
                    {% if venue.categories.all %}
                    <div class="mb-3">
                        {% for category in venue.categories.all %}
                            <span class="badge-cw-secondary me-2 mb-2">{{ category.category_name }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Price Range -->
                    {% if price_range %}
                    <div class="mb-3">
                        <span class="badge-cw-primary">{{ price_range }}</span>
                    </div>
                    {% endif %}

                    <p class="card-text">{{ venue.short_description }}</p>

                    <!-- Tags -->
                    {% if venue.tags %}
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-tags me-1"></i>
                            {{ venue.tags }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Discount Summary -->
            {% venue_discount_summary venue %}

            <div class="accordion accordion-cw mb-4" id="venueInfoAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingInfo">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInfo" aria-expanded="true" aria-controls="collapseInfo">
                            <i class="fas fa-info-circle me-2"></i>Venue Information
                        </button>
                    </h2>
                    <div id="collapseInfo" class="accordion-collapse collapse show" aria-labelledby="headingInfo">
                        <div class="accordion-body">
                            <div class="mb-3">
                                <h6><i class="fas fa-map-marker-alt me-2"></i>Location</h6>
                                <p class="mb-1">{{ venue.full_address }}</p>
                            </div>
                            <!-- Structured Operating Hours -->
                            {% if venue.operating_hours_set.all %}
                            <div class="mb-3">
                                <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                <div class="row">
                                    {% for hours in venue.operating_hours_set.all %}
                                    <div class="col-md-6 mb-1">
                                        <span class="fw-bold">{{ hours.get_day_display }}:</span>
                                        {% if hours.is_closed %}
                                            <span class="text-muted">Closed</span>
                                        {% else %}
                                            <span>{{ hours.opening|time:"g:i A" }} - {{ hours.closing|time:"g:i A" }}</span>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% elif venue.operating_hours %}
                            <div class="mb-3">
                                <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                <p class="mb-1">{{ venue.operating_hours|linebreaks }}</p>
                            </div>
                            {% endif %}
                            {% if venue.opening_notes %}
                            <div class="mb-3">
                                <h6><i class="fas fa-exclamation-circle me-2"></i>Special Notes</h6>
                                <p class="mb-1">{{ venue.opening_notes|linebreaks }}</p>
                            </div>
                            {% endif %}
                            <!-- Contact Information - Only show if visible and has content -->
                            {% if venue.show_contact_info and venue.phone or venue.email or venue.website_url %}
                            <div class="mb-3">
                                <h6><i class="fas fa-phone me-2"></i>Contact</h6>
                                {% if venue.phone %}
                                    <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ venue.phone }}</p>
                                {% endif %}
                                {% if venue.email %}
                                    <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ venue.email }}</p>
                                {% endif %}
                                {% if venue.website_url %}
                                    <p class="mb-1"><i class="fas fa-globe me-2"></i><a href="{{ venue.website_url }}" target="_blank" rel="noopener" class="text-decoration-none" style="color: var(--cw-brand-primary);">Visit Website</a></p>
                                {% endif %}
                            </div>
                            {% endif %}
                            
                            <!-- Social Media Links - Only show if visible and has content -->
                            {% if venue.show_social_media and venue.instagram_url or venue.facebook_url or venue.twitter_url or venue.linkedin_url %}
                            <div class="mb-3">
                                <h6><i class="fas fa-share-alt me-2"></i>Follow Us</h6>
                                <div class="d-flex gap-2">
                                    {% if venue.instagram_url %}
                                    <a href="{{ venue.instagram_url }}" target="_blank" rel="noopener" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                    {% endif %}
                                    {% if venue.facebook_url %}
                                    <a href="{{ venue.facebook_url }}" target="_blank" rel="noopener" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook"></i>
                                    </a>
                                    {% endif %}
                                    {% if venue.twitter_url %}
                                    <a href="{{ venue.twitter_url }}" target="_blank" rel="noopener" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    {% endif %}
                                    {% if venue.linkedin_url %}
                                    <a href="{{ venue.linkedin_url }}" target="_blank" rel="noopener" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-linkedin"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amenities Section -->
            {% if venue.show_amenities and venue.amenities.all %}
            <div class="card-cw mb-4">
                <div class="card-body p-4">
                    <h5 class="card-title"><i class="fas fa-star me-2"></i>Amenities & Features</h5>
                    <div class="row">
                        {% for amenity in venue.amenities.all %}
                        {% if amenity.is_active %}
                        <div class="col-md-6 col-lg-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2" style="color: var(--cw-brand-primary);"></i>
                                <span>
                                    {% if amenity.custom_name %}
                                        {{ amenity.custom_name }}
                                    {% else %}
                                        {{ amenity.get_amenity_type_display }}
                                    {% endif %}
                                </span>
                            </div>
                            
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Team Members Section -->
            {% if venue.show_team_members and team_members %}
            <div class="card-cw mb-4">
                <div class="card-body p-4">
                    <h5 class="card-title"><i class="fas fa-users me-2"></i>Meet Our Team</h5>
                    <div class="row g-4">
                        {% for member in team_members %}
                        <div class="col-lg-4 col-md-6">
                            <div class="team-member-card h-100">
                                <div class="team-member-image-container">
                                    {% if member.photo %}
                                        <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="team-member-image">
                                    {% else %}
                                        <div class="team-member-placeholder">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="team-member-info">
                                    <h6 class="team-member-name">{{ member.name }}</h6>
                                    <p class="team-member-position mb-0">{{ member.position }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

        </div>
    </div>

    <!-- Services Section -->
    {% if services %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="services-showcase">
                <!-- Services Header -->
                <div class="services-header">
                    <div class="services-header-content">
                        <h2 class="services-title">
                            <i class="fas fa-spa me-2"></i>Our Services
                        </h2>
                        <p class="services-subtitle">Discover our range of professional services designed for your comfort and well-being</p>
                    </div>
                    <div class="services-count">
                        <span class="count-number">{{ services.count }}</span>
                        <span class="count-label">Service{{ services.count|pluralize }}</span>
                    </div>
                </div>

                <!-- Enhanced Services Grid -->
                <div class="services-grid">
                    {% for service in services %}
                    <div class="card-cw-accent d-flex flex-row align-items-stretch justify-content-between p-4" style="background: var(--cw-accent-light); border: 1px solid var(--cw-brand-accent); border-radius: 1rem; box-shadow: var(--cw-shadow-sm); min-height: 120px;">
                        <!-- Left: Service Info -->
                        <div class="flex-grow-1 pe-4 d-flex flex-column justify-content-center">
                            <div class="d-flex align-items-center mb-1" style="gap: 0.5rem;">
                                <span class="fw-semibold" style="font-family: var(--cw-font-heading); color: var(--cw-brand-primary); font-size: 1.1rem;">{{ service.service_title }}</span>
                                <span style="color: var(--cw-neutral-600); font-size: 1rem;">&bull;</span>
                                <span style="color: var(--cw-neutral-600); font-size: 1rem;">{{ service.duration_display }}</span>
                                {% if service.service_category %}
                                    <span class="service-category-tag ms-2">
                                        <i class="{{ service.service_category.icon_class|default:'fas fa-tag' }}"></i>
                                        {{ service.service_category.name }}
                                    </span>
                                {% endif %}
                            </div>
                            <div class="text-neutral-cw mb-2" style="font-family: var(--cw-font-primary); font-size: 0.97rem; color: var(--cw-neutral-600); white-space: pre-line;">
                                {{ service.short_description }}
                            </div>
                            <!-- Details Row (Booking, Status) -->
                            <div class="d-flex flex-wrap align-items-center gap-3 mt-1">
                                <div>
                                    {% if service.requires_booking %}
                                        <span class="badge-cw-secondary">Advance booking ({{ service.min_advance_booking_hours }}h min)</span>
                                    {% else %}
                                        <span class="badge-cw-secondary">Walk-in welcome</span>
                                    {% endif %}
                                </div>
                                <div>
                                    {% if service.is_active %}
                                        <span class="badge bg-success">Available</span>
                                    {% else %}
                                        <span class="badge bg-danger">Unavailable</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <!-- Right: Price, Discount, Actions -->
                        <div class="d-flex flex-column align-items-end justify-content-between" style="min-width: 140px;">
                            <div class="mb-2">
                                {% if service.is_featured %}
                                    <span class="badge-cw-primary mb-1" title="Featured Service"><i class="fas fa-star me-1"></i>Featured</span><br>
                                {% endif %}
                                {% if service|has_service_discount %}
                                    <span class="price-original d-block">${{ service.price }}</span>
                                    <span class="price-discounted d-block">${{ service|get_service_discounted_price }}</span>
                                    <span class="indicator-discount ms-0 mt-1 d-inline-block" title="Special Offer">
                                        <i class="fas fa-tag me-1"></i>{{ service|get_service_best_discount|format_discount_value }}
                                    </span>
                                {% else %}
                                    <span class="fw-bold price-current d-block" style="font-family: var(--cw-font-heading); color: var(--cw-brand-primary); font-size: 1.15rem;">{{ service.price_display }}</span>
                                {% endif %}
                            </div>
                            <div class="d-flex gap-2 mt-auto">
                                <a href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug=service.slug %}"
                                   class="btn btn-cw-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                                {% if service.is_active %}
                                    <button class="btn btn-cw-secondary btn-sm" onclick="openBookingModal('{{ service.id }}', '{{ service.service_title }}')">
                                        <i class="fas fa-calendar-plus me-1"></i>Book Now
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- FAQs Section -->
    {% if venue.show_faqs and faqs %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="accordion accordion-cw" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFaq">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFaq" aria-expanded="true" aria-controls="collapseFaq">
                            <i class="fas fa-question-circle me-2"></i>Frequently Asked Questions
                        </button>
                    </h2>
                    <div id="collapseFaq" class="accordion-collapse collapse show" aria-labelledby="headingFaq">
                        <div class="accordion-body">
                            {% for faq in faqs %}
                            <div class="faq-item mb-3">
                                <h6 class="fw-bold mb-2" style="color: var(--cw-brand-primary);">{{ faq.question }}</h6>
                                <p class="mb-0">{{ faq.answer|linebreaks }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Flag Venue Button (Fixed Position) -->
{% if can_flag %}
<a href="{% url 'venues_app:flag_venue' venue_slug=venue.slug %}" class="fixed-action-btn" style="right: 2rem;">
    <i class="fas fa-flag me-2"></i>{% trans "Report Issue" %}
</a>
{% endif %}

<!-- Favorite Button (Fixed Position) -->
{% if user.is_authenticated and user.is_customer %}
<button id="favorite-btn" data-venue-id="{{ venue.id }}" class="fixed-action-btn" style="left: 2rem;" aria-label="Toggle favorite">
    <i class="fas fa-heart me-2"></i>Favorite
</button>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Existing favorite button functionality
  const btn = document.getElementById('favorite-btn');
  if (btn) {
    const venueId = btn.dataset.venueId;
    const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    function updateStatus(isFav) {
      if (isFav) {
        btn.classList.add('text-danger', 'active');
      } else {
        btn.classList.remove('text-danger', 'active');
      }
    }
    fetch('{% url "dashboard_app:check_favorite_status" venue_id=0 %}'.replace('0', venueId))
      .then(r => r.json())
      .then(data => updateStatus(data.is_favorite));
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      const isFav = btn.classList.contains('active');
      const url = isFav ? '{% url "dashboard_app:remove_favorite_venue" venue_id=0 %}' : '{% url "dashboard_app:add_favorite_venue" venue_id=0 %}';
      fetch(url.replace('0', venueId), {
        method: 'POST',
        headers: { 'X-CSRFToken': csrftoken }
      }).then(r => r.json()).then(data => {
        if (data.success) {
          updateStatus(!isFav);
        }
      });
    });
  }

  // Enhanced Service Card Interactions
  const serviceCards = document.querySelectorAll('.service-card-enhanced');
  
  serviceCards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });

  // Service category tag interactions
  const categoryTags = document.querySelectorAll('.service-category-tag');
  
  categoryTags.forEach(tag => {
    tag.addEventListener('click', function(e) {
      e.preventDefault();
      const categoryName = this.textContent.trim();
      
      showToast(`Viewing services in category: ${categoryName}`, 'info');
      
      // Scroll to services section
      document.querySelector('.services-showcase').scrollIntoView({ 
        behavior: 'smooth' 
      });
    });
  });
});

// Enhanced Service Booking Modal Function
function openBookingModal(serviceId, serviceName) {
    // Check if user is authenticated
    {% if not user.is_authenticated %}
        // Redirect to login if not authenticated
        window.location.href = '{% url "accounts_app:customer_login" %}?next=' + encodeURIComponent(window.location.pathname);
        return;
    {% endif %}

    // Create and show booking modal
    const modalHtml = `
        <div class="modal fade" id="bookingModal" tabindex="-1" aria-labelledby="bookingModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="bookingModalLabel">
                            <i class="fas fa-calendar-plus me-2"></i>Book Service: ${serviceName}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading booking options...</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug='PLACEHOLDER' %}".replace('PLACEHOLDER', serviceId) 
                           class="btn btn-primary">
                            View Full Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('bookingModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('bookingModal'));
    modal.show();

    // Load booking form content
    setTimeout(() => {
        const modalBody = document.querySelector('#bookingModal .modal-body');
        modalBody.innerHTML = `
            <div class="booking-quick-info">
                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <div class="info-card p-3 bg-light rounded">
                            <h6 class="mb-2"><i class="fas fa-spa me-2 text-primary"></i>Service</h6>
                            <p class="mb-0 fw-semibold">${serviceName}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-card p-3 bg-light rounded">
                            <h6 class="mb-2"><i class="fas fa-building me-2 text-primary"></i>Venue</h6>
                            <p class="mb-0 fw-semibold">{{ venue.venue_name }}</p>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Quick Booking:</strong> For detailed scheduling and availability, please visit the service details page where you can view available time slots and complete your booking.
                </div>
                <div class="d-grid gap-2">
                    <a href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug='PLACEHOLDER' %}".replace('PLACEHOLDER', serviceId) 
                       class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>Continue to Full Booking
                    </a>
                </div>
            </div>
        `;
    }, 1000);

    // Clean up modal after hiding
    document.getElementById('bookingModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Utility function to show toast notifications
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'info' ? 'primary' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Show toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    
    // Remove toast element after hiding
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Add smooth scrolling for internal links
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
{% endblock %}
