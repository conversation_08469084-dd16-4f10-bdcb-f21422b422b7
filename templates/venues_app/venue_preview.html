{% extends 'base.html' %}
{% load discount_tags i18n %}

{% block title %}Preview: {{ venue.venue_name }} - {{ venue.service_provider.business_name }}{% endblock %}

{% block extra_css %}
<style>
    /* Preview Mode Styles */
    .preview-banner {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        padding: 1rem;
        text-align: center;
        position: sticky;
        top: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .preview-banner h2 {
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }
    
    .preview-controls {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 0.5rem;
    }
    
    .preview-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.2s ease;
    }
    
    .preview-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .completeness-indicator {
        position: fixed;
        top: 120px;
        right: 1rem;
        background: white;
        border-radius: 1rem;
        padding: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-width: 300px;
        z-index: 999;
    }
    
    .completeness-score {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .score-badge {
        background: #2F160F;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 700;
        font-size: 0.9rem;
    }
    
    .missing-items {
        max-height: 200px;
        overflow-y: auto;
    }
    
    .missing-item {
        background: #fef3c7;
        border-left: 3px solid #f59e0b;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        border-radius: 0 0.25rem 0.25rem 0;
        font-size: 0.8rem;
    }
    
    .missing-item.high-priority {
        background: #fee2e2;
        border-left-color: #dc2626;
    }
    
    .freshness-indicator {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        z-index: 10;
    }
    
    .freshness-recent {
        background: rgba(34, 197, 94, 0.8);
    }
    
    .freshness-stale {
        background: rgba(239, 68, 68, 0.8);
    }
    
    .section-wrapper {
        position: relative;
    }
    
    .section-hidden {
        opacity: 0.3;
        position: relative;
        pointer-events: none;
    }
    
    .section-hidden::after {
        content: "Hidden from customers";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        z-index: 10;
    }
    
    .empty-section {
        background: #f9fafb;
        border: 2px dashed #d1d5db;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
        color: #6b7280;
        margin: 1rem 0;
    }
    
    .empty-section-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .add-content-btn {
        background: #2F160F;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        transition: all 0.2s ease;
    }
    
    .add-content-btn:hover {
        background: #4a2a1f;
        color: white;
        text-decoration: none;
    }
    
    /* CozyWish Design System - Venue Detail (from original) */
    :root {
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    .breadcrumb-cw {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-cw .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .breadcrumb-cw .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-cw .breadcrumb-item.active {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    .card-cw {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-featured {
        border: 2px solid var(--cw-brand-primary);
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
    }

    .badge-cw-secondary {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .accordion-cw .accordion-item {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .accordion-cw .accordion-button {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        font-weight: 600;
        border: none;
        padding: 1.25rem 1.5rem;
    }

    .accordion-cw .accordion-button:not(.collapsed) {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        box-shadow: none;
    }

    .accordion-cw .accordion-body {
        background: white;
        padding: 1.5rem;
    }

    .service-item {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: white;
        transition: all 0.2s ease;
    }

    .service-item:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
    }

    .service-name {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .service-description {
        color: var(--cw-neutral-600);
        margin-bottom: 1rem;
    }

    .price-info .text-success {
        color: var(--cw-brand-primary) !important;
    }
    
    @media (max-width: 768px) {
        .completeness-indicator {
            position: relative;
            top: auto;
            right: auto;
            margin: 1rem;
            max-width: none;
        }
        
        .preview-controls {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Preview Mode Banner -->
<div class="preview-banner">
    <h2><i class="fas fa-eye"></i> Preview Mode</h2>
    <p>This is how your venue appears to customers. Hidden sections are shown with reduced opacity.</p>
    <div class="preview-controls">
        <a href="{% url 'venues_app:manage_venue_visibility' %}" class="preview-btn">
            <i class="fas fa-cog"></i> Manage Visibility
        </a>
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="preview-btn">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<!-- Completeness Indicator -->
<div class="completeness-indicator">
    <div class="completeness-score">
        <strong>Profile Completeness</strong>
        <span class="score-badge">{{ completeness_score }}%</span>
    </div>
    
    {% if missing_info %}
    <div class="missing-items">
        <small><strong>Areas for Improvement:</strong></small>
        {% for item in missing_info %}
        <div class="missing-item {% if item.priority == 'high' %}high-priority{% endif %}">
            <strong>{{ item.title }}</strong><br>
            <small>{{ item.message }}</small>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>

<div class="container py-4">
    <!-- Venue Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-cw">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_search' %}">Venues</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ venue.venue_name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Venue Information -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <!-- Venue Images Carousel -->
            <div class="section-wrapper">
                {% if freshness_info.description.is_recent %}
                    <div class="freshness-indicator freshness-recent">Recently updated</div>
                {% elif freshness_info.description.is_stale %}
                    <div class="freshness-indicator freshness-stale">Needs updating</div>
                {% endif %}
                
                {% if not images.exists and not venue.main_image %}
                <div class="empty-section">
                    <div class="empty-section-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <h4>No Images Available</h4>
                    <p>Add attractive images to showcase your venue and attract more customers.</p>
                    <a href="{% url 'venues_app:manage_venue_images' %}" class="add-content-btn">
                        <i class="fas fa-plus"></i> Add Images
                    </a>
                </div>
                {% else %}
                <div id="venueCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                    <div class="carousel-inner rounded overflow-hidden ratio ratio-21x9">
                        {% if primary_image %}
                        <div class="carousel-item active">
                            <img src="{{ primary_image.image.url }}" alt="{{ primary_image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                        </div>
                        {% elif venue.main_image %}
                        <div class="carousel-item active">
                            <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                        </div>
                        {% endif %}

                        {% for image in gallery_images %}
                        <div class="carousel-item {% if not primary_image and not venue.main_image and forloop.first %}active{% endif %}">
                            <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Carousel controls -->
                    {% if images.count > 1 %}
                    <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>

            <!-- Venue Description -->
            <div class="section-wrapper">
                <div class="card-cw card-cw-featured mb-4">
                    <div class="card-body p-4">
                        <h1 class="card-title h2 mb-3">{{ venue.venue_name }}</h1>
                        <p class="text-muted mb-3">{{ venue.service_provider.business_name }}</p>

                        <!-- Categories -->
                        {% if venue.categories.all %}
                        <div class="mb-3">
                            {% for category in venue.categories.all %}
                                <span class="badge-cw-secondary me-2 mb-2">{{ category.category_name }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Price Range -->
                        {% if price_range %}
                        <div class="mb-3">
                            <span class="badge-cw-primary">{{ price_range }}</span>
                        </div>
                        {% endif %}

                        {% if venue.short_description %}
                            <p class="card-text">{{ venue.short_description }}</p>
                        {% else %}
                            <div class="empty-section">
                                <div class="empty-section-icon">
                                    <i class="fas fa-file-text"></i>
                                </div>
                                <h4>No Description Available</h4>
                                <p>Add a compelling description to tell customers about your venue.</p>
                                <a href="{% url 'venues_app:venue_edit' %}" class="add-content-btn">
                                    <i class="fas fa-plus"></i> Add Description
                                </a>
                            </div>
                        {% endif %}

                        <!-- Tags -->
                        {% if venue.tags %}
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-tags me-1"></i>
                                {{ venue.tags }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Venue Information -->
            <div class="section-wrapper {% if not venue.show_contact_info and not venue.show_operating_hours %}section-hidden{% endif %}">
                <div class="accordion accordion-cw mb-4" id="venueInfoAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingInfo">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInfo" aria-expanded="true" aria-controls="collapseInfo">
                                <i class="fas fa-info-circle me-2"></i>Venue Information
                            </button>
                        </h2>
                        <div id="collapseInfo" class="accordion-collapse collapse show" aria-labelledby="headingInfo">
                            <div class="accordion-body">
                                <div class="mb-3">
                                    <h6><i class="fas fa-map-marker-alt me-2"></i>Location</h6>
                                    <p class="mb-1">{{ venue.full_address }}</p>
                                </div>
                                
                                <!-- Operating Hours -->
                                {% if venue.show_operating_hours %}
                                    {% if venue.operating_hours_set.all %}
                                    <div class="mb-3">
                                        {% if freshness_info.hours.is_stale %}
                                            <div class="freshness-indicator freshness-stale">Needs updating</div>
                                        {% endif %}
                                        <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                        <div class="row">
                                            {% for hours in venue.operating_hours_set.all %}
                                            <div class="col-md-6 mb-1">
                                                <span class="fw-bold">{{ hours.get_day_display }}:</span>
                                                {% if hours.is_closed %}
                                                    <span class="text-muted">Closed</span>
                                                {% else %}
                                                    <span>{{ hours.opening|time:"g:i A" }} - {{ hours.closing|time:"g:i A" }}</span>
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% elif venue.operating_hours %}
                                    <div class="mb-3">
                                        <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                        <p class="mb-1">{{ venue.operating_hours|linebreaks }}</p>
                                    </div>
                                    {% else %}
                                    <div class="empty-section">
                                        <div class="empty-section-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <h4>No Operating Hours</h4>
                                        <p>Add your operating hours so customers know when you're open.</p>
                                        <a href="{% url 'venues_app:manage_operating_hours' %}" class="add-content-btn">
                                            <i class="fas fa-plus"></i> Add Hours
                                        </a>
                                    </div>
                                    {% endif %}
                                {% endif %}
                                
                                {% if venue.opening_notes %}
                                <div class="mb-3">
                                    <h6><i class="fas fa-exclamation-circle me-2"></i>Special Notes</h6>
                                    <p class="mb-1">{{ venue.opening_notes|linebreaks }}</p>
                                </div>
                                {% endif %}
                                
                                <!-- Contact Information -->
                                {% if venue.show_contact_info %}
                                <div class="mb-3">
                                    {% if freshness_info.contact.is_stale %}
                                        <div class="freshness-indicator freshness-stale">Needs updating</div>
                                    {% endif %}
                                    <h6><i class="fas fa-phone me-2"></i>Contact</h6>
                                    {% if venue.phone %}
                                        <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ venue.phone }}</p>
                                    {% endif %}
                                    {% if venue.email %}
                                        <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ venue.email }}</p>
                                    {% endif %}
                                    {% if venue.website_url %}
                                        <p class="mb-1"><i class="fas fa-globe me-2"></i><a href="{{ venue.website_url }}" target="_blank" rel="noopener" class="text-decoration-none" style="color: var(--cw-brand-primary);">Visit Website</a></p>
                                    {% endif %}
                                    
                                    {% if not venue.phone and not venue.email and not venue.website_url %}
                                    <div class="empty-section">
                                        <div class="empty-section-icon">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <h4>No Contact Information</h4>
                                        <p>Add contact details so customers can reach you.</p>
                                        <a href="{% url 'venues_app:venue_edit' %}" class="add-content-btn">
                                            <i class="fas fa-plus"></i> Add Contact Info
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amenities Section -->
            <div class="section-wrapper {% if not venue.show_amenities %}section-hidden{% endif %}">
                {% if venue.show_amenities %}
                    {% if amenities %}
                    <div class="card-cw mb-4">
                        <div class="card-body p-4">
                            {% if freshness_info.amenities.is_stale %}
                                <div class="freshness-indicator freshness-stale">Needs updating</div>
                            {% endif %}
                            <h5 class="card-title"><i class="fas fa-star me-2"></i>Amenities & Features</h5>
                            <div class="row">
                                {% for amenity in amenities %}
                                <div class="col-md-6 col-lg-4 mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle me-2" style="color: var(--cw-brand-primary);"></i>
                                        <span>
                                            {% if amenity.custom_name %}
                                                {{ amenity.custom_name }}
                                            {% else %}
                                                {{ amenity.get_amenity_type_display }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="empty-section">
                        <div class="empty-section-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h4>No Amenities Listed</h4>
                        <p>Add amenities to highlight what makes your venue special.</p>
                        <a href="{% url 'venues_app:manage_amenities' %}" class="add-content-btn">
                            <i class="fas fa-plus"></i> Add Amenities
                        </a>
                    </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Services Section -->
    <div class="section-wrapper">
        {% if services %}
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-4"><i class="fas fa-spa me-2"></i>Our Services</h2>
                <div class="row">
                    {% for service in services %}
                    <div class="col-lg-6 mb-4">
                        <div class="service-item">
                            <h4 class="service-name">{{ service.service_title }}</h4>
                            <p class="service-description">{{ service.description|truncatewords:20 }}</p>
                            <div class="price-info">
                                <span class="text-success fw-bold">
                                    {% if service.price_max and service.price_max != service.price_min %}
                                        ${{ service.price_min }} - ${{ service.price_max }}
                                    {% else %}
                                        ${{ service.price_min }}
                                    {% endif %}
                                </span>
                                <span class="text-muted">
                                    {% if service.duration %}
                                        • {{ service.duration }} minutes
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% else %}
        <div class="empty-section">
            <div class="empty-section-icon">
                <i class="fas fa-spa"></i>
            </div>
            <h4>No Services Listed</h4>
            <p>Add your services to show customers what you offer and start taking bookings.</p>
            <a href="{% url 'venues_app:service_create' %}" class="add-content-btn">
                <i class="fas fa-plus"></i> Add Services
            </a>
        </div>
        {% endif %}
    </div>

    <!-- FAQs Section -->
    <div class="section-wrapper {% if not venue.show_faqs %}section-hidden{% endif %}">
        {% if venue.show_faqs %}
            {% if faqs %}
            <div class="row mb-5">
                <div class="col-12">
                    <h2 class="mb-4"><i class="fas fa-question-circle me-2"></i>Frequently Asked Questions</h2>
                    <div class="accordion accordion-cw" id="faqAccordion">
                        {% for faq in faqs %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ forloop.counter }}" aria-expanded="false" aria-controls="collapse{{ forloop.counter }}">
                                    {{ faq.question }}
                                </button>
                            </h2>
                            <div id="collapse{{ forloop.counter }}" class="accordion-collapse collapse" aria-labelledby="heading{{ forloop.counter }}" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    {{ faq.answer|linebreaks }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="empty-section">
                <div class="empty-section-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h4>No FAQs Available</h4>
                <p>Add frequently asked questions to help customers understand your services better.</p>
                <a href="{% url 'venues_app:manage_faqs' %}" class="add-content-btn">
                    <i class="fas fa-plus"></i> Add FAQs
                </a>
            </div>
            {% endif %}
        {% endif %}
    </div>

    <!-- Team Members Section -->
    {% if venue.show_team_members and team_members %}
    <div class="section-wrapper">
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="mb-4"><i class="fas fa-users me-2"></i>Meet Our Team</h2>
                <div class="row">
                    {% for member in team_members %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="team-member-card">
                            <div class="team-member-image-container">
                                {% if member.profile_image %}
                                <img src="{{ member.profile_image.url }}" alt="{{ member.name }}" class="team-member-image">
                                {% else %}
                                <div class="team-member-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                                {% endif %}
                            </div>
                            <div class="team-member-info">
                                <h5 class="team-member-name">{{ member.name }}</h5>
                                <p class="team-member-position">{{ member.position }}</p>
                                {% if member.bio %}
                                <p class="team-member-bio">{{ member.bio|truncatewords:15 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 