{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}Manage Operating Hours - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #fae1d7;
        font-size: 1.25rem;
    }

    .form-label {
        font-weight: 600;
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-text {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-check-label {
        font-weight: 500;
        color: #525252;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
    }

    /* Button styling */
    .btn-cw-primary {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: block;
        margin: 2rem auto 0;
        width: fit-content;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }

    /* Template apply button */
    .btn-cw-template {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 2rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        margin-top: 1rem;
    }

    .btn-cw-template:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    /* Template Selection */
    .template-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .template-section h3 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .template-section h3 i {
        color: #fae1d7;
        font-size: 1.25rem;
    }

    .template-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .template-card {
        background: white;
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .template-card:hover {
        border-color: #2F160F;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .template-card.selected {
        border-color: #2F160F;
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .template-name {
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        color: #2F160F;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .template-description {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 0.9rem;
        margin-bottom: 0.75rem;
    }

    .template-preview {
        font-family: 'Inter', sans-serif;
        font-size: 0.8rem;
        color: #525252;
        background: rgba(255, 255, 255, 0.8);
        padding: 0.5rem;
        border-radius: 0.375rem;
        border: 1px solid #fae1d7;
    }

    /* Custom Schedule Section */
    .custom-schedule-section {
        margin-top: 2rem;
        display: none;
    }

    .custom-schedule-section.active {
        display: block;
    }

    .schedule-section h3 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .schedule-section h3 i {
        color: #fae1d7;
        font-size: 1.25rem;
    }

    /* Day Cards */
    .days-grid {
        display: grid;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .day-card {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .day-card:hover {
        border-color: #2F160F;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .day-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .day-name {
        font-family: 'Poppins', sans-serif;
        font-size: 1.125rem;
        font-weight: 500;
        color: #2F160F;
    }

    .day-controls {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1rem;
        align-items: end;
    }

    /* Form Controls */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 500;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        display: block;
    }

    .form-control {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        background: white;
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control:disabled {
        background: var(--cw-neutral-100);
        color: var(--cw-neutral-500);
        cursor: not-allowed;
    }

    /* Status Indicators */
    .status-closed { background: #fef2f2; border-color: #fecaca; }
    .status-regular { background: #f0fdf4; border-color: #bbf7d0; }
    .status-24hours { background: #eff6ff; border-color: #bfdbfe; }
    .status-overnight { background: #fef3c7; border-color: #fde68a; }

    /* Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        cursor: pointer;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-cw-template {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-template:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        padding: 2rem;
        background: var(--cw-gradient-card-subtle);
        border-top: 1px solid var(--cw-brand-accent);
    }

    /* Alerts */
    .alert {
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        border: none;
        font-family: var(--cw-font-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .alert-info {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        color: #1e40af;
        border-left: 4px solid #3b82f6;
    }

    .alert-success {
        background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        color: #166534;
        border-left: 4px solid #22c55e;
    }

    .alert-warning {
        background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        color: #92400e;
        border-left: 4px solid #f59e0b;
    }

    .alert-error {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        color: #991b1b;
        border-left: 4px solid #ef4444;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hours-container {
            padding: 0 0.5rem;
        }
        
        .hours-title {
            font-size: 2rem;
        }
        
        .template-grid {
            grid-template-columns: 1fr;
        }
        
        .day-controls {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

<!-- Edit Form -->
<form method="post" novalidate>
    {% csrf_token %}

    <!-- Custom Schedule Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-calendar-alt me-2"></i>{% trans "Weekly Schedule" %}
        </h4>

        <div class="days-grid">
            {% for day in form.DAYS %}
                {% with day_key=day.0 day_name=day.1 %}
                <div class="day-card" data-day="{{ day_key }}">
                    <div class="day-header">
                        <h5 class="day-name">{{ day_name }}</h5>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">{% trans "Status" %}</label>
                            {% with status_field=day_key|add:"_status" %}
                                {% for field in form %}
                                    {% if field.name == status_field %}
                                        {{ field|add_class:"form-control day-status-select" }}
                                        {% if field.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in field.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            {% endwith %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">{% trans "Opening" %}</label>
                            {% with opening_field=day_key|add:"_opening" %}
                                {% for field in form %}
                                    {% if field.name == opening_field %}
                                        {{ field|add_class:"form-control time-select" }}
                                        {% if field.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in field.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            {% endwith %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">{% trans "Closing" %}</label>
                            {% with closing_field=day_key|add:"_closing" %}
                                {% for field in form %}
                                    {% if field.name == closing_field %}
                                        {{ field|add_class:"form-control time-select" }}
                                        {% if field.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in field.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            {% endwith %}
                        </div>
                    </div>

                </div>
                {% endwith %}
            {% endfor %}
        </div>

        <!-- Form-wide errors -->
        {% if form.non_field_errors %}
            <div class="invalid-feedback">
                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- Form Actions -->
    <div class="text-center">
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-save me-2"></i>{% trans "Save Operating Hours" %}
        </button>
    </div>
</form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced operating hours manager
    const operatingHoursManager = new OperatingHoursManager();
});

class OperatingHoursManager {
    constructor() {
        this.days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        this.dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        this.timeSlots = this.generateTimeSlots();
        this.init();
    }

    init() {
        this.setupVisualTimePickers();
        this.setupCopyHours();
        this.setupWeeklySummary();
        this.initializeDefaultView();
    }

    generateTimeSlots() {
        const slots = [];
        for (let hour = 0; hour < 24; hour++) {
            for (let minute = 0; minute < 60; minute += 15) {
                const time24 = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                const period = hour < 12 ? 'AM' : 'PM';
                const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                const display = `${hour12}:${minute.toString().padStart(2, '0')} ${period}`;
                slots.push({ value: time24, display: display });
            }
        }
        return slots;
    }

    setupVisualTimePickers() {
        // Replace existing time selects with visual time pickers
        const timeSelects = document.querySelectorAll('.time-select');
        
        timeSelects.forEach(select => {
            this.createVisualTimePicker(select);
        });
    }

    createVisualTimePicker(originalSelect) {
        const container = document.createElement('div');
        container.className = 'visual-time-picker';
        
        const display = document.createElement('div');
        display.className = 'time-display';
        display.innerHTML = `
            <input type="text" class="time-input" placeholder="--:-- --" readonly />
            <button type="button" class="time-picker-btn">
                <i class="fas fa-clock"></i>
            </button>
        `;
        
        const dropdown = document.createElement('div');
        dropdown.className = 'time-dropdown';
        dropdown.innerHTML = `
            <div class="time-dropdown-header">
                <span>Select Time</span>
                <button type="button" class="close-time-picker">×</button>
            </div>
            <div class="time-slots">
                ${this.timeSlots.map(slot => 
                    `<div class="time-slot" data-value="${slot.value}">${slot.display}</div>`
                ).join('')}
            </div>
        `;
        
        container.appendChild(display);
        container.appendChild(dropdown);
        
        // Replace original select
        originalSelect.style.display = 'none';
        originalSelect.parentNode.insertBefore(container, originalSelect);
        
        // Setup event listeners
        this.setupTimePickerEvents(container, originalSelect);
    }

    setupTimePickerEvents(container, originalSelect) {
        const input = container.querySelector('.time-input');
        const button = container.querySelector('.time-picker-btn');
        const dropdown = container.querySelector('.time-dropdown');
        const closeBtn = container.querySelector('.close-time-picker');
        const timeSlots = container.querySelectorAll('.time-slot');
        
        // Toggle dropdown
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeAllTimePickers();
            dropdown.classList.toggle('show');
        });
        
        // Close dropdown
        closeBtn.addEventListener('click', () => {
            dropdown.classList.remove('show');
        });
        
        // Select time
        timeSlots.forEach(slot => {
            slot.addEventListener('click', () => {
                const value = slot.dataset.value;
                const display = slot.textContent;
                
                input.value = display;
                originalSelect.value = value;
                
                // Trigger change event
                originalSelect.dispatchEvent(new Event('change', { bubbles: true }));
                
                dropdown.classList.remove('show');
            });
        });
        
        // Set initial value
        if (originalSelect.value) {
            const slot = this.timeSlots.find(s => s.value === originalSelect.value);
            if (slot) {
                input.value = slot.display;
            }
        }
    }

    closeAllTimePickers() {
        document.querySelectorAll('.time-dropdown.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }

    setupCopyHours() {
        // Add copy hours controls to each day
        this.days.forEach((day, index) => {
            const dayCard = document.querySelector(`[data-day="${day}"]`);
            if (!dayCard) return;
            
            // Add copy controls
            const copyControls = document.createElement('div');
            copyControls.className = 'copy-hours-controls';
            copyControls.innerHTML = `
                <div class="copy-section">
                    <label class="form-label">Copy Hours</label>
                    <div class="copy-buttons">
                        <button type="button" class="btn-copy-from" data-day="${day}">
                            <i class="fas fa-copy"></i> Copy From
                        </button>
                        <button type="button" class="btn-copy-to" data-day="${day}">
                            <i class="fas fa-paste"></i> Copy To
                        </button>
                    </div>
                </div>
            `;
            
            dayCard.querySelector('.day-controls').appendChild(copyControls);
        });
        
        // Setup copy event listeners
        this.setupCopyEvents();
    }

    setupCopyEvents() {
        // Copy from buttons
        document.querySelectorAll('.btn-copy-from').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const day = btn.dataset.day;
                this.showCopyFromModal(day);
            });
        });
        
        // Copy to buttons
        document.querySelectorAll('.btn-copy-to').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const day = btn.dataset.day;
                this.showCopyToModal(day);
            });
        });
    }

    showCopyFromModal(targetDay) {
        const modal = this.createCopyModal('copy-from', targetDay);
        document.body.appendChild(modal);
        
        // Show available days to copy from
        const daysList = modal.querySelector('.days-list');
        this.days.forEach((day, index) => {
            if (day === targetDay) return;
            
            const dayData = this.getDayData(day);
            const dayItem = document.createElement('div');
            dayItem.className = 'day-copy-item';
            dayItem.innerHTML = `
                <div class="day-copy-info">
                    <strong>${this.dayNames[index]}</strong>
                    <span class="day-status">${this.formatDayStatus(dayData)}</span>
                </div>
                <button type="button" class="btn-select-day" data-day="${day}">Copy</button>
            `;
            
            daysList.appendChild(dayItem);
        });
        
        // Setup copy from events
        modal.querySelectorAll('.btn-select-day').forEach(btn => {
            btn.addEventListener('click', () => {
                const sourceDay = btn.dataset.day;
                this.copyHoursFromTo(sourceDay, targetDay);
                this.closeModal(modal);
            });
        });
    }

    showCopyToModal(sourceDay) {
        const modal = this.createCopyModal('copy-to', sourceDay);
        document.body.appendChild(modal);
        
        // Show checkboxes for multiple day selection
        const daysList = modal.querySelector('.days-list');
        this.days.forEach((day, index) => {
            if (day === sourceDay) return;
            
            const dayItem = document.createElement('div');
            dayItem.className = 'day-copy-item';
            dayItem.innerHTML = `
                <label class="day-checkbox">
                    <input type="checkbox" value="${day}" />
                    <span class="checkmark"></span>
                    <strong>${this.dayNames[index]}</strong>
                </label>
            `;
            
            daysList.appendChild(dayItem);
        });
        
        // Setup copy to events
        const copyBtn = modal.querySelector('.btn-copy-selected');
        copyBtn.addEventListener('click', () => {
            const selectedDays = Array.from(modal.querySelectorAll('input[type="checkbox"]:checked'))
                .map(cb => cb.value);
            
            selectedDays.forEach(targetDay => {
                this.copyHoursFromTo(sourceDay, targetDay);
            });
            
            this.closeModal(modal);
            this.updateWeeklySummary();
        });
    }

    createCopyModal(type, day) {
        const modal = document.createElement('div');
        modal.className = 'hours-modal-overlay';
        
        const isFrom = type === 'copy-from';
        const title = isFrom ? `Copy hours to ${this.getDayName(day)}` : `Copy ${this.getDayName(day)} hours to other days`;
        
        modal.innerHTML = `
            <div class="hours-modal">
                <div class="modal-header">
                    <h4>${title}</h4>
                    <button type="button" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="days-list"></div>
                    ${!isFrom ? '<button type="button" class="btn-copy-selected btn-cw-primary">Copy to Selected Days</button>' : ''}
                </div>
            </div>
        `;
        
        // Setup close events
        modal.querySelector('.modal-close').addEventListener('click', () => {
            this.closeModal(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
            }
        });
        
        return modal;
    }

    closeModal(modal) {
        modal.remove();
    }

    copyHoursFromTo(sourceDay, targetDay) {
        const sourceData = this.getDayData(sourceDay);
        this.setDayData(targetDay, sourceData);
        
        // Show success message
        this.showMessage(`Copied hours from ${this.getDayName(sourceDay)} to ${this.getDayName(targetDay)}`, 'success');
    }

    getDayData(day) {
        const statusSelect = document.querySelector(`[name="${day}_status"]`);
        const openingSelect = document.querySelector(`[name="${day}_opening"]`);
        const closingSelect = document.querySelector(`[name="${day}_closing"]`);
        
        return {
            status: statusSelect?.value || 'closed',
            opening: openingSelect?.value || '',
            closing: closingSelect?.value || ''
        };
    }

    setDayData(day, data) {
        const statusSelect = document.querySelector(`[name="${day}_status"]`);
        const openingSelect = document.querySelector(`[name="${day}_opening"]`);
        const closingSelect = document.querySelector(`[name="${day}_closing"]`);
        
        if (statusSelect) {
            statusSelect.value = data.status;
            statusSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        if (openingSelect) {
            openingSelect.value = data.opening;
            // Update visual time picker
            this.updateTimePickerDisplay(openingSelect);
        }
        
        if (closingSelect) {
            closingSelect.value = data.closing;
            // Update visual time picker
            this.updateTimePickerDisplay(closingSelect);
        }
    }

    updateTimePickerDisplay(select) {
        const container = select.parentNode.querySelector('.visual-time-picker');
        if (container && select.value) {
            const input = container.querySelector('.time-input');
            const slot = this.timeSlots.find(s => s.value === select.value);
            if (slot && input) {
                input.value = slot.display;
            }
        }
    }

    setupWeeklySummary() {
        // Create weekly summary section
        const summarySection = document.createElement('div');
        summarySection.className = 'weekly-summary-section';
        summarySection.innerHTML = `
            <div class="summary-header">
                <h3><i class="fas fa-calendar-week"></i> Weekly Summary</h3>
                <button type="button" class="btn-toggle-summary">
                    <i class="fas fa-eye"></i> Show Summary
                </button>
            </div>
            <div class="summary-content" style="display: none;">
                <div class="summary-grid">
                    ${this.days.map((day, index) => `
                        <div class="summary-day" data-day="${day}">
                            <div class="summary-day-name">${this.dayNames[index]}</div>
                            <div class="summary-day-hours">--</div>
                        </div>
                    `).join('')}
                </div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Open Days:</span>
                        <span class="stat-value" id="open-days-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Hours/Week:</span>
                        <span class="stat-value" id="total-hours-week">0</span>
                    </div>
                </div>
            </div>
        `;
        
        // Insert after custom schedule section
        const customSection = document.querySelector('.custom-schedule-section');
        customSection.parentNode.insertBefore(summarySection, customSection.nextSibling);
        
        // Setup toggle
        const toggleBtn = summarySection.querySelector('.btn-toggle-summary');
        const content = summarySection.querySelector('.summary-content');
        
        toggleBtn.addEventListener('click', () => {
            const isVisible = content.style.display !== 'none';
            content.style.display = isVisible ? 'none' : 'block';
            toggleBtn.innerHTML = isVisible ? 
                '<i class="fas fa-eye"></i> Show Summary' : 
                '<i class="fas fa-eye-slash"></i> Hide Summary';
            
            if (!isVisible) {
                this.updateWeeklySummary();
            }
        });
    }

    updateWeeklySummary() {
        const summaryDays = document.querySelectorAll('.summary-day');
        let openDays = 0;
        let totalMinutes = 0;
        
        summaryDays.forEach((summaryDay, index) => {
            const day = this.days[index];
            const dayData = this.getDayData(day);
            const hoursDiv = summaryDay.querySelector('.summary-day-hours');
            
            let displayText = 'Closed';
            let dayMinutes = 0;
            
            if (dayData.status === '24hours') {
                displayText = '24 Hours';
                dayMinutes = 24 * 60;
                openDays++;
            } else if (dayData.status === 'regular' || dayData.status === 'overnight') {
                if (dayData.opening && dayData.closing) {
                    const openTime = this.timeSlots.find(s => s.value === dayData.opening);
                    const closeTime = this.timeSlots.find(s => s.value === dayData.closing);
                    
                    if (openTime && closeTime) {
                        displayText = `${openTime.display.replace(' ', '')} - ${closeTime.display.replace(' ', '')}`;
                        dayMinutes = this.calculateMinutesBetween(dayData.opening, dayData.closing, dayData.status === 'overnight');
                        openDays++;
                    }
                }
            }
            
            hoursDiv.textContent = displayText;
            totalMinutes += dayMinutes;
        });
        
        // Update stats
        document.getElementById('open-days-count').textContent = openDays;
        document.getElementById('total-hours-week').textContent = Math.round(totalMinutes / 60 * 10) / 10;
    }

    calculateMinutesBetween(startTime, endTime, isOvernight) {
        const [startHour, startMin] = startTime.split(':').map(Number);
        const [endHour, endMin] = endTime.split(':').map(Number);
        
        let startMinutes = startHour * 60 + startMin;
        let endMinutes = endHour * 60 + endMin;
        
        if (isOvernight && endMinutes <= startMinutes) {
            endMinutes += 24 * 60; // Add 24 hours for overnight
        }
        
        return Math.max(0, endMinutes - startMinutes);
    }

    setupDayStatusHandlers() {
        const statusSelects = document.querySelectorAll('.day-status-select');
        
        statusSelects.forEach(select => {
            const dayCard = select.closest('.day-card');
            const timeInputs = dayCard.querySelectorAll('.time-select');
            
            const updateDayCard = () => {
                const status = select.value;
                
                // Remove all status classes
                dayCard.classList.remove('status-closed', 'status-regular', 'status-24hours', 'status-overnight');
                
                // Add current status class
                dayCard.classList.add(`status-${status}`);
                
                // Enable/disable time inputs
                const needsTimes = ['regular', 'overnight'].includes(status);
                timeInputs.forEach(input => {
                    input.disabled = !needsTimes;
                    if (!needsTimes) {
                        input.value = '';
                        this.updateTimePickerDisplay(input);
                    }
                });
                
                // Update summary if visible
                setTimeout(() => this.updateWeeklySummary(), 100);
            };
            
            // Initial state
            updateDayCard();
            
            // Handle changes
            select.addEventListener('change', updateDayCard);
        });
    }

    getDayName(day) {
        const index = this.days.indexOf(day);
        return this.dayNames[index] || day;
    }

    formatDayStatus(dayData) {
        switch (dayData.status) {
            case 'closed': return 'Closed';
            case '24hours': return '24 Hours';
            case 'regular':
            case 'overnight':
                if (dayData.opening && dayData.closing) {
                    const openSlot = this.timeSlots.find(s => s.value === dayData.opening);
                    const closeSlot = this.timeSlots.find(s => s.value === dayData.closing);
                    if (openSlot && closeSlot) {
                        return `${openSlot.display} - ${closeSlot.display}`;
                    }
                }
                return 'Hours not set';
            default:
                return 'Unknown';
        }
    }

    showMessage(text, type = 'info') {
        const message = document.createElement('div');
        message.className = `hours-message message-${type}`;
        message.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
            ${text}
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => message.remove(), 300);
        }, 3000);
    }

    initializeDefaultView() {
        // Show custom schedule by default if no template is pre-selected
        const hasSelectedTemplate = document.querySelector('.template-card.selected');
        const customScheduleSection = document.querySelector('.custom-schedule-section');
        
        if (!hasSelectedTemplate) {
            customScheduleSection.classList.add('active');
            // Select custom template by default
            const customCard = document.querySelector('.template-card[data-template="custom"]');
            if (customCard) {
                customCard.classList.add('selected');
                document.getElementById('selected-template').value = 'custom';
            }
        }
        
        // Close time pickers when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.visual-time-picker')) {
                this.closeAllTimePickers();
            }
        });
    }

    applyTemplate(templateType) {
        // Template definitions would go here
        // This is a simplified version - you would expand based on your template system
        const templates = {
            'business_hours': {
                monday: { status: 'regular', opening: '09:00', closing: '17:00' },
                tuesday: { status: 'regular', opening: '09:00', closing: '17:00' },
                wednesday: { status: 'regular', opening: '09:00', closing: '17:00' },
                thursday: { status: 'regular', opening: '09:00', closing: '17:00' },
                friday: { status: 'regular', opening: '09:00', closing: '17:00' },
                saturday: { status: 'closed' },
                sunday: { status: 'closed' }
            }
            // Add other templates as needed
        };
        
        const template = templates[templateType];
        if (template) {
            Object.keys(template).forEach(day => {
                this.setDayData(day, template[day]);
            });
            this.updateWeeklySummary();
        }
    }
}

// Close existing script and add CSS
</script>

<style>
/* Visual Time Picker Styles */
.visual-time-picker {
    position: relative;
    display: inline-block;
    width: 100%;
}

.time-display {
    display: flex;
    align-items: center;
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    background: white;
    overflow: hidden;
}

.time-input {
    flex: 1;
    border: none;
    padding: 0.75rem;
    font-family: var(--cw-font-primary);
    background: transparent;
    cursor: pointer;
}

.time-picker-btn {
    border: none;
    background: var(--cw-brand-accent);
    color: var(--cw-brand-primary);
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.time-picker-btn:hover {
    background: var(--cw-brand-primary);
    color: white;
}

.time-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    box-shadow: var(--cw-shadow-lg);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.time-dropdown.show {
    display: block;
}

.time-dropdown-header {
    padding: 1rem;
    background: var(--cw-accent-light);
    border-bottom: 1px solid var(--cw-brand-accent);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.close-time-picker {
    border: none;
    background: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--cw-neutral-600);
}

.time-slots {
    max-height: 200px;
    overflow-y: auto;
}

.time-slot {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--cw-neutral-200);
    transition: all 0.2s ease;
}

.time-slot:hover {
    background: var(--cw-accent-light);
    color: var(--cw-brand-primary);
}

.time-slot:last-child {
    border-bottom: none;
}

/* Copy Hours Controls */
.copy-hours-controls {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--cw-neutral-300);
}

.copy-section {
    text-align: center;
}

.copy-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-copy-from,
.btn-copy-to {
    border: 1px solid var(--cw-brand-accent);
    background: white;
    color: var(--cw-brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-copy-from:hover,
.btn-copy-to:hover {
    background: var(--cw-brand-primary);
    color: white;
}

/* Modal Styles */
.hours-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.hours-modal {
    background: white;
    border-radius: 1rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--cw-shadow-xl);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--cw-neutral-300);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    color: var(--cw-brand-primary);
    font-family: var(--cw-font-heading);
}

.modal-close {
    border: none;
    background: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--cw-neutral-600);
}

.modal-body {
    padding: 1.5rem;
}

.day-copy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--cw-neutral-300);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.day-copy-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.day-status {
    font-size: 0.875rem;
    color: var(--cw-neutral-600);
}

.btn-select-day {
    border: 1px solid var(--cw-brand-primary);
    background: white;
    color: var(--cw-brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
}

.btn-select-day:hover {
    background: var(--cw-brand-primary);
    color: white;
}

.day-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
}

.day-checkbox input[type="checkbox"] {
    margin: 0;
}

.btn-copy-selected {
    width: 100%;
    margin-top: 1rem;
}

/* Weekly Summary Styles */
.weekly-summary-section {
    background: var(--cw-accent-light);
    border: 2px solid var(--cw-brand-accent);
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.summary-header h3 {
    font-family: var(--cw-font-heading);
    color: var(--cw-brand-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.btn-toggle-summary {
    border: 1px solid var(--cw-brand-primary);
    background: white;
    color: var(--cw-brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-toggle-summary:hover {
    background: var(--cw-brand-primary);
    color: white;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-day {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--cw-brand-accent);
    text-align: center;
}

.summary-day-name {
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
}

.summary-day-hours {
    color: var(--cw-neutral-700);
    font-size: 0.875rem;
}

.summary-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--cw-brand-accent);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--cw-neutral-600);
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
}

/* Day Status Classes */
.day-card.status-closed {
    opacity: 0.7;
    background: var(--cw-neutral-100);
}

.day-card.status-24hours {
    border-color: var(--cw-success);
    background: rgba(34, 197, 94, 0.1);
}

.day-card.status-overnight {
    border-color: var(--cw-warning);
    background: rgba(251, 191, 36, 0.1);
}

/* Message Styles */
.hours-message {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: var(--cw-shadow-lg);
    z-index: 3000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.hours-message.show {
    transform: translateX(0);
}

.message-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgb(34, 197, 94);
    color: rgb(21, 128, 61);
}

.message-info {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgb(59, 130, 246);
    color: rgb(30, 58, 138);
}
</style>
{% endblock %}
