{% extends 'venues_app/base_venues.html' %}
{% load widget_tweaks i18n %}

{% block title %}Delete Amenity - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block venues_content %}
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-cw-secondary me-3">
                        <i class="fas fa-arrow-left me-2"></i>Back
                    </a>
                    <div>
                        <h1 class="display-font text-brand-cw mb-2">Delete Amenity</h1>
                        <p class="lead text-neutral-cw mb-0">Confirm deletion of amenity for {{ venue.venue_name }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card-cw border-danger">
                <div class="card-header bg-danger text-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                    </h5>
                </div>
                <div class="card-body p-4 text-center">
                    <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center mx-auto mb-4" style="width: 80px; height: 80px;">
                        <i class="fas fa-exclamation-triangle text-white fa-2x"></i>
                    </div>

                    <h4 class="text-brand-cw mb-3">Are you sure you want to delete this amenity?</h4>
                    <p class="text-neutral-cw mb-4">This action cannot be undone. The amenity will be permanently removed from your venue.</p>

                    <!-- Amenity Preview -->
                    <div class="bg-light-cw rounded-cw p-4 mb-4 text-start">
                        <div class="d-flex align-items-start">
                            <div class="bg-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-star text-brand-cw"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="text-brand-cw mb-2">
                                    {% if amenity.custom_name %}
                                        {{ amenity.custom_name }}
                                    {% else %}
                                        {{ amenity.get_amenity_type_display }}
                                    {% endif %}
                                </h5>
                                
                                <div class="d-flex align-items-center">
                                    {% if amenity.is_active %}
                                        <span class="badge bg-success me-2">
                                            <i class="fas fa-check me-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary me-2">
                                            <i class="fas fa-pause me-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                    <small class="text-neutral-cw">
                                        <i class="fas fa-calendar me-1"></i>Created {{ amenity.created_at|date:"M d, Y" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post">
                        {% csrf_token %}

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                            <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Yes, Delete Amenity
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
