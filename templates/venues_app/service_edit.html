{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Edit Service - {{ object.service_title }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
{% load widget_tweaks %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #2F160F;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    /* Form section description text */
    .form-section p,
    .form-section .text-muted,
    .form-section small {
        color: #525252;
        font-family: 'Inter', sans-serif;
        font-size: 1.125rem;
        line-height: 1.6;
    }

    .form-section small {
        font-size: 0.875rem;
    }

    /* Form labels */
    .form-label {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Character count */
    .character-count {
        font-size: 0.875rem;
        color: #525252;
        font-family: 'Inter', sans-serif;
        text-align: right;
        margin-top: 0.25rem;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }

    /* Button styling */
    .btn-cw-primary {
        background: white;
        border: 2px solid #2F160F;
        color: #2F160F;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: block;
        margin: 2rem auto 0;
        width: fit-content;
    }

    .btn-cw-primary:hover {
        background: #2F160F;
        border-color: #2F160F;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    /* Checkbox styling */
    .form-check {
        margin-bottom: 0.75rem;
    }

    .form-check-input {
        margin-right: 0.5rem;
    }

    .form-check-label {
        font-weight: 500;
        color: #525252;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

<!-- Edit Form -->
<form method="post" novalidate>
    {% csrf_token %}

    <!-- Basic Details Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-spa me-2"></i>Service Details
        </h4>

        <div class="mb-3">
            <label for="{{ form.service_title.id_for_label }}" class="form-label fw-bold">
                Service Name <span class="text-danger">*</span>
            </label>
            {{ form.service_title }}
            {% if form.service_title.errors %}
                {% for error in form.service_title.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="character-count">
                <span id="service-title-count">0</span>/255 characters
            </div>
        </div>

        <div class="mb-3">
            <label for="{{ form.custom_slug.id_for_label }}" class="form-label fw-bold">
                {{ form.custom_slug.label }}
            </label>
            {{ form.custom_slug }}
            {% if form.custom_slug.errors %}
                {% for error in form.custom_slug.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
        </div>

        <div class="mb-3">
            <label for="{{ form.service_category.id_for_label }}" class="form-label fw-bold">
                Service Category <span class="text-danger">*</span>
            </label>
            {{ form.service_category }}
            {% if form.service_category.errors %}
                {% for error in form.service_category.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
        </div>

        <div class="mb-3">
            <label for="{{ form.short_description.id_for_label }}" class="form-label fw-bold">
                Service Description <span class="text-danger">*</span>
            </label>
            {{ form.short_description }}
            {% if form.short_description.errors %}
                {% for error in form.short_description.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="character-count">
                <span id="description-count">0</span>/500 characters
            </div>
        </div>
    </div>

    <!-- Pricing Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-dollar-sign me-2"></i>Pricing & Duration
        </h4>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.price_min.id_for_label }}" class="form-label fw-bold">
                    Minimum Price ($) <span class="text-danger">*</span>
                </label>
                {{ form.price_min }}
                {% if form.price_min.errors %}
                    {% for error in form.price_min.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="col-md-6 mb-3">
                <label for="{{ form.price_max.id_for_label }}" class="form-label fw-bold">
                    Maximum Price ($)
                </label>
                {{ form.price_max }}
                {% if form.price_max.errors %}
                    {% for error in form.price_max.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <div class="mb-3">
            <label for="{{ form.duration_minutes.id_for_label }}" class="form-label fw-bold">
                Duration (minutes) <span class="text-danger">*</span>
            </label>
            {{ form.duration_minutes }}
            {% if form.duration_minutes.errors %}
                {% for error in form.duration_minutes.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
        </div>
    </div>

    <!-- Availability Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-calendar-check me-2"></i>Availability
        </h4>

        <div class="form-check mb-3">
            {{ form.is_active }}
            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                Available for Booking
            </label>
            {% if form.is_active.errors %}
                {% for error in form.is_active.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
        </div>
    </div>

    <!-- Form Errors -->
    {% if form.non_field_errors %}
        <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
                {{ error }}
            {% endfor %}
        </div>
    {% endif %}

    <!-- Form Actions -->
    <div class="text-center">
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-save me-2"></i>Update
        </button>
    </div>
</form>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counting
    function updateCharacterCount(input, countElement, maxLength) {
        const count = input.value.length;
        countElement.textContent = count;

        if (count > maxLength * 0.9) {
            countElement.style.color = '#dc3545';
        } else if (count > maxLength * 0.7) {
            countElement.style.color = '#ffc107';
        } else {
            countElement.style.color = '#6c757d';
        }
    }

    // Service title character count
    const serviceTitleInput = document.getElementById('{{ form.service_title.id_for_label }}');
    const serviceTitleCount = document.getElementById('service-title-count');
    if (serviceTitleInput && serviceTitleCount) {
        updateCharacterCount(serviceTitleInput, serviceTitleCount, 255);
        serviceTitleInput.addEventListener('input', function() {
            updateCharacterCount(this, serviceTitleCount, 255);
        });
    }

    // Description character count
    const descriptionInput = document.getElementById('{{ form.short_description.id_for_label }}');
    const descriptionCount = document.getElementById('description-count');
    if (descriptionInput && descriptionCount) {
        updateCharacterCount(descriptionInput, descriptionCount, 500);
        descriptionInput.addEventListener('input', function() {
            updateCharacterCount(this, descriptionCount, 500);
        });
    }

    // Slug preview functionality
    const customSlugInput = document.getElementById('{{ form.custom_slug.id_for_label }}');
    const slugPreview = document.getElementById('slug-preview');

    function slugify(text) {
        return text
            .toString()
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9 -]/g, '')    // Remove invalid chars
            .replace(/\s+/g, '-')           // Replace spaces with -
            .replace(/-+/g, '-')            // Replace multiple - with single -
            .replace(/^-+/, '')             // Remove leading -
            .replace(/-+$/, '');            // Remove trailing -
    }

    function updateSlugPreview() {
        let previewSlug = '';

        if (customSlugInput && customSlugInput.value.trim()) {
            previewSlug = customSlugInput.value.trim();
        } else if (serviceTitleInput && serviceTitleInput.value.trim()) {
            previewSlug = slugify(serviceTitleInput.value);
        } else {
            previewSlug = '{{ service.slug }}';
        }

        if (slugPreview) {
            slugPreview.textContent = previewSlug;
        }
    }

    // Update slug preview on input
    if (serviceTitleInput) {
        serviceTitleInput.addEventListener('input', updateSlugPreview);
    }
    if (customSlugInput) {
        customSlugInput.addEventListener('input', updateSlugPreview);
    }

    // Custom slug validation
    if (customSlugInput) {
        customSlugInput.addEventListener('input', function() {
            let value = this.value;
            // Auto-convert to lowercase and remove invalid chars
            value = value.toLowerCase().replace(/[^a-z0-9-]/g, '');
            if (value !== this.value) {
                this.value = value;
            }
        });
    }

    // Initialize slug preview
    updateSlugPreview();
});
</script>
{% endblock %}
