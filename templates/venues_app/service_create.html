{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Create Service - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #000000;
        font-size: 1.25rem;
    }

    .form-label {
        font-weight: 500;
        color: #525252;
        font-family: 'Inter', sans-serif;
        margin-bottom: 0.5rem;
    }

    .step-circle {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        border: 2px solid var(--cw-brand-accent);
        background: white;
        color: var(--cw-neutral-600);
    }

    .step-circle.active {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .step-circle.completed {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--cw-neutral-600);
        max-width: 120px;
    }

    .step-title.active {
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .step-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        min-height: 400px;
    }

    .step-section {
        display: none;
    }

    .step-section.active {
        display: block;
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-number {
        display: inline-block;
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 2rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .step-heading {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: var(--cw-neutral-600);
        font-size: 1rem;
    }

    .field-group {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .field-group-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .field-group-icon {
        background: var(--cw-brand-primary);
        color: white;
        width: 2rem;
        height: 2rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .form-control-cw {
        border-radius: 0.5rem;
        border: 2px solid var(--cw-brand-accent);
        padding: 0.75rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-valid {
        border-color: #28a745;
    }

    .form-control-cw.is-invalid {
        border-color: #dc3545;
    }

    .form-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-text {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        display: block !important;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .valid-feedback {
        display: block;
        color: #28a745;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--cw-brand-accent);
    }

    .btn-nav {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: none;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-prev {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-accent);
    }

    .btn-prev:hover {
        background: var(--cw-brand-accent);
    }

    .btn-next, .btn-submit {
        background: var(--cw-gradient-brand-button);
        color: white;
    }

    .btn-next:hover, .btn-submit:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-next:disabled, .btn-submit:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .required-field {
        color: #dc3545;
    }

    .price-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .validation-message {
        padding: 0.75rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
    }

    .validation-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .form-check-label {
        font-weight: 500;
        color: #525252;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
    }

    /* Button styling */
    .btn-cw-primary {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        border: none;
        color: white;
        padding: 0.75rem 3rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: block;
        margin: 2rem auto 0;
        width: fit-content;
    }

    .btn-cw-primary:hover {
        background: linear-gradient(135deg, #4a2a1f 0%, #2F160F 100%);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.2);
    }

    /* Form inputs */
    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: 'Inter', sans-serif;
        color: #2F160F;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        background: white;
    }

    /* Character count */
    .character-count {
        font-size: 0.875rem;
        color: #525252;
        font-family: 'Inter', sans-serif;
        text-align: right;
        margin-top: 0.25rem;
    }

    /* Error messages */
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        margin-top: 0.25rem;
    }

    /* Slug preview */
    .slug-preview {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
        font-family: monospace;
        font-size: 0.875rem;
        color: #525252;
    }

    /* Price range styling */
    .price-range-container {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .price-range-separator {
        color: #525252;
        font-weight: 500;
        font-family: 'Inter', sans-serif;
    }

    .status-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .status-options {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .status-option {
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 1rem;
        transition: all 0.3s ease;
        flex: 1;
        min-width: 250px;
    }

    .status-option:hover {
        background: rgba(255, 255, 255, 0.9);
        border-color: #2F160F;
    }

    .form-check-input:checked ~ .status-option {
        background: #fef7f0;
        border-color: #2F160F;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

<!-- Create Service Form -->
<form method="post" enctype="multipart/form-data" novalidate>
    {% csrf_token %}

    <!-- Basic Information Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-info-circle me-2"></i>Basic Information
        </h4>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.service_title.id_for_label }}" class="form-label fw-bold">
                    Service Title <span class="text-danger">*</span>
                </label>
                {{ form.service_title }}
                {% if form.service_title.errors %}
                    {% for error in form.service_title.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="character-count">
                    <span id="service-title-count">0</span>/255 characters
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <label for="{{ form.custom_slug.id_for_label }}" class="form-label fw-bold">
                    Custom URL Slug
                </label>
                {{ form.custom_slug }}
                {% if form.custom_slug.errors %}
                    {% for error in form.custom_slug.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="slug-preview" id="slugPreview" style="display: none;">
                    URL: /services/<span id="slugText"></span>/
                </div>
                <small class="form-text text-muted">
                    Leave blank to auto-generate from service title
                </small>
            </div>
        </div>

        <div class="mb-3">
            <label for="{{ form.short_description.id_for_label }}" class="form-label fw-bold">
                Service Description <span class="text-danger">*</span>
            </label>
            {{ form.short_description }}
            {% if form.short_description.errors %}
                {% for error in form.short_description.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="character-count">
                <span id="description-count">0</span>/500 characters
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 mb-3">
                <label for="{{ form.service_category.id_for_label }}" class="form-label fw-bold">
                    Service Category <span class="text-danger">*</span>
                </label>
                {{ form.service_category }}
                {% if form.service_category.errors %}
                    {% for error in form.service_category.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Pricing & Duration Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-dollar-sign me-2"></i>Pricing & Duration
        </h4>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="{{ form.price_min.id_for_label }}" class="form-label fw-bold">
                    Minimum Price ($) <span class="text-danger">*</span>
                </label>
                {{ form.price_min }}
                {% if form.price_min.errors %}
                    {% for error in form.price_min.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Starting price for this service
                </small>
            </div>

            <div class="col-md-4 mb-3">
                <label for="{{ form.price_max.id_for_label }}" class="form-label fw-bold">
                    Maximum Price ($)
                </label>
                {{ form.price_max }}
                {% if form.price_max.errors %}
                    {% for error in form.price_max.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Leave blank for fixed pricing
                </small>
            </div>

            <div class="col-md-4 mb-3">
                <label for="{{ form.duration_minutes.id_for_label }}" class="form-label fw-bold">
                    Duration (minutes) <span class="text-danger">*</span>
                </label>
                {{ form.duration_minutes }}
                {% if form.duration_minutes.errors %}
                    {% for error in form.duration_minutes.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Typical duration for this service
                </small>
            </div>
        </div>
    </div>

    <!-- Service Status Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-toggle-on me-2"></i>Service Status
        </h4>

        <div class="status-section">
            <div class="status-options">
                <div class="status-option">
                    <div class="form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            <strong>Service Active</strong>
                        </label>
                    </div>
                    <small class="form-text text-muted">
                        Whether customers can currently book this service
                    </small>
                </div>

                <div class="status-option">
                    <div class="form-check">
                        {{ form.is_featured }}
                        <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                            <strong>Featured Service</strong>
                        </label>
                    </div>
                    <small class="form-text text-muted">
                        Highlight this service as a featured offering
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Errors -->
    {% if form.non_field_errors %}
        <div class="form-section">
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                    <div>{{ error }}</div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- Form Actions -->
    <div class="text-center">
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-plus me-2"></i>Create Service
        </button>
    </div>
</form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counting
    function updateCharacterCount(input, countElement, maxLength) {
        const count = input.value.length;
        countElement.textContent = count;

        if (count > maxLength * 0.9) {
            countElement.style.color = '#dc3545';
        } else if (count > maxLength * 0.7) {
            countElement.style.color = '#ffc107';
        } else {
            countElement.style.color = '#6c757d';
        }
    }

    // Service title character count
    const serviceTitleInput = document.getElementById('{{ form.service_title.id_for_label }}');
    const serviceTitleCount = document.getElementById('service-title-count');
    if (serviceTitleInput && serviceTitleCount) {
        updateCharacterCount(serviceTitleInput, serviceTitleCount, 255);
        serviceTitleInput.addEventListener('input', function() {
            updateCharacterCount(this, serviceTitleCount, 255);
        });
    }

    // Description character count
    const descriptionInput = document.getElementById('{{ form.short_description.id_for_label }}');
    const descriptionCount = document.getElementById('description-count');
    if (descriptionInput && descriptionCount) {
        updateCharacterCount(descriptionInput, descriptionCount, 500);
        descriptionInput.addEventListener('input', function() {
            updateCharacterCount(this, descriptionCount, 500);
        });
    }

    // Slug preview
    const slugInput = document.getElementById('{{ form.custom_slug.id_for_label }}');
    const slugPreview = document.getElementById('slugPreview');
    const slugText = document.getElementById('slugText');

    if (slugInput && slugPreview && slugText) {
        function updateSlugPreview() {
            const slugValue = slugInput.value.trim();
            if (slugValue) {
                slugText.textContent = slugValue;
                slugPreview.style.display = 'block';
            } else {
                slugPreview.style.display = 'none';
            }
        }

        slugInput.addEventListener('input', updateSlugPreview);
        updateSlugPreview(); // Initial call
    }

    // Auto-generate slug from service title
    if (serviceTitleInput && slugInput) {
        serviceTitleInput.addEventListener('input', function() {
            if (!slugInput.value.trim()) {
                const slug = this.value.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .substring(0, 50);
                slugInput.value = slug;
                if (slugPreview && slugText) {
                    if (slug) {
                        slugText.textContent = slug;
                        slugPreview.style.display = 'block';
                    } else {
                        slugPreview.style.display = 'none';
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}
