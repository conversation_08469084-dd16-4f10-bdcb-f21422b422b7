{% extends 'accounts/base_auth.html' %}
{% load socialaccount %}

{% block title %}Complete Social Sign Up - CozyWish{% endblock %}

{% block auth_card_class %}auth-card-wide{% endblock %}

{% block auth_header %}
    {% include 'accounts/components/auth_header.html' with icon='fas fa-user-check' title='Complete Your Account' subtitle='Finish setting up your CozyWish account' %}
{% endblock %}

{% block auth_extra_css %}
<style>
    /* Social Account Info */
    .social-account-info {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .social-account-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid var(--cw-brand-primary);
        object-fit: cover;
    }

    .social-account-details h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .social-account-details p {
        color: var(--cw-neutral-700);
        margin-bottom: 0;
        font-size: 0.9rem;
    }
    /* Role selection styling */
    .role-selection {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .role-selection h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block auth_content %}
    <!-- Form Errors -->
    {% include 'accounts/components/form_errors.html' with form=form %}

    <!-- Social Account Information -->
    {% if sociallogin %}
        <div class="social-account-info">
            {% if sociallogin.account.get_avatar_url %}
                <img src="{{ sociallogin.account.get_avatar_url }}"
                     alt="Profile Picture"
                     class="social-account-avatar">
            {% else %}
                <div class="social-account-avatar d-flex align-items-center justify-content-center"
                     style="background: var(--cw-brand-accent); color: var(--cw-brand-primary);">
                    <i class="fas fa-user"></i>
                </div>
            {% endif %}
            <div class="social-account-details">
                <h6>{{ sociallogin.account.provider|title }} Account</h6>
                <p>{{ sociallogin.account.extra_data.name|default:sociallogin.account.extra_data.email }}</p>
            </div>
        </div>
    {% endif %}

    <!-- Social Signup Form -->
    <form method="post" action="{% url 'socialaccount_signup' %}" novalidate>
        {% csrf_token %}

        <!-- Role Selection (if available) -->
        {% if form.role %}
            <div class="role-selection">
                <h6>{{ form.role.label }}</h6>
                {% for choice in form.role %}
                    <div class="form-check mb-2">
                        {{ choice.tag }}
                        <label class="form-check-label" for="{{ choice.id_for_label }}">
                            {{ choice.choice_label }}
                        </label>
                    </div>
                {% endfor %}
                {% if form.role.help_text %}
                    <div class="form-text">{{ form.role.help_text }}</div>
                {% endif %}
            </div>
        {% endif %}

        <!-- Email Field (if not from social) -->
        {% if form.email %}
            {% include 'accounts/components/form_field.html' with field=form.email field_type='email' placeholder='Enter your email address' autocomplete='email' %}
        {% endif %}

        <!-- Additional form fields (if any) -->
        {% for field in form %}
            {% if field.name not in "email,role" %}
                {% include 'accounts/components/form_field.html' with field=field %}
            {% endif %}
        {% endfor %}

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary mb-3">
            <i class="fas fa-check me-2"></i>
            Complete Sign Up
        </button>

        <!-- Cancel Link -->
        <a href="{% url 'account_login' %}" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>
            Cancel
        </a>
    </form>

    <!-- Privacy Notice -->
    <div class="mt-4">
        <p class="text-muted text-center">
            <small>
                <i class="fas fa-shield-alt me-1"></i>
                By completing sign up, you agree to our
                <a href="#" class="text-muted">Terms of Service</a> and
                <a href="#" class="text-muted">Privacy Policy</a>.
            </small>
        </p>
    </div>

    <!-- Footer -->
    {% include 'accounts/components/auth_footer.html' with footer_style='minimal' %}
{% endblock %}
