{% extends 'base.html' %}
{% load socialaccount %}

{% block title %}Social Account Connections - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Social Connections */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-200: #e5e5e5;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Connections Section */
    .connections-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .connections-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .connections-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .connections-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .connections-icon {
        width: 80px;
        height: 80px;
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .connections-icon i {
        color: var(--cw-brand-primary);
        font-size: 2rem;
    }

    .connections-title {
        color: var(--cw-brand-primary);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
        font-family: var(--cw-font-heading);
    }

    .connections-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 0;
        line-height: 1.6;
    }

    /* Content Section */
    .connections-content {
        padding: 3rem;
    }

    /* Social Account Item */
    .social-account-item {
        background: white;
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
    }

    .social-account-item:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
    }

    .social-account-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
    }

    .social-account-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .social-account-icon.google {
        background: #db4437;
    }

    .social-account-icon.facebook {
        background: #4267B2;
    }

    .social-account-icon.twitter {
        background: #1DA1F2;
    }

    .social-account-icon.github {
        background: #333;
    }

    .social-account-details h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .social-account-details p {
        color: var(--cw-neutral-700);
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .social-account-status {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-badge.connected {
        background: #f0fff4;
        color: #28a745;
        border: 1px solid #28a745;
    }

    .status-badge.available {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-brand-accent);
    }

    /* Button styling */
    .btn-primary {
        background: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
    }

    .btn-primary:hover {
        background: var(--cw-brand-light);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .btn-danger {
        background: #dc3545;
        border: 2px solid #dc3545;
        color: white;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
    }

    .btn-danger:hover {
        background: #c82333;
        border-color: #c82333;
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .btn-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        width: 100%;
    }

    .btn-secondary:hover {
        background: var(--cw-accent-light);
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    /* Alert styling */
    .alert {
        border: 2px solid;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-success {
        background-color: #f0fff4;
        color: #28a745;
        border-color: #28a745;
    }

    .alert-info {
        background-color: #f0f9ff;
        color: #007bff;
        border-color: #007bff;
    }

    .alert-warning {
        background-color: #fffbf0;
        color: #f59e0b;
        border-color: #f59e0b;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Empty state */
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--cw-neutral-600);
    }

    .empty-state i {
        font-size: 3rem;
        color: var(--cw-brand-accent);
        margin-bottom: 1rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .connections-header {
            padding: 2rem 2rem 1.5rem;
        }

        .connections-content {
            padding: 2rem;
        }

        .connections-title {
            font-size: 2rem;
        }

        .social-account-item {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .social-account-info {
            flex-direction: column;
            text-align: center;
        }

        .social-account-status {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="connections-section">
    <div class="connections-container">
        <div class="connections-card">
            <div class="connections-header">
                <div class="connections-icon">
                    <i class="fas fa-link"></i>
                </div>
                <h1 class="connections-title">Social Connections</h1>
                <p class="connections-subtitle">Manage your connected social accounts</p>
            </div>

            <div class="connections-content">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Connected Accounts -->
                {% if socialaccount_list %}
                    <h5 class="mb-3">
                        <i class="fas fa-check-circle me-2 text-success"></i>
                        Connected Accounts
                    </h5>
                    
                    {% for socialaccount in socialaccount_list %}
                        <div class="social-account-item">
                            <div class="social-account-info">
                                <div class="social-account-icon {{ socialaccount.provider }}">
                                    {% if socialaccount.provider == "google" %}
                                        <i class="fab fa-google"></i>
                                    {% elif socialaccount.provider == "facebook" %}
                                        <i class="fab fa-facebook-f"></i>
                                    {% elif socialaccount.provider == "twitter" %}
                                        <i class="fab fa-twitter"></i>
                                    {% elif socialaccount.provider == "github" %}
                                        <i class="fab fa-github"></i>
                                    {% else %}
                                        <i class="fas fa-user"></i>
                                    {% endif %}
                                </div>
                                <div class="social-account-details">
                                    <h6>{{ socialaccount.provider|title }}</h6>
                                    <p>{{ socialaccount.extra_data.name|default:socialaccount.extra_data.email|default:"Connected Account" }}</p>
                                </div>
                            </div>
                            <div class="social-account-status">
                                <span class="status-badge connected">
                                    <i class="fas fa-check me-1"></i>
                                    Connected
                                </span>
                                {% if socialaccount_list|length > 1 or user.has_usable_password %}
                                    <form method="post" action="{% url 'socialaccount_connections' %}" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="account" value="{{ socialaccount.id }}">
                                        <button type="submit" class="btn btn-danger btn-sm" 
                                                onclick="return confirm('Are you sure you want to disconnect this account?')">
                                            <i class="fas fa-unlink me-1"></i>
                                            Disconnect
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Available Providers -->
                {% if providers %}
                    <h5 class="mb-3 mt-4">
                        <i class="fas fa-plus-circle me-2"></i>
                        Add New Connection
                    </h5>
                    
                    {% for provider in providers %}
                        {% if provider.id not in connected_providers %}
                            <div class="social-account-item">
                                <div class="social-account-info">
                                    <div class="social-account-icon {{ provider.id }}">
                                        {% if provider.id == "google" %}
                                            <i class="fab fa-google"></i>
                                        {% elif provider.id == "facebook" %}
                                            <i class="fab fa-facebook-f"></i>
                                        {% elif provider.id == "twitter" %}
                                            <i class="fab fa-twitter"></i>
                                        {% elif provider.id == "github" %}
                                            <i class="fab fa-github"></i>
                                        {% else %}
                                            <i class="fas fa-user"></i>
                                        {% endif %}
                                    </div>
                                    <div class="social-account-details">
                                        <h6>{{ provider.name }}</h6>
                                        <p>Connect your {{ provider.name }} account for easier sign in</p>
                                    </div>
                                </div>
                                <div class="social-account-status">
                                    <span class="status-badge available">Available</span>
                                    <a href="{% provider_login_url provider.id process='connect' %}" 
                                       class="btn btn-primary">
                                        <i class="fas fa-link me-1"></i>
                                        Connect
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}

                <!-- Empty State -->
                {% if not socialaccount_list and not providers %}
                    <div class="empty-state">
                        <i class="fas fa-unlink"></i>
                        <h5>No Social Connections Available</h5>
                        <p>Social authentication providers are not currently configured.</p>
                    </div>
                {% endif %}

                <!-- Security Notice -->
                {% if socialaccount_list %}
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Security Notice:</strong> 
                        {% if not user.has_usable_password %}
                            You can only disconnect social accounts if you have set a password or have multiple accounts connected.
                            <a href="{% url 'account_set_password' %}" class="alert-link">Set a password</a> to manage your connections freely.
                        {% else %}
                            You can safely disconnect social accounts since you have a password set for your account.
                        {% endif %}
                    </div>
                {% endif %}

                <!-- Back to Profile -->
                <div class="mt-4">
                    <a href="{% url 'dashboard_app:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
