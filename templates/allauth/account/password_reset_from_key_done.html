{% extends 'base.html' %}

{% block title %}Password Changed - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Password Reset Complete */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-200: #e5e5e5;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Success Section */
    .success-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .success-container {
        max-width: 600px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .success-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .success-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .success-icon {
        width: 80px;
        height: 80px;
        background: white;
        border: 2px solid #28a745;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .success-icon i {
        color: #28a745;
        font-size: 2rem;
    }

    .success-title {
        color: var(--cw-brand-primary);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
        font-family: var(--cw-font-heading);
    }

    .success-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 0;
        line-height: 1.6;
    }

    /* Content Section */
    .success-content {
        padding: 3rem;
        text-align: center;
    }

    .success-message {
        color: var(--cw-neutral-700);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    /* Button styling */
    .btn-primary {
        background: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem;
    }

    .btn-primary:hover {
        background: var(--cw-brand-light);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .btn-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem;
    }

    .btn-secondary:hover {
        background: var(--cw-accent-light);
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    /* Alert styling */
    .alert {
        border: 2px solid;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-success {
        background-color: #f0fff4;
        color: #28a745;
        border-color: #28a745;
    }

    .alert-info {
        background-color: #f0f9ff;
        color: #007bff;
        border-color: #007bff;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Security tips styling */
    .security-tips {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }

    .security-tips h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .security-tips ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    .security-tips li {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .success-header {
            padding: 2rem 2rem 1.5rem;
        }

        .success-content {
            padding: 2rem;
        }

        .success-title {
            font-size: 2rem;
        }

        .btn-primary,
        .btn-secondary {
            width: 100%;
            margin: 0.5rem 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="success-section">
    <div class="success-container">
        <div class="success-card">
            <div class="success-header">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1 class="success-title">Password Changed!</h1>
                <p class="success-subtitle">Your password has been successfully updated</p>
            </div>

            <div class="success-content">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="alert alert-success">
                    <i class="fas fa-shield-alt me-2"></i>
                    Your password has been changed successfully. You can now sign in with your new password.
                </div>

                <div class="success-message">
                    For your security, you have been logged out of all devices. Please sign in again with your new password.
                </div>

                <!-- Security Tips -->
                <div class="security-tips">
                    <h6><i class="fas fa-lightbulb me-2"></i>Security Tips</h6>
                    <ul>
                        <li>Keep your password private and secure</li>
                        <li>Don't share your password with anyone</li>
                        <li>Consider using a password manager</li>
                        <li>Change your password regularly</li>
                        <li>Use unique passwords for different accounts</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="mt-4">
                    <a href="{% url 'account_login' %}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In Now
                    </a>

                    <a href="{% url 'home' %}" class="btn btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        Go to Homepage
                    </a>
                </div>

                <!-- Additional help -->
                <div class="mt-4">
                    <p class="text-muted">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            Remember to update your password in any saved password managers or browsers.
                        </small>
                    </p>
                    <p class="text-muted">
                        <small>
                            Having trouble signing in? 
                            <a href="mailto:<EMAIL>">Contact our support team</a>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
