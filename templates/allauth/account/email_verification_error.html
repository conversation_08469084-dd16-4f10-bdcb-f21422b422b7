{% extends "base.html" %}
{% load static %}

{% block title %}Email Verification Error - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
        text-align: center;
    }
    
    .error-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 3rem 2rem;
        margin-bottom: 1.5rem;
    }
    
    .error-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 3rem;
        animation: errorShake 0.5s ease-in-out;
    }
    
    @keyframes errorShake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    .error-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .error-message {
        color: #525252;
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-details {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }
    
    .error-details h4 {
        color: #721c24;
        font-family: 'Poppins', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .error-details p {
        color: #721c24;
        margin: 0;
        line-height: 1.6;
    }
    
    .solutions {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: left;
    }
    
    .solutions h3 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .solutions-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .solutions-list li {
        padding: 0.75rem 0;
        color: #525252;
        font-size: 1rem;
        line-height: 1.5;
        position: relative;
        padding-left: 2.5rem;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .solutions-list li:last-child {
        border-bottom: none;
    }
    
    .solutions-list li:before {
        content: "💡";
        position: absolute;
        left: 0;
        top: 0.75rem;
        font-size: 1.2rem;
    }
    
    .action-button {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0.5rem;
    }
    
    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .secondary-button {
        background: white;
        color: #2F160F;
        border: 2px solid #2F160F;
    }
    
    .secondary-button:hover {
        background: #2F160F;
        color: white;
    }
    
    .warning-button {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
    }
    
    .warning-button:hover {
        color: #212529;
        box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
    }
    
    .help-section {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .help-section h4 {
        color: #0056b3;
        font-family: 'Poppins', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .help-section p {
        color: #0056b3;
        margin: 0;
        line-height: 1.6;
    }
    
    .help-section a {
        color: #0056b3;
        font-weight: 600;
    }
    
    .error-code {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 0.5rem 1rem;
        font-family: monospace;
        font-size: 0.875rem;
        color: #495057;
        margin: 1rem 0;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-card">
        <div class="error-icon">
            ❌
        </div>
        
        <h1 class="error-title">{{ error_title }}</h1>
        
        <p class="error-message">
            {{ error_message }}
        </p>
        
        <div class="error-code">
            Error Type: {{ error_type|upper }}
        </div>
        
        {% if error_type == 'expired' %}
            <div class="error-details">
                <h4>🕐 Link Expired</h4>
                <p>Email verification links expire after 24 hours for security reasons. This helps protect your account from unauthorized access.</p>
            </div>
            
            <div class="solutions">
                <h3>🔧 How to Fix This</h3>
                <ul class="solutions-list">
                    <li><strong>Request a new verification email</strong> - Click the button below to get a fresh link</li>
                    <li><strong>Check your email immediately</strong> - New links are valid for 24 hours</li>
                    <li><strong>Complete verification quickly</strong> - Don't wait too long to click the link</li>
                    <li><strong>Check spam folder</strong> - Sometimes emails end up in spam</li>
                </ul>
            </div>
            
        {% elif error_type == 'invalid' %}
            <div class="error-details">
                <h4>🔗 Invalid Link</h4>
                <p>This verification link is either malformed, has already been used, or doesn't exist in our system.</p>
            </div>
            
            <div class="solutions">
                <h3>🔧 How to Fix This</h3>
                <ul class="solutions-list">
                    <li><strong>Request a new verification email</strong> - Get a fresh, valid link</li>
                    <li><strong>Copy the full link</strong> - Make sure you copied the complete URL from your email</li>
                    <li><strong>Check for line breaks</strong> - Sometimes email clients break long URLs</li>
                    <li><strong>Try a different browser</strong> - Some browsers may have issues with certain links</li>
                </ul>
            </div>
            
        {% elif error_type == 'already_verified' %}
            <div class="error-details">
                <h4>✅ Already Verified</h4>
                <p>Good news! Your email address has already been verified. You can proceed to use your account normally.</p>
            </div>
            
            <div class="solutions">
                <h3>🚀 What You Can Do Now</h3>
                <ul class="solutions-list">
                    <li><strong>Sign in to your account</strong> - Your email is verified and ready to use</li>
                    <li><strong>Complete your profile</strong> - Add more information to enhance your experience</li>
                    <li><strong>Start exploring</strong> - Browse services and make your first booking</li>
                    <li><strong>Contact support</strong> - If you're still having issues, we're here to help</li>
                </ul>
            </div>
            
        {% else %}
            <div class="error-details">
                <h4>❓ Unknown Error</h4>
                <p>An unexpected error occurred during email verification. This might be a temporary issue.</p>
            </div>
            
            <div class="solutions">
                <h3>🔧 Troubleshooting Steps</h3>
                <ul class="solutions-list">
                    <li><strong>Try again in a few minutes</strong> - This might be a temporary server issue</li>
                    <li><strong>Request a new verification email</strong> - Get a fresh link to try again</li>
                    <li><strong>Clear your browser cache</strong> - Sometimes cached data causes issues</li>
                    <li><strong>Contact our support team</strong> - We'll help you resolve this quickly</li>
                </ul>
            </div>
        {% endif %}
        
        <div class="text-center">
            {% if show_resend %}
                {% if user.is_authenticated %}
                    <a href="{% url 'accounts_app:email_verification_status' %}" class="action-button">
                        📧 Request New Verification Email
                    </a>
                {% else %}
                    <a href="{% url 'accounts_app:customer_login' %}" class="action-button">
                        🔑 Sign In to Request New Email
                    </a>
                {% endif %}
            {% endif %}
            
            {% if error_type == 'already_verified' %}
                {% if user.is_authenticated %}
                    {% if user.role == 'service_provider' %}
                        <a href="{% url 'accounts_app:provider_profile' %}" class="action-button">
                            🏢 Go to Business Dashboard
                        </a>
                    {% else %}
                        <a href="{% url 'dashboard_app:customer_dashboard' %}" class="action-button">
                            🌟 Go to Dashboard
                        </a>
                    {% endif %}
                {% else %}
                    <a href="{% url 'accounts_app:customer_login' %}" class="action-button">
                        🔑 Sign In to Your Account
                    </a>
                {% endif %}
            {% endif %}
            
            <a href="{% url 'venues_app:home' %}" class="action-button secondary-button">
                🏠 Return to Home
            </a>
        </div>
        
        <div class="help-section">
            <h4>🆘 Need Additional Help?</h4>
            <p>
                If you continue to experience issues with email verification, our support team is ready to assist you. 
                Contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or through our help center.
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Log error for analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'email_verification_error', {
            'error_type': '{{ error_type }}',
            'user_authenticated': {{ user.is_authenticated|yesno:"true,false" }}
        });
    }
    
    // Auto-focus on primary action button
    const primaryButton = document.querySelector('.action-button');
    if (primaryButton) {
        primaryButton.focus();
    }
});
</script>
{% endblock %}
