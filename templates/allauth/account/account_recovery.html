{% extends "base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Account Recovery - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .recovery-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .recovery-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .form-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
    }
    
    .form-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .form-subtitle {
        color: #525252;
        font-size: 1rem;
        margin: 0;
    }
    
    .form-control {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #f59e0b;
        box-shadow: 0 0 0 0.2rem rgba(245, 158, 11, 0.25);
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
        color: white;
    }
    
    .btn-link {
        color: #2F160F;
        text-decoration: none;
        font-weight: 500;
    }
    
    .btn-link:hover {
        color: #4a2a1f;
        text-decoration: underline;
    }
    
    .alert {
        border-radius: 8px;
        border: none;
        padding: 1rem;
    }
    
    .alert-warning {
        background: #fffbeb;
        color: #92400e;
        border-left: 4px solid #f59e0b;
    }
    
    .alert ul {
        padding-left: 1.2rem;
    }
    
    .recovery-process {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .recovery-process h4 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .process-steps {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .process-steps li {
        padding: 0.75rem 0;
        color: #525252;
        position: relative;
        padding-left: 2.5rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .process-steps li:last-child {
        border-bottom: none;
    }
    
    .process-steps li:before {
        content: counter(step-counter);
        counter-increment: step-counter;
        position: absolute;
        left: 0;
        top: 0.75rem;
        background: #f59e0b;
        color: white;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .process-steps {
        counter-reset: step-counter;
    }
    
    .verification-requirements {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1.5rem;
    }
    
    .verification-requirements h5 {
        color: #0369a1;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .verification-requirements ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .verification-requirements li {
        padding: 0.5rem 0;
        color: #0369a1;
        position: relative;
        padding-left: 1.5rem;
    }
    
    .verification-requirements li:before {
        content: "📋";
        position: absolute;
        left: 0;
        top: 0.5rem;
    }
    
    .help-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1.5rem;
        text-align: center;
    }
    
    .help-section h5 {
        color: #2F160F;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .help-section p {
        color: #525252;
        margin: 0;
    }
    
    .help-section a {
        color: #2F160F;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="recovery-container">
    <div class="recovery-card">
        {% crispy form %}
        
        <div class="recovery-process">
            <h4>🔄 Recovery Process</h4>
            <ol class="process-steps">
                <li><strong>Request Submission:</strong> Your recovery request is submitted to our security team</li>
                <li><strong>Initial Review:</strong> We verify the request and check account status</li>
                <li><strong>Identity Verification:</strong> You may be asked to provide additional verification</li>
                <li><strong>Account Recovery:</strong> Once verified, we'll help restore access to your account</li>
                <li><strong>Security Update:</strong> We'll recommend security improvements for your account</li>
            </ol>
        </div>
        
        <div class="verification-requirements">
            <h5>📋 What You May Need to Provide</h5>
            <ul>
                <li>Government-issued photo ID</li>
                <li>Recent booking confirmations or receipts</li>
                <li>Details about your last successful login</li>
                <li>Information about services you've used</li>
                <li>Any other account-related information</li>
            </ul>
        </div>
        
        <div class="help-section">
            <h5>🆘 Emergency Contact</h5>
            <p>
                For urgent security concerns or if you believe your account has been compromised, 
                contact our security team immediately at 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on email field
    const emailField = document.querySelector('input[name="email"]');
    if (emailField) {
        emailField.focus();
    }
    
    // Form submission tracking
    const form = document.querySelector('.account-recovery-form');
    if (form) {
        form.addEventListener('submit', function() {
            // Track recovery attempts
            if (typeof gtag !== 'undefined') {
                gtag('event', 'account_recovery_attempt', {
                    'event_category': 'authentication',
                    'event_label': 'account_recovery_form'
                });
            }
        });
    }
    
    // Dynamic help text based on recovery reason
    const reasonSelect = document.querySelector('select[name="recovery_reason"]');
    const additionalInfoField = document.querySelector('textarea[name="additional_info"]');
    
    if (reasonSelect && additionalInfoField) {
        reasonSelect.addEventListener('change', function() {
            const reason = this.value;
            let placeholder = 'Please provide any additional details that might help us verify your identity and resolve the issue...';
            
            switch(reason) {
                case 'forgot_password':
                    placeholder = 'Please describe when you last successfully logged in and any error messages you received when trying to reset your password...';
                    break;
                case 'account_locked':
                    placeholder = 'Please describe what happened when you tried to log in and any error messages you received...';
                    break;
                case 'email_changed':
                    placeholder = 'Please provide details about when you noticed the email change and your original email address...';
                    break;
                case 'suspicious_activity':
                    placeholder = 'Please describe the suspicious activity you noticed and when it occurred...';
                    break;
                case 'lost_access':
                    placeholder = 'Please provide details about your old email account and when you lost access to it...';
                    break;
            }
            
            additionalInfoField.placeholder = placeholder;
        });
    }
});
</script>
{% endblock %}
