{% extends 'base.html' %}

{% block title %}Sign Out - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Logout */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-200: #e5e5e5;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Logout Section */
    .logout-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .logout-container {
        max-width: 520px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .logout-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .logout-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .logout-icon {
        width: 80px;
        height: 80px;
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .logout-icon i {
        color: var(--cw-brand-primary);
        font-size: 2rem;
    }

    .logout-title {
        color: var(--cw-brand-primary);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
        font-family: var(--cw-font-heading);
    }

    .logout-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 0;
        line-height: 1.6;
    }

    /* Content Section */
    .logout-content {
        padding: 3rem;
        text-align: center;
    }

    .logout-message {
        color: var(--cw-neutral-700);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    /* Button styling */
    .btn-primary {
        background: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        width: 100%;
        margin-bottom: 1rem;
    }

    .btn-primary:hover {
        background: var(--cw-brand-light);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        width: 100%;
    }

    .btn-secondary:hover {
        background: var(--cw-accent-light);
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    /* Alert styling */
    .alert {
        border: 2px solid;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-info {
        background-color: #f0f9ff;
        color: #007bff;
        border-color: #007bff;
    }

    .alert-warning {
        background-color: #fffbf0;
        color: #f59e0b;
        border-color: #f59e0b;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* User info styling */
    .user-info {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .user-info h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .user-info p {
        color: var(--cw-neutral-700);
        margin-bottom: 0;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .logout-header {
            padding: 2rem 2rem 1.5rem;
        }

        .logout-content {
            padding: 2rem;
        }

        .logout-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="logout-section">
    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-header">
                <div class="logout-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h1 class="logout-title">Sign Out</h1>
                <p class="logout-subtitle">Are you sure you want to sign out?</p>
            </div>

            <div class="logout-content">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% if user.is_authenticated %}
                    <!-- User is logged in - show logout confirmation -->
                    <div class="user-info">
                        <h6><i class="fas fa-user me-2"></i>Currently signed in as:</h6>
                        <p>{{ user.get_full_name|default:user.email }}</p>
                    </div>

                    <div class="logout-message">
                        You will be signed out of your CozyWish account. Any unsaved changes will be lost.
                    </div>

                    <!-- Logout Form -->
                    <form method="post" action="{% url 'account_logout' %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Yes, Sign Me Out
                        </button>
                    </form>

                    <!-- Cancel Link -->
                    <a href="{% url 'dashboard_app:dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>

                {% else %}
                    <!-- User is not logged in -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        You are not currently signed in.
                    </div>

                    <div class="logout-message">
                        You have been successfully signed out of your CozyWish account.
                    </div>

                    <!-- Action Buttons -->
                    <a href="{% url 'account_login' %}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In
                    </a>

                    <a href="{% url 'home' %}" class="btn btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        Go to Homepage
                    </a>
                {% endif %}

                <!-- Additional Links -->
                <div class="mt-4">
                    <p class="text-muted">
                        <small>
                            Need help? 
                            <a href="mailto:<EMAIL>">Contact our support team</a>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
