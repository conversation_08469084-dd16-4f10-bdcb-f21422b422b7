{% extends 'accounts/base_auth.html' %}

{% block title %}Reset Password - CozyWish{% endblock %}
{% block auth_header %}
    {% include 'accounts/components/auth_header.html' with icon='fas fa-lock' title='Reset Password' subtitle='Enter your email address to reset your password' %}
{% endblock %}

{% block auth_content %}
    <!-- Form Errors -->
    {% include 'accounts/components/form_errors.html' with form=form %}

    <!-- Instructions -->
    <div class="instructions">
        <h6><i class="fas fa-info-circle me-2"></i>Password Reset Instructions</h6>
        <ul>
            <li>Enter the email address associated with your account</li>
            <li>We'll send you a secure link to reset your password</li>
            <li>Check your email (including spam folder) for the reset link</li>
            <li>Follow the link to create a new password</li>
        </ul>
    </div>

    <!-- Password Reset Form -->
    <form method="post" action="{% url 'account_reset_password' %}" novalidate>
        {% csrf_token %}

        <!-- Email Field -->
        {% include 'accounts/components/form_field.html' with field=form.email field_type='email' placeholder='Enter your email address' autocomplete='email' %}

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary mb-3">
            <i class="fas fa-paper-plane me-2"></i>
            Send Reset Link
        </button>

        <!-- Back to Login -->
        <div class="text-center">
            <a href="{% url 'account_login' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Sign In
            </a>
        </div>
    </form>

    <!-- Footer -->
    {% include 'accounts/components/auth_footer.html' with footer_style='minimal' %}
{% endblock %}


