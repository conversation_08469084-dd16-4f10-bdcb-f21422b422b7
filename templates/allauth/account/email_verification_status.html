{% extends "base.html" %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Email Verification Status - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .verification-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .verification-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .verification-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .verification-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
    }
    
    .verification-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .verification-subtitle {
        color: #525252;
        font-size: 1rem;
        margin: 0;
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        margin: 1rem 0;
    }
    
    .status-verified {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .resend-section {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .resend-button {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .resend-button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    }
    
    .resend-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }
    
    .cooldown-timer {
        color: #856404;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        text-align: center;
    }
    
    .verification-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .verification-info h4 {
        color: #2F160F;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .verification-info ul {
        margin: 0;
        padding-left: 1.5rem;
    }
    
    .verification-info li {
        color: #525252;
        margin-bottom: 0.25rem;
    }
    
    .email-display {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 0.75rem;
        font-family: monospace;
        font-size: 0.875rem;
        color: #495057;
        word-break: break-all;
        margin: 0.5rem 0;
    }
    
    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #2F160F;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .alert {
        padding: 0.75rem 1rem;
        border-radius: 6px;
        margin: 1rem 0;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .alert-danger {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>
{% endblock %}

{% block content %}
<div class="verification-container">
    <div class="verification-card">
        <div class="verification-header">
            <div class="verification-icon">
                {% if email_address.verified %}
                    ✅
                {% else %}
                    📧
                {% endif %}
            </div>
            <h1 class="verification-title">Email Verification</h1>
            <p class="verification-subtitle">
                {% if email_address.verified %}
                    Your email address has been verified
                {% else %}
                    Please verify your email address to continue
                {% endif %}
            </p>
        </div>
        
        <!-- Verification Status -->
        <div class="text-center">
            <div class="email-display">{{ user.email }}</div>
            
            {% if email_address.verified %}
                <span class="status-badge status-verified">✅ Verified</span>
            {% else %}
                <span class="status-badge status-pending">⏳ Pending Verification</span>
            {% endif %}
        </div>
        
        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        
        <!-- Resend Section -->
        {% if not email_address.verified %}
            <div class="resend-section">
                <h3 style="color: #2F160F; font-size: 1.1rem; margin-bottom: 1rem;">
                    Didn't receive the email?
                </h3>
                
                <form id="resend-form" method="post" action="{% url 'accounts_app:resend_verification_email' %}">
                    {% csrf_token %}
                    <button type="submit" class="resend-button" id="resend-btn" {% if not can_resend %}disabled{% endif %}>
                        <span class="loading-spinner" id="loading-spinner"></span>
                        <span id="button-text">
                            {% if can_resend %}
                                📧 Resend Verification Email
                            {% else %}
                                ⏳ Please wait before resending
                            {% endif %}
                        </span>
                    </button>
                </form>
                
                {% if not can_resend %}
                    <div class="cooldown-timer" id="cooldown-timer">
                        You can request another email in <span id="countdown"></span> seconds
                    </div>
                {% endif %}
            </div>
            
            <div class="verification-info">
                <h4>📋 Verification Checklist</h4>
                <ul>
                    <li>Check your inbox for an email from CozyWish</li>
                    <li>Look in your spam or junk folder</li>
                    <li>Make sure {{ user.email }} is correct</li>
                    <li>Click the verification link in the email</li>
                    <li>Return to this page to continue</li>
                </ul>
            </div>
        {% else %}
            <div class="verification-info">
                <h4>🎉 What's Next?</h4>
                <ul>
                    {% if user.role == 'service_provider' %}
                        <li>Complete your business profile</li>
                        <li>Add your services and pricing</li>
                        <li>Start receiving bookings</li>
                    {% else %}
                        <li>Explore spa and wellness services</li>
                        <li>Book your first appointment</li>
                        <li>Leave reviews and build your profile</li>
                    {% endif %}
                </ul>
                
                <div class="text-center" style="margin-top: 1.5rem;">
                    {% if user.role == 'service_provider' %}
                        <a href="{% url 'accounts_app:provider_profile' %}" class="resend-button" style="text-decoration: none; display: inline-block;">
                            🏢 Complete Business Profile
                        </a>
                    {% else %}
                        <a href="{% url 'dashboard_app:customer_dashboard' %}" class="resend-button" style="text-decoration: none; display: inline-block;">
                            🌟 Explore Services
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resendForm = document.getElementById('resend-form');
    const resendBtn = document.getElementById('resend-btn');
    const buttonText = document.getElementById('button-text');
    const loadingSpinner = document.getElementById('loading-spinner');
    const countdownElement = document.getElementById('countdown');
    
    // Handle form submission
    if (resendForm) {
        resendForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (resendBtn.disabled) return;
            
            // Show loading state
            resendBtn.disabled = true;
            loadingSpinner.style.display = 'inline-block';
            buttonText.textContent = 'Sending...';
            
            // Submit form via AJAX
            fetch(resendForm.action, {
                method: 'POST',
                body: new FormData(resendForm),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Hide loading state
                loadingSpinner.style.display = 'none';
                
                if (data.success) {
                    buttonText.textContent = '✅ Email Sent!';
                    // Show success message
                    showMessage(data.message, 'success');
                    // Start cooldown
                    startCooldown(300); // 5 minutes
                } else {
                    buttonText.textContent = '📧 Resend Verification Email';
                    resendBtn.disabled = false;
                    // Show error message
                    showMessage(data.message, 'danger');
                    
                    if (data.cooldown_remaining) {
                        startCooldown(data.cooldown_remaining);
                    }
                }
            })
            .catch(error => {
                // Hide loading state
                loadingSpinner.style.display = 'none';
                buttonText.textContent = '📧 Resend Verification Email';
                resendBtn.disabled = false;
                showMessage('An error occurred. Please try again.', 'danger');
            });
        });
    }
    
    // Cooldown timer
    function startCooldown(seconds) {
        if (!countdownElement) return;
        
        resendBtn.disabled = true;
        buttonText.textContent = '⏳ Please wait before resending';
        
        const timer = setInterval(() => {
            countdownElement.textContent = seconds;
            seconds--;
            
            if (seconds < 0) {
                clearInterval(timer);
                resendBtn.disabled = false;
                buttonText.textContent = '📧 Resend Verification Email';
                const cooldownTimer = document.getElementById('cooldown-timer');
                if (cooldownTimer) {
                    cooldownTimer.style.display = 'none';
                }
            }
        }, 1000);
    }
    
    // Show message function
    function showMessage(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        const container = document.querySelector('.verification-card');
        const header = container.querySelector('.verification-header');
        header.insertAdjacentElement('afterend', alertDiv);
        
        // Remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
    
    // Auto-refresh verification status every 30 seconds
    if (!{{ email_address.verified|yesno:"true,false" }}) {
        setInterval(checkVerificationStatus, 30000);
    }
    
    function checkVerificationStatus() {
        fetch('{% url "accounts_app:email_verification_check_ajax" %}')
        .then(response => response.json())
        .then(data => {
            if (data.verified) {
                location.reload();
            }
        })
        .catch(error => {
            console.log('Verification check failed:', error);
        });
    }
});
</script>
{% endblock %}
