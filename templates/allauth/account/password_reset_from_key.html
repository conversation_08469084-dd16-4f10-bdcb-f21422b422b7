{% extends 'base.html' %}

{% block title %}Set New Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Password Reset From Key */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-200: #e5e5e5;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Reset Section */
    .reset-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .reset-container {
        max-width: 520px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .reset-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .reset-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .reset-icon {
        width: 80px;
        height: 80px;
        background: white;
        border: 2px solid var(--cw-brand-primary);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .reset-icon i {
        color: var(--cw-brand-primary);
        font-size: 2rem;
    }

    .reset-title {
        color: var(--cw-brand-primary);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
        font-family: var(--cw-font-heading);
    }

    .reset-subtitle {
        color: var(--cw-neutral-600);
        font-size: 1.1rem;
        margin-bottom: 0;
        line-height: 1.6;
    }

    /* Form Section */
    .reset-form-section {
        padding: 3rem;
    }

    .form-floating > label {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    .form-control {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 1rem;
        font-size: 1rem;
        color: var(--cw-neutral-800);
        background-color: white;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
        outline: none;
        background-color: white;
    }

    .form-control::placeholder {
        color: var(--cw-neutral-600);
    }

    /* Error state styling */
    .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    /* Button styling */
    .btn-primary {
        background: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        width: 100%;
    }

    .btn-primary:hover {
        background: var(--cw-brand-light);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        width: 100%;
    }

    .btn-secondary:hover {
        background: var(--cw-accent-light);
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    /* Alert styling */
    .alert {
        border: 2px solid;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .alert-danger {
        background-color: #fff5f5;
        color: #dc3545;
        border-color: #dc3545;
    }

    .alert-success {
        background-color: #f0fff4;
        color: #28a745;
        border-color: #28a745;
    }

    .alert-info {
        background-color: #f0f9ff;
        color: #007bff;
        border-color: #007bff;
    }

    /* Error message styling */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.9rem;
        color: #dc3545;
        font-weight: 500;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Form text (help text) styling */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        color: var(--cw-neutral-600);
    }

    /* Security info box */
    .security-info {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .security-info h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .security-info ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    .security-info li {
        margin-bottom: 0.5rem;
        color: var(--cw-neutral-700);
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .reset-header {
            padding: 2rem 2rem 1.5rem;
        }

        .reset-form-section {
            padding: 2rem;
        }

        .reset-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="reset-section">
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <div class="reset-icon">
                    <i class="fas fa-key"></i>
                </div>
                {% if token_fail %}
                    <h1 class="reset-title">Link Expired</h1>
                    <p class="reset-subtitle">This password reset link is no longer valid</p>
                {% else %}
                    <h1 class="reset-title">Set New Password</h1>
                    <p class="reset-subtitle">Choose a strong password for your account</p>
                {% endif %}
            </div>

            <div class="reset-form-section">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% if token_fail %}
                    <!-- Token Failed -->
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        The password reset link was invalid, possibly because it has already been used. Please request a new password reset.
                    </div>

                    <a href="{% url 'account_reset_password' %}" class="btn btn-primary mb-3">
                        <i class="fas fa-redo me-2"></i>
                        Request New Reset Link
                    </a>

                    <a href="{% url 'account_login' %}" class="btn btn-secondary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Back to Login
                    </a>

                {% else %}
                    <!-- Valid Token - Show Form -->
                    <!-- Security Information -->
                    <div class="security-info">
                        <h6><i class="fas fa-shield-alt me-2"></i>Password Security Tips</h6>
                        <ul>
                            <li>Use at least 8 characters</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Add numbers and special characters</li>
                            <li>Avoid common words or personal information</li>
                        </ul>
                    </div>

                    <!-- Password Reset Form -->
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- New Password Field -->
                        <div class="form-floating mb-3">
                            {{ form.password1|add_class:"form-control"|attr:"placeholder:Enter your new password"|attr:"autocomplete:new-password" }}
                            <label for="{{ form.password1.id_for_label }}">{{ form.password1.label }}</label>
                            {% if form.password1.help_text %}
                                <div class="form-text">{{ form.password1.help_text }}</div>
                            {% endif %}
                            {% if form.password1.errors %}
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.password1.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Confirm New Password Field -->
                        <div class="form-floating mb-3">
                            {{ form.password2|add_class:"form-control"|attr:"placeholder:Confirm your new password"|attr:"autocomplete:new-password" }}
                            <label for="{{ form.password2.id_for_label }}">{{ form.password2.label }}</label>
                            {% if form.password2.errors %}
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.password2.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary mb-3">
                            <i class="fas fa-save me-2"></i>
                            Set New Password
                        </button>

                        <!-- Back to Login Link -->
                        <a href="{% url 'account_login' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Login
                        </a>
                    </form>
                {% endif %}

                <!-- Additional help -->
                <div class="text-center mt-4">
                    <p class="text-muted">
                        <small>
                            Having trouble? 
                            <a href="mailto:<EMAIL>">Contact our support team</a>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
