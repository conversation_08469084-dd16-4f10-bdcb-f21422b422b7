{% extends "base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Reset Password - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .password-reset-container {
        max-width: 500px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .password-reset-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .form-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
    }
    
    .form-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .form-subtitle {
        color: #525252;
        font-size: 1rem;
        margin: 0;
    }
    
    .form-control-lg {
        padding: 0.75rem 1rem;
        font-size: 1.1rem;
        border-radius: 8px;
        border: 2px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .form-control-lg:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(47, 22, 15, 0.3);
    }
    
    .btn-link {
        color: #2F160F;
        text-decoration: none;
        font-weight: 500;
    }
    
    .btn-link:hover {
        color: #4a2a1f;
        text-decoration: underline;
    }
    
    .alert {
        border-radius: 8px;
        border: none;
        padding: 1rem;
    }
    
    .alert-info {
        background: #e7f3ff;
        color: #0369a1;
        border-left: 4px solid #0ea5e9;
    }
    
    .alert ul {
        padding-left: 1.2rem;
    }
    
    .security-features {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .security-features h4 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .security-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .security-features li {
        padding: 0.5rem 0;
        color: #525252;
        position: relative;
        padding-left: 1.5rem;
    }
    
    .security-features li:before {
        content: "🔒";
        position: absolute;
        left: 0;
        top: 0.5rem;
    }
    
    .help-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1.5rem;
        text-align: center;
    }
    
    .help-section h5 {
        color: #2F160F;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .help-section p {
        color: #525252;
        margin: 0;
    }
    
    .help-section a {
        color: #2F160F;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="password-reset-container">
    <div class="password-reset-card">
        {% crispy form %}
        
        <div class="security-features">
            <h4>🔐 Security Features</h4>
            <ul>
                <li>All password reset requests are logged for security</li>
                <li>Reset links are single-use and expire automatically</li>
                <li>We'll send you a security notification about this request</li>
                <li>Your account remains secure during the reset process</li>
            </ul>
        </div>
        
        <div class="help-section">
            <h5>Need Additional Help?</h5>
            <p>
                If you're unable to access your email or continue having issues, 
                try our <a href="{% url 'accounts_app:account_recovery' %}">account recovery process</a> 
                or contact <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on email field
    const emailField = document.querySelector('input[name="email"]');
    if (emailField) {
        emailField.focus();
    }
    
    // Form submission tracking
    const form = document.querySelector('.password-reset-form');
    if (form) {
        form.addEventListener('submit', function() {
            // Track password reset attempts
            if (typeof gtag !== 'undefined') {
                gtag('event', 'password_reset_attempt', {
                    'event_category': 'authentication',
                    'event_label': 'password_reset_form'
                });
            }
        });
    }
});
</script>
{% endblock %}
