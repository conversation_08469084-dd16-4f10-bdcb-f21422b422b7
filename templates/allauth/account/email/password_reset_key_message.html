{% extends "emails/base_email.html" %}

{% block email_title %}Reset Your CozyWish Password{% endblock %}

{% block header_gradient %}linear-gradient(135deg, #dc2626 0%, #ef4444 100%){% endblock %}
{% block button_gradient %}linear-gradient(135deg, #dc2626 0%, #ef4444 100%){% endblock %}

{% block header_icon %}🔐{% endblock %}
{% block header_title %}Password Reset{% endblock %}
{% block header_subtitle %}Secure password reset for your account{% endblock %}

{% block custom_styles %}
.reset-link-box {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    word-break: break-all;
    font-family: monospace;
    font-size: 14px;
    color: #991b1b;
}

.security-tips {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 12px;
    padding: 24px;
    margin: 24px 0;
}

.security-tips h3 {
    color: #0369a1;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    padding: 8px 0;
    color: #0369a1;
    font-size: 15px;
    line-height: 1.5;
    position: relative;
    padding-left: 24px;
}

.tips-list li:before {
    content: "🔒";
    position: absolute;
    left: 0;
    top: 8px;
}

.request-info {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    border-radius: 8px;
    padding: 16px;
    margin: 20px 0;
    font-size: 14px;
}

.request-info h4 {
    color: #92400e;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.request-info p {
    color: #92400e;
    margin: 4px 0;
}

.warning-box {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 16px;
    margin: 20px 0;
}

.warning-box h4 {
    color: #991b1b;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.warning-box p {
    color: #991b1b;
    margin: 0;
    line-height: 1.5;
}
{% endblock %}

{% block email_content %}
<p><strong>Hello {{ user.get_full_name|default:user.email }},</strong></p>

<p>You recently requested to reset your password for your <strong>CozyWish</strong> account.</p>

<p>If you did not request this password reset, please ignore this email and your password will remain unchanged.</p>

<p>To create a new password, please click the button below:</p>
{% endblock %}

{% block action_section %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ password_reset_url }}" class="action-button">
        🔐 Reset My Password
    </a>
</div>

<p>Or copy and paste this link into your browser:</p>

<div class="reset-link-box">
    {{ password_reset_url }}
</div>
{% endblock %}

{% block details_section %}
<div class="account-details">
    <div class="details-title">
        <span>🏠</span> Account Details
    </div>
    <ul class="details-list">
        <li>
            <span class="details-label">Email:</span>
            <span class="details-value">{{ user.email }}</span>
        </li>
        <li>
            <span class="details-label">Account Type:</span>
            <span class="details-value">{{ user.role|default:"Customer"|title }}</span>
        </li>
        <li>
            <span class="details-label">Reset Requested:</span>
            <span class="details-value">{{ timestamp|date:"F j, Y g:i A" }}</span>
        </li>
    </ul>
</div>

<div class="request-info">
    <h4>🔍 Request Information</h4>
    <p><strong>IP Address:</strong> {{ request.META.REMOTE_ADDR|default:"Unknown" }}</p>
    <p><strong>User Agent:</strong> {{ request.META.HTTP_USER_AGENT|default:"Unknown"|truncatechars:100 }}</p>
    <p><strong>Time:</strong> {{ timestamp|date:"F j, Y g:i A T" }}</p>
</div>

<div class="security-warning">
    <p><strong>⏰ Important:</strong> This password reset link will expire in 24 hours for your security.</p>
</div>

<div class="security-tips">
    <h3>🔒 Password Security Tips</h3>
    <ul class="tips-list">
        <li>Use a strong, unique password with at least 8 characters</li>
        <li>Include uppercase, lowercase, numbers, and special characters</li>
        <li>Don't reuse passwords from other accounts</li>
        <li>Consider using a password manager</li>
        <li>Never share your password with anyone</li>
    </ul>
</div>
{% endblock %}

{% block additional_content %}
<div class="warning-box">
    <h4>⚠️ Security Alert</h4>
    <p>If you didn't request this password reset, someone may be trying to access your account. Please contact our support team immediately at <a href="mailto:<EMAIL>" style="color: #991b1b;"><EMAIL></a></p>
</div>

<p>Having trouble with the button above? Copy and paste the full URL from the box above into your browser's address bar.</p>

<p>Need help? Contact our support team at <a href="mailto:<EMAIL>" style="color: #2F160F;"><EMAIL></a></p>

<p>Stay secure,<br>The CozyWish Security Team</p>
{% endblock %}
