{% extends "base.html" %}
{% load static %}

{% block title %}Email Verified Successfully - CozyWish{% endblock %}

{% block extra_css %}
<style>
    .success-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 0 1rem;
        text-align: center;
    }
    
    .success-card {
        background: #fef7f0;
        border: 1px solid #fae1d7;
        border-radius: 12px;
        padding: 3rem 2rem;
        margin-bottom: 1.5rem;
    }
    
    .success-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 3rem;
        animation: successPulse 2s ease-in-out;
    }
    
    @keyframes successPulse {
        0% { transform: scale(0.8); opacity: 0; }
        50% { transform: scale(1.1); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }
    
    .success-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .success-subtitle {
        color: #525252;
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .next-steps {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: left;
    }
    
    .next-steps h3 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .steps-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .steps-list li {
        padding: 0.75rem 0;
        color: #525252;
        font-size: 1rem;
        line-height: 1.5;
        position: relative;
        padding-left: 2.5rem;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .steps-list li:last-child {
        border-bottom: none;
    }
    
    .steps-list li:before {
        content: "✨";
        position: absolute;
        left: 0;
        top: 0.75rem;
        font-size: 1.2rem;
    }
    
    .action-button {
        background: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 1rem 0.5rem;
    }
    
    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .secondary-button {
        background: white;
        color: #2F160F;
        border: 2px solid #2F160F;
    }
    
    .secondary-button:hover {
        background: #2F160F;
        color: white;
    }
    
    .welcome-message {
        background: linear-gradient(135deg, #fef7f0 0%, #fae1d7 100%);
        border: 1px solid #f1d4c4;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .welcome-message h4 {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .welcome-message p {
        color: #525252;
        margin: 0;
        line-height: 1.6;
    }
    
    .account-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    
    .account-info strong {
        color: #2F160F;
    }
    
    .confetti {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
    }
    
    .confetti-piece {
        position: absolute;
        width: 10px;
        height: 10px;
        background: #ffd700;
        animation: confetti-fall 3s linear infinite;
    }
    
    @keyframes confetti-fall {
        0% {
            transform: translateY(-100vh) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(720deg);
            opacity: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="success-container">
    <div class="success-card">
        <div class="success-icon">
            ✅
        </div>
        
        <h1 class="success-title">Email Verified Successfully!</h1>
        
        <p class="success-subtitle">
            Congratulations! Your email address has been verified and your CozyWish account is now fully activated.
        </p>
        
        {% if user.is_authenticated %}
            <div class="account-info">
                <strong>Account:</strong> {{ user.email }}<br>
                <strong>Type:</strong> {{ user.role|default:"Customer"|title }}<br>
                <strong>Status:</strong> ✅ Verified
            </div>
        {% endif %}
        
        <div class="welcome-message">
            {% if user.role == 'service_provider' %}
                <h4>🏢 Welcome to CozyWish Business!</h4>
                <p>You're now ready to start building your business profile and connecting with customers looking for wellness services.</p>
            {% else %}
                <h4>🌟 Welcome to CozyWish!</h4>
                <p>You're now ready to discover and book amazing spa and wellness experiences in your area.</p>
            {% endif %}
        </div>
        
        <div class="next-steps">
            <h3>🚀 What's Next?</h3>
            <ul class="steps-list">
                {% if user.role == 'service_provider' %}
                    <li><strong>Complete your business profile</strong> - Add your business information, photos, and contact details</li>
                    <li><strong>Set up your services</strong> - Create service listings with descriptions, pricing, and availability</li>
                    <li><strong>Configure your booking settings</strong> - Set your hours, booking policies, and payment preferences</li>
                    <li><strong>Start receiving bookings</strong> - Your profile will be visible to customers once complete</li>
                {% else %}
                    <li><strong>Explore local services</strong> - Browse spas, massage therapists, and wellness providers in your area</li>
                    <li><strong>Read reviews and ratings</strong> - See what other customers say about their experiences</li>
                    <li><strong>Book your first appointment</strong> - Find the perfect service and schedule your visit</li>
                    <li><strong>Build your profile</strong> - Save favorites, leave reviews, and get personalized recommendations</li>
                {% endif %}
            </ul>
        </div>
        
        <div class="text-center">
            <a href="{{ next_url }}" class="action-button">
                {{ next_action }}
            </a>
            
            {% if user.role == 'service_provider' %}
                <a href="{% url 'venues_app:home' %}" class="action-button secondary-button">
                    Browse Platform
                </a>
            {% else %}
                <a href="{% url 'accounts_app:customer_profile' %}" class="action-button secondary-button">
                    Edit Profile
                </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Confetti Animation -->
<div class="confetti" id="confetti"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Create confetti animation
    createConfetti();
    
    // Auto-redirect after 10 seconds if user doesn't interact
    let redirectTimer = setTimeout(() => {
        window.location.href = '{{ next_url }}';
    }, 10000);
    
    // Cancel auto-redirect if user interacts with page
    document.addEventListener('click', () => {
        clearTimeout(redirectTimer);
    });
    
    document.addEventListener('keydown', () => {
        clearTimeout(redirectTimer);
    });
});

function createConfetti() {
    const confettiContainer = document.getElementById('confetti');
    const colors = ['#ffd700', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
    
    for (let i = 0; i < 50; i++) {
        const confettiPiece = document.createElement('div');
        confettiPiece.className = 'confetti-piece';
        confettiPiece.style.left = Math.random() * 100 + '%';
        confettiPiece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confettiPiece.style.animationDelay = Math.random() * 3 + 's';
        confettiPiece.style.animationDuration = (Math.random() * 3 + 2) + 's';
        confettiContainer.appendChild(confettiPiece);
    }
    
    // Remove confetti after animation
    setTimeout(() => {
        confettiContainer.remove();
    }, 5000);
}
</script>
{% endblock %}
