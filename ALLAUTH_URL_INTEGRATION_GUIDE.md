# Django Allauth URL Integration Guide

## Overview

This guide explains the URL configuration for django-allauth integration with role-based authentication in the CozyWish project. The implementation maintains backward compatibility while providing modern allauth functionality.

## URL Structure

### URL Precedence

The URL configuration is designed with proper precedence to avoid conflicts:

1. **Custom authentication URLs** (accounts_app) - Higher precedence
2. **Django Allauth URLs** - Lower precedence

This is achieved by placing `accounts_app.urls` before `allauth.urls` in the main `urls.py`.

### Available URL Patterns

#### Role-Based Allauth Integration URLs (Recommended)

These URLs provide role-based signup/login that integrates with allauth:

```
/accounts/customer/signup/allauth/    - Customer signup (redirects to allauth)
/accounts/provider/signup/allauth/    - Provider signup (redirects to allauth)
/accounts/customer/login/allauth/     - Customer login (redirects to allauth)
/accounts/provider/login/allauth/     - Provider login (redirects to allauth)
/accounts/login/unified/              - Unified login (redirects to allauth)
```

#### Smart Redirect URLs

```
/accounts/redirect/post-auth/         - Post-authentication smart redirect
/accounts/redirect/role-switch/       - Role-based dashboard redirect
```

#### Legacy Custom URLs (Backward Compatibility)

These URLs maintain existing functionality:

```
/accounts/customer/signup/            - Legacy customer signup
/accounts/customer/login/             - Legacy customer login
/accounts/provider/signup/            - Legacy provider signup
/accounts/provider/login/             - Legacy provider login
/accounts/logout/                     - Unified logout
```

#### Django Allauth URLs

Standard allauth URLs are available:

```
/accounts/login/                      - Allauth login
/accounts/signup/                     - Allauth signup
/accounts/logout/                     - Allauth logout
/accounts/password/reset/             - Password reset
/accounts/password/change/            - Password change
/accounts/email/                      - Email management
/accounts/confirm-email/<key>/        - Email confirmation
```

## How It Works

### Role-Based Signup Flow

1. User visits `/accounts/customer/signup/allauth/`
2. View sets `signup_role = 'customer'` in session
3. User is redirected to `/accounts/signup/` (allauth)
4. Allauth adapter detects role from session
5. User is created with customer role
6. Customer profile is automatically created
7. User is redirected to appropriate dashboard

### Role-Based Login Flow

1. User visits `/accounts/customer/login/allauth/`
2. View sets role preference in session
3. User is redirected to `/accounts/login/` (allauth)
4. After successful login, adapter redirects based on role
5. Customer → `/dashboard/customer/`
6. Provider → `/dashboard/provider/`
7. Admin → `/admin-panel/`

### Session Variables Used

- `signup_role` - Role to assign during signup
- `signup_source` - Source of signup (customer_direct, provider_direct)
- `preferred_role` - Preferred role for login
- `login_next_url` - Next URL after login
- `auth_redirect_destination` - Specific redirect destination

## Usage Examples

### Frontend Links

#### For Customer Signup
```html
<a href="{% url 'accounts_app:customer_signup_allauth' %}">Sign Up as Customer</a>
```

#### For Provider Signup
```html
<a href="{% url 'accounts_app:provider_signup_allauth' %}">Sign Up as Service Provider</a>
```

#### For Unified Login
```html
<a href="{% url 'accounts_app:unified_login_redirect' %}">Login</a>
```

### Programmatic Redirects

#### In Views
```python
from django.shortcuts import redirect

# Redirect to role-based signup
return redirect('accounts_app:customer_signup_allauth')

# Redirect to smart post-auth handler
return redirect('accounts_app:post_auth_redirect')
```

#### Setting Custom Redirect
```python
# Set custom redirect destination
request.session['auth_redirect_destination'] = 'custom_app:custom_view'
return redirect('accounts_app:unified_login_redirect')
```

## Configuration

### Settings Required

```python
# Custom Allauth Adapters
ACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishAccountAdapter'
SOCIALACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishSocialAccountAdapter'

# Custom Allauth Forms
ACCOUNT_FORMS = {
    'signup': 'accounts_app.allauth_forms.CozyWishSignupForm',
    'login': 'accounts_app.allauth_forms.CozyWishLoginForm',
}

# Allauth Configuration
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_LOGIN_REDIRECT_URL = '/dashboard/'
ACCOUNT_SIGNUP_REDIRECT_URL = '/dashboard/'
```

### URL Configuration

In `project_root/urls.py`:
```python
urlpatterns = [
    # Custom URLs first (higher precedence)
    path('accounts/', include('accounts_app.urls')),
    
    # Allauth URLs second (lower precedence)
    path('accounts/', include('allauth.urls')),
    
    # Other URLs...
]
```

## Migration Guide

### From Custom Auth to Allauth Integration

1. **Update signup links** to use new allauth integration URLs:
   ```html
   <!-- Old -->
   <a href="{% url 'accounts_app:customer_signup' %}">Sign Up</a>
   
   <!-- New (Recommended) -->
   <a href="{% url 'accounts_app:customer_signup_allauth' %}">Sign Up</a>
   ```

2. **Update login links** to use unified login:
   ```html
   <!-- Old -->
   <a href="{% url 'accounts_app:customer_login' %}">Login</a>
   
   <!-- New (Recommended) -->
   <a href="{% url 'accounts_app:unified_login_redirect' %}">Login</a>
   ```

3. **Use smart redirects** for post-authentication routing:
   ```python
   # In views where you need smart redirects
   return redirect('accounts_app:post_auth_redirect')
   ```

### Backward Compatibility

- All existing URLs continue to work
- Existing forms and views are preserved
- No breaking changes to current functionality
- Gradual migration is possible

## Testing

Run the URL integration tests:

```bash
python manage.py test accounts_app.tests.test_allauth_url_integration
```

## Troubleshooting

### Common Issues

1. **URL conflicts**: Ensure custom URLs come before allauth URLs in main `urls.py`
2. **Role not set**: Check that session variables are properly set in redirect views
3. **Redirect loops**: Verify that adapters have proper fallback redirects
4. **Permission errors**: Ensure users have appropriate roles and profiles

### Debug Session Variables

```python
# In views or templates
print(f"Signup role: {request.session.get('signup_role')}")
print(f"Login next: {request.session.get('login_next_url')}")
```

## Security Considerations

- All views use CSRF protection
- Session variables are cleared after use
- Proper authentication checks on all redirect views
- Rate limiting applies to allauth URLs
- Email verification is mandatory for providers
