# Django Allauth Custom Adapters Guide

## Overview

This guide explains the custom Django Allauth adapters implemented for role-based user creation in the CozyWish project. The adapters handle automatic role assignment and profile creation for both regular and social authentication.

## Files Created

### 1. `accounts_app/allauth_adapters.py`
Contains two custom adapter classes:
- `CozyWishAccountAdapter` - Handles regular signup with role assignment
- `CozyWishSocialAccountAdapter` - Handles social login with role assignment

### 2. `accounts_app/tests/test_allauth_adapters.py`
Comprehensive test suite for the custom adapters.

### 3. Settings Configuration
Updated `project_root/settings.py` to use the custom adapters.

## Features

### CozyWishAccountAdapter

#### Role-Based User Creation
- **Automatic Role Detection**: Determines user role based on:
  1. Explicit role in form data
  2. URL path analysis (e.g., `/provider/` → service provider)
  3. Session data
  4. Default to customer role

#### Profile Creation
- **Customer Profiles**: Automatically creates `CustomerProfile` with form data
- **Service Provider Profiles**: Creates `ServiceProviderProfile` with business information
- **Transaction Safety**: Uses database transactions for data integrity

#### Email Verification
- **Service Providers**: Require email verification (`is_active = False`)
- **Customers**: Active immediately (`is_active = True`)

#### Custom Redirects
- **Role-Based Redirects**: Different redirect URLs based on user role
- **Service Providers**: `/dashboard/`
- **Customers**: `/dashboard/`
- **Admins**: `/admin-panel/`

### CozyWishSocialAccountAdapter

#### Social Role Detection
- **URL Analysis**: Detects business signups from URL patterns
- **Business Account Detection**: Analyzes social data for business indicators
- **Session Preferences**: Respects role preferences stored in session

#### Social Profile Creation
- **Data Extraction**: Extracts user information from social providers
- **Business Detection**: Identifies business accounts by name/email patterns
- **Profile Mapping**: Maps social data to appropriate profile fields

## Configuration

### Settings.py Configuration
```python
# Custom Allauth Adapters for role-based user creation
ACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishAccountAdapter'
SOCIALACCOUNT_ADAPTER = 'accounts_app.allauth_adapters.CozyWishSocialAccountAdapter'
```

### Required Settings (Already Configured)
```python
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_UNIQUE_EMAIL = True
```

## Usage Examples

### 1. Customer Signup
When a user signs up through `/accounts/customer/signup/`:
- Role automatically set to `customer`
- `CustomerProfile` created with form data
- User is immediately active
- Redirected to `/dashboard/`

### 2. Service Provider Signup
When a user signs up through `/accounts/provider/signup/`:
- Role automatically set to `service_provider`
- `ServiceProviderProfile` created with business data
- User requires email verification
- Redirected to `/dashboard/` after verification

### 3. Social Login
When a user logs in via Google OAuth:
- Role determined by URL or business indicators
- Appropriate profile created from social data
- User data extracted from provider

## Role Detection Logic

### URL-Based Detection
```python
# Customer URLs
/accounts/customer/signup/ → customer role
/accounts/client/signup/ → customer role

# Provider URLs
/accounts/provider/signup/ → service_provider role
/accounts/business/signup/ → service_provider role
/accounts/service/signup/ → service_provider role
```

### Form-Based Detection
```python
# Form with explicit role
form.cleaned_data = {'role': 'service_provider'}

# Form attribute
form.role = 'customer'
```

### Session-Based Detection
```python
# Set role preference in session
request.session['signup_role'] = 'service_provider'
request.session['social_signup_role'] = 'customer'
```

## Business Account Detection (Social)

### Name-Based Detection
Business keywords in social account names:
- business, company, corp, llc, inc, ltd, service

### Email-Based Detection
Business patterns in email domains:
- business, company, corp, services

## Profile Field Mapping

### Customer Profile Mapping
```python
form_field → profile_field
'first_name' → 'first_name'
'last_name' → 'last_name'
'phone_number' → 'phone_number'
'gender' → 'gender'
'birth_month' → 'birth_month'
'birth_year' → 'birth_year'
'address' → 'address'
'city' → 'city'
'zip_code' → 'zip_code'
```

### Service Provider Profile Mapping
```python
form_field → profile_field
'business_name' → 'legal_name'
'legal_name' → 'legal_name'
'display_name' → 'display_name'
'business_phone_number' → 'phone'
'phone' → 'phone'
'contact_person_name' → 'contact_name'
'contact_name' → 'contact_name'
'business_address' → 'address'
'address' → 'address'
'city' → 'city'
'state' → 'state'
'county' → 'county'
'zip_code' → 'zip_code'
'ein' → 'ein'
'website' → 'website'
'instagram' → 'instagram'
'facebook' → 'facebook'
'description' → 'description'
```

## Error Handling

### Transaction Safety
- All profile creation wrapped in database transactions
- Rollback on any error during user/profile creation
- Comprehensive error logging

### Graceful Degradation
- Social profile creation failures don't break login flow
- Missing form fields handled with sensible defaults
- Extensive logging for debugging

## Testing

### Running Tests
```bash
# Run all adapter tests
python manage.py test accounts_app.tests.test_allauth_adapters

# Run specific test class
python manage.py test accounts_app.tests.test_allauth_adapters.CozyWishAccountAdapterTests

# Run specific test
python manage.py test accounts_app.tests.test_allauth_adapters.CozyWishAccountAdapterTests.test_save_user_customer
```

### Test Coverage
- Role determination logic
- Profile creation for both user types
- Social account handling
- Error scenarios
- Email cleaning and validation
- Login redirects

## Integration with Existing Code

### Backward Compatibility
- Existing signup forms continue to work
- No changes required to existing views
- Maintains all existing business logic
- Preserves email verification workflow

### Enhanced Functionality
- Automatic role assignment
- Profile creation without manual intervention
- Social login support with role detection
- Improved user experience with smart redirects

## Troubleshooting

### Common Issues

1. **Role not detected correctly**
   - Check URL patterns in `_determine_user_role`
   - Verify form data structure
   - Check session data

2. **Profile creation fails**
   - Verify required fields are provided
   - Check field mapping in adapter
   - Review error logs

3. **Social login issues**
   - Ensure social app is configured
   - Check business detection logic
   - Verify social data structure

### Debugging
- Enable DEBUG logging for `accounts_app.allauth_adapters`
- Check database for created profiles
- Review allauth configuration

## Future Enhancements

### Potential Improvements
1. **Dynamic Role Selection**: Allow users to choose role during signup
2. **Role Migration**: Support changing user roles post-signup
3. **Enhanced Business Detection**: More sophisticated business account detection
4. **Custom Email Templates**: Role-specific email verification templates
5. **Advanced Redirects**: More granular redirect logic based on user state

### Configuration Options
Consider adding settings for:
- Default role assignment
- Email verification requirements per role
- Custom redirect URLs per role
- Business detection sensitivity

## Conclusion

The custom allauth adapters provide a robust foundation for role-based user management while maintaining compatibility with existing code. They handle the complexity of role assignment and profile creation automatically, improving the user experience and reducing manual intervention.
