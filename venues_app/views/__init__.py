"""Aggregate imports for venues_app views."""

# --- Common helpers and constants ---
from .common import (
    ServiceProviderRequiredMixin,
    MAX_SERVICES_PER_VENUE,
    MAX_FAQS_PER_VENUE,
    home_view,
    login_view,
)

# --- Search and public views ---
from .search import (
    venue_search,
    location_autocomplete,
    category_venues,
    services_by_category,
    services_by_tag,
    get_location_data,
    venue_detail,
    service_detail,
    flag_venue,
)

# --- Provider management views ---
from .provider import (
    VenueCreateView,
    VenueDeleteView,
    venue_create,
    venue_create_view,
    venue_create_wizard_view,
    venue_edit_wizard_view,
    venue_edit,
    venue_progress,
    trigger_auto_approval_check,
    venue_delete,
    manage_services,
    bulk_service_actions,
    manage_faqs,
    manage_operating_hours,
    manage_amenities,
    edit_faq,
    delete_faq,
    delete_amenity,
    provider_venues,
    provider_venue_detail,
    change_venue_status,
    ServiceCreateView,
    ServiceUpdateView,
    ServiceDeleteView,
    service_create,
    service_edit,
    service_delete,
    manage_venue_images,
    upload_venue_image,
    upload_wizard_image,
    validate_venue_image_preview,
    set_primary_image,
    delete_venue_image,
    reorder_venue_image,
    reorder_venue_images,
    undo_image_action,
    manage_holiday_schedules,
    delete_holiday_schedule,
    validate_field_ajax,
    auto_save_progress,
    reorder_faqs,
    sync_contact_info,
    send_email_verification,
    verify_venue_email,
    manage_venue_visibility,
    venue_preview,
    update_information_freshness,
    edit_venue_basic_information,
    edit_venue_location,
)

# --- Admin management views ---
from .admin import (
    admin_venue_approval_dashboard,
    admin_venue_list,
    admin_pending_venues,
    admin_venue_detail,
    admin_venue_approval,
    admin_venue_changes_comparison,
    admin_category_list,
    admin_category_create,
    admin_category_edit,
    admin_category_delete,
    admin_category_toggle_status,
    admin_service_category_list,
    admin_service_category_create,
    admin_service_category_edit,
    admin_service_category_delete,
    admin_service_category_toggle_status,
    admin_flagged_venues,
)

__all__ = [
    'ServiceProviderRequiredMixin',
    'MAX_SERVICES_PER_VENUE',
    'MAX_FAQS_PER_VENUE',
    'home_view',
    'login_view',
    'venue_search',
    'location_autocomplete',
    'category_venues',
    'services_by_category',
    'services_by_tag',
    'get_location_data',
    'venue_detail',
    'service_detail',
    'flag_venue',
    'VenueCreateView',
    'VenueDeleteView',
    'venue_create',
    'venue_create_view',
    'venue_create_wizard_view',
    'venue_edit_wizard_view',
    'venue_edit',
    'venue_progress',
    'trigger_auto_approval_check',
    'venue_delete',
    'manage_services',
    'bulk_service_actions',
    'manage_faqs',
    'manage_operating_hours',
    'manage_amenities',
    'edit_faq',
    'delete_faq',
    'delete_amenity',
    'provider_venues',
    'provider_venue_detail',
    'change_venue_status',
    'ServiceCreateView',
    'ServiceUpdateView',
    'ServiceDeleteView',
    'service_create',
    'service_edit',
    'service_delete',
    'manage_venue_images',
    'upload_venue_image',
    'upload_wizard_image',
    'validate_venue_image_preview',
    'set_primary_image',
    'delete_venue_image',
    'reorder_venue_image',
    'reorder_venue_images',
    'undo_image_action',
    'manage_holiday_schedules',
    'delete_holiday_schedule',
    'validate_field_ajax',
    'auto_save_progress',
    'reorder_faqs',
    'sync_contact_info',
    'send_email_verification',
    'verify_venue_email',
    'manage_venue_visibility',
    'venue_preview',
    'update_information_freshness',
    'edit_venue_basic_information',
    'edit_venue_location',
    'admin_venue_approval_dashboard',
    'admin_venue_list',
    'admin_pending_venues',
    'admin_venue_detail',
    'admin_venue_approval',
    'admin_venue_changes_comparison',
    'admin_category_list',
    'admin_category_create',
    'admin_category_edit',
    'admin_category_delete',
    'admin_category_toggle_status',
    'admin_service_category_list',
    'admin_service_category_create',
    'admin_service_category_edit',
    'admin_service_category_delete',
    'admin_service_category_toggle_status',
    'admin_flagged_venues',
]
