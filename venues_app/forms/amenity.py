"""Forms for venue amenity management."""

# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import VenueAmenity


try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class VenueAmenityForm(forms.ModelForm):
    """Form for managing venue amenities."""

    class Meta:
        model = VenueAmenity
        fields = ['amenity_type']
        widgets = {
            'amenity_type': forms.Select(attrs={
                'class': 'form-control',
            }),
        }

    def clean_custom_name(self):
        """Validate custom name."""
        custom_name = self.cleaned_data.get('custom_name', '')
        if custom_name:
            custom_name = custom_name.strip()
        return sanitize_html(custom_name)
