# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
import re

# --- Local App Imports ---
from ..models import VenueFAQ


try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class VenueFAQForm(forms.ModelForm):
    """Enhanced form for creating and editing venue FAQs with validation and templates."""

    class Meta:
        model = VenueFAQ
        fields = ['question', 'answer']
        widgets = {
            'question': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your frequently asked question (minimum 10 characters)',
                'maxlength': 255,
                'data-character-count': 'true',
                'data-min-length': '10',
                'data-max-length': '255',
            }),
            'answer': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Provide a clear and helpful answer (minimum 20 characters, maximum 500 characters)',
                'maxlength': 500,
                'data-character-count': 'true',
                'data-min-length': '20',
                'data-max-length': '500',
            }),
        }
        help_texts = {
            'question': _('Enter a clear, specific question that customers commonly ask (10-255 characters)'),
            'answer': _('Provide a comprehensive answer that addresses the question thoroughly (20-500 characters)'),
        }

    def clean_question(self):
        """Enhanced question validation with minimum length and quality checks."""
        question = self.cleaned_data.get('question', '').strip()
        
        if not question:
            raise ValidationError(_('Question is required.'))
            
        question = sanitize_html(question)
        
        if len(question) < 10:
            raise ValidationError(_('Question must be at least 10 characters long.'))
            
        if len(question) > 255:
            raise ValidationError(_('Question cannot exceed 255 characters.'))
        
        # Quality checks
        if not question.endswith('?'):
            raise ValidationError(_('Question should end with a question mark (?).'))
            
        # Check for low-quality questions
        low_quality_patterns = [
            r'^test+\??$',
            r'^(.)\1{3,}',  # Repeated characters (4 or more)
            r'^[a-z\s]+\??$',  # Only lowercase letters and spaces
        ]
        
        for pattern in low_quality_patterns:
            if re.match(pattern, question, re.IGNORECASE):
                raise ValidationError(
                    _('Please provide a more meaningful and professional question.')
                )
        
        # Ensure minimum word count
        word_count = len(question.split())
        if word_count < 3:
            raise ValidationError(_('Question must contain at least 3 words.'))
            
        return question

    def clean_answer(self):
        """Enhanced answer validation with minimum length and quality checks."""
        answer = self.cleaned_data.get('answer', '').strip()
        
        if not answer:
            raise ValidationError(_('Answer is required.'))
            
        answer = sanitize_html(answer)
        
        if len(answer) < 20:
            raise ValidationError(_('Answer must be at least 20 characters long.'))
            
        if len(answer) > 500:
            raise ValidationError(_('Answer cannot exceed 500 characters.'))
        
        # Quality checks
        low_quality_patterns = [
            r'^test+$',
            r'^(.)\1{5,}',  # Repeated characters (6 or more)
            r'^[a-z\s]+$',  # Only lowercase letters and spaces
        ]
        
        for pattern in low_quality_patterns:
            if re.match(pattern, answer, re.IGNORECASE):
                raise ValidationError(
                    _('Please provide a more detailed and professional answer.')
                )
        
        # Ensure minimum word count
        word_count = len(answer.split())
        if word_count < 5:
            raise ValidationError(_('Answer must contain at least 5 words.'))
            
        return answer


