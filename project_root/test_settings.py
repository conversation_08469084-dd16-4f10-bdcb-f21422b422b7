from .settings import *


# --- Test database configuration - use in-memory SQLite for speed ---
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}


# --- Disable migrations for faster tests ---
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()



# --- Faster password hasher for tests ---
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]


# --- Disable logging during tests ---
LOGGING_CONFIG = None
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
}


# --- Email backend for testing ---
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'


# --- Media and Static Files Configuration for Testing ---
MEDIA_ROOT = BASE_DIR / 'test_media'


# --- Static Files Configuration for Testing ---
STATIC_ROOT = BASE_DIR / 'test_staticfiles'


# --- Cache Configuration for Testing ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'test-cache',
    }
}


# --- Disable debug toolbar during tests ---
if 'debug_toolbar' in INSTALLED_APPS:
    INSTALLED_APPS.remove('debug_toolbar')


# --- Disable whitenoise during tests ---
MIDDLEWARE = [m for m in MIDDLEWARE if 'whitenoise' not in m.lower()]


# --- Testing Flags ---
TESTING = True
DEBUG = False



# --- Security Settings for Testing ---
CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False



# --- Celery Configuration for Testing ---
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True


# --- File Storage Configuration for Testing ---
# Use STORAGES instead of deprecated DEFAULT_FILE_STORAGE
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}


# --- Platform Fee Rate for Testing ---
PLATFORM_FEE_RATE = 0.05


# --- Cache Timeouts for Testing ---
DASHBOARD_CACHE_TIMEOUT = 0
NOTIFICATION_CACHE_TIMEOUT = 0


NOTIFICATIONS_ASYNC = False