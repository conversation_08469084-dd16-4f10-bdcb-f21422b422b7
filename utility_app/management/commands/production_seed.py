"""
Production-safe seeding command for Render.com and other production environments.
This command bypasses the development-only restrictions in the main seed_data command.
"""

from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings
from django.db import transaction
from django.contrib.auth import get_user_model
import os

User = get_user_model()


class Command(BaseCommand):
    """Production-safe command to seed database with initial data."""

    help = 'Seed database with initial data (production-safe)'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--reset-db',
            action='store_true',
            help='Perform database reset before seeding',
        )
        parser.add_argument(
            '--minimal',
            action='store_true',
            help='Seed only essential data (categories, superuser)',
        )
        parser.add_argument(
            '--skip-confirmation',
            action='store_true',
            help='Skip confirmation prompts (for automated deployments)',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting production-safe data seeding...')
        )

        # Show environment info
        self.show_environment_info()

        # Confirmation for production
        if not settings.DEBUG and not options['skip_confirmation']:
            self.confirm_production_operation()

        # Perform database reset if requested
        if options['reset_db']:
            self.perform_database_reset()

        # Seed data based on options
        if options['minimal']:
            self.seed_minimal_data()
        else:
            self.seed_full_data()

        self.stdout.write(
            self.style.SUCCESS('✅ Production seeding completed successfully!')
        )

    def show_environment_info(self):
        """Show current environment information."""
        self.stdout.write('🔍 Environment Information:')
        self.stdout.write(f'   DEBUG: {settings.DEBUG}')
        self.stdout.write(f'   DATABASE: {"PostgreSQL" if "postgres" in str(settings.DATABASES["default"]["ENGINE"]) else "SQLite"}')
        self.stdout.write(f'   RENDER: {os.environ.get("RENDER", "false")}')
        self.stdout.write('')

    def confirm_production_operation(self):
        """Confirm operation in production environment."""
        self.stdout.write(
            self.style.WARNING('⚠️  You are about to seed data in a production environment!')
        )
        self.stdout.write('   This operation will add test data to your database.')
        
        response = input('Do you want to continue? (yes/no): ')
        if response.lower() != 'yes':
            raise CommandError('Operation cancelled by user.')

    def perform_database_reset(self):
        """Perform database reset (migrations only, no file deletion)."""
        self.stdout.write(
            self.style.WARNING('🔄 Performing database reset...')
        )

        # Apply migrations
        self.stdout.write('⚙️  Applying migrations...')
        call_command('migrate', verbosity=0)

        # Seed US cities data
        self.stdout.write('🏙️  Seeding US cities data...')
        call_command('seed_us_cities', clear=True, verbosity=0)

        self.stdout.write(
            self.style.SUCCESS('✅ Database reset completed!')
        )

    def seed_minimal_data(self):
        """Seed only essential data."""
        self.stdout.write('📋 Seeding minimal essential data...')

        # Create superuser if none exists
        self.create_superuser_if_needed()

        # Seed service categories
        self.stdout.write('🏷️ Seeding service categories...')
        call_command('seed_service_categories', verbosity=0)

        self.stdout.write('✅ Minimal data seeding completed!')

    def seed_full_data(self):
        """Seed full test data."""
        self.stdout.write('📋 Seeding full test data...')

        # Create superuser if none exists
        self.create_superuser_if_needed()

        # Seed service categories first
        self.stdout.write('🏷️ Seeding service categories...')
        call_command('seed_service_categories', verbosity=0)

        # Seed all app data using the main command with force flag
        self.stdout.write('🌱 Seeding all app data...')
        call_command('seed_data', force_production=True, verbosity=1)

        self.stdout.write('✅ Full data seeding completed!')

    def create_superuser_if_needed(self):
        """Create superuser if none exists."""
        if User.objects.filter(is_superuser=True).exists():
            self.stdout.write('👤 Superuser already exists, skipping creation.')
            return

        self.stdout.write('👤 Creating Django superuser...')
        try:
            with transaction.atomic():
                User.objects.create_superuser(
                    email='<EMAIL>',
                    password='123'
                )
            self.stdout.write('✅ Superuser created successfully!')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Could not create superuser: {e}')
            )
