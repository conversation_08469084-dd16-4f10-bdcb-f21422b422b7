# Production Commands for CozyWish

This document explains how to run database operations safely in production environments like Render.com.

## Problem Fixed

The original `make seed_data` and `make reset_db` commands were failing on Render.com because:

1. **Production Safety Check**: The commands detected `DEBUG=false` (production mode) and refused to run
2. **Missing Force Flag**: The commands didn't include the `--force-production` flag required for production

## Solution Overview

We've created production-safe alternatives that:

- ✅ Work in production environments (Render.com)
- ✅ Include proper safety confirmations
- ✅ Provide minimal and full seeding options
- ✅ Maintain development environment protections

## Available Commands

### 1. Makefile Commands (Local Development)

```bash
# Development (DEBUG=true)
make reset_db          # Reset database (development only)
make seed_data         # Seed test data (development only)

# Production (DEBUG=false)
make reset_db_production    # Reset database with confirmation
make seed_data_production   # Seed data with confirmation
make migrate_only          # Apply migrations only (safe)
make help                  # Show all available commands
```

### 2. Django Management Commands

```bash
# Production-safe seeding command
python manage.py production_seed --minimal              # Essential data only
python manage.py production_seed                        # Full test data
python manage.py production_seed --reset-db             # Reset and seed
python manage.py production_seed --skip-confirmation    # For automation

# Original command (with force flag)
python manage.py seed_data --force-production
```

### 3. Production Script (Render.com)

```bash
# Make executable (one time)
chmod +x scripts/production_commands.sh

# Available operations
./scripts/production_commands.sh status           # Show database status
./scripts/production_commands.sh migrate          # Apply migrations only
./scripts/production_commands.sh seed_minimal     # Essential data only
./scripts/production_commands.sh seed_full        # Full test data (with confirmation)
./scripts/production_commands.sh reset_and_seed   # Reset and seed (DANGEROUS)
```

## Usage on Render.com

### Option 1: Using the Production Script (Recommended)

1. **SSH into your Render service** or use the web shell
2. **Navigate to your app directory**
3. **Run the desired command**:

```bash
# Check current database status
./scripts/production_commands.sh status

# Add essential data (categories, superuser)
./scripts/production_commands.sh seed_minimal

# Add full test data (with confirmation)
./scripts/production_commands.sh seed_full
```

### Option 2: Direct Django Commands

```bash
# Essential data only
python manage.py production_seed --minimal --skip-confirmation

# Full test data
python manage.py production_seed --skip-confirmation

# Reset database and seed
python manage.py production_seed --reset-db --skip-confirmation
```

### Option 3: Original Commands with Force Flag

```bash
# Using the original command
python manage.py seed_data --force-production

# Service categories
python manage.py seed_service_categories
```

## Safety Features

### Development Protection
- Commands check for `DEBUG=false` or `RENDER=true` environment
- Prevents accidental production operations in development

### Production Confirmations
- Interactive confirmations for dangerous operations
- `--skip-confirmation` flag for automated deployments
- Clear warnings about data loss

### Minimal vs Full Seeding
- **Minimal**: Only essential data (categories, superuser)
- **Full**: Complete test data (users, venues, bookings, etc.)

## Automatic Deployment

The `build.sh` script now uses the production-safe command:

```bash
# On first deployment (empty database)
python manage.py production_seed --reset-db --skip-confirmation

# On subsequent deployments (existing data)
python manage.py production_seed --minimal --skip-confirmation
```

## Troubleshooting

### Command Not Found
```bash
# Make sure you're in the project directory
cd /opt/render/project/src

# Make script executable
chmod +x scripts/production_commands.sh
```

### Permission Denied
```bash
# Use python directly
python manage.py production_seed --minimal --skip-confirmation
```

### Database Connection Issues
```bash
# Check environment variables
echo $DATABASE_URL
echo $DEBUG

# Test database connection
python manage.py migrate --check
```

## Environment Variables

Ensure these are set in your Render.com environment:

```bash
DEBUG=false
DATABASE_URL=postgresql://...
SECRET_KEY=your-secret-key
```

## Next Steps

1. **Deploy the changes** to Render.com
2. **Test the commands** using the production script
3. **Seed your database** with the appropriate command
4. **Monitor the application** to ensure everything works correctly

The commands are now production-ready and will work reliably on Render.com! 🚀
