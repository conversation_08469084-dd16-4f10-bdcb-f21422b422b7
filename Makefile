.PHONY: run reset_db reset_db_production seed_data seed_data_production migrate_only help test_coverage pytest pytest_app pytest_coverage pytest_fast



# --- Start Django development server (accessible via LAN) ---
run:
	@echo "🚀 Starting Django development server at http://0.0.0.0:8000 ..."
	python manage.py runserver 0.0.0.0:8000



# --- Reset local SQLite database and clean cache files ---
reset_db:
	@echo "🔄  Starting full Django reset: database, cache, media files, and migrations..."
	@if [ "$$DEBUG" = "false" ] || [ "$$RENDER" = "true" ]; then \
		echo "🚨 Production environment detected. Use 'make reset_db_production' instead."; \
		echo "ℹ️  This prevents accidental database reset in production."; \
		exit 1; \
	fi
	@echo "🗑️  Deleting SQLite database file..."
	rm -f db.sqlite3 || true

	@echo "🗑️  Deleting all migration files (except __init__.py)..."
	find . -path "*/migrations/*.py" ! -name "__init__.py" -delete || true

	@echo "🧹  Cleaning migration cache files (*.pyc)..."
	find . -path "*/migrations/*.pyc" -delete || true

	@echo "🗑️  Removing all Python __pycache__ folders..."
	find . -name "__pycache__" -type d -exec rm -rf {} + || true

	@echo "🗑️  Deleting Python bytecode files (*.pyc, *.pyo)..."
	find . -name "*.pyc" -delete || true
	find . -name "*.pyo" -delete || true

	@echo "🖼️  Clearing all user-uploaded media files (media/*)..."
	rm -rf media/* || true

	@echo "🔧  Ensuring Django installation is intact..."
	pip uninstall -y Django && pip install Django==5.2.4

	@echo "⚙️  Creating fresh migrations..."
	python manage.py makemigrations

	@echo "⚙️  Applying migrations to rebuild the database schema..."
	python manage.py migrate

	@echo "🏙️  Seeding US cities data from CSV file..."
	python manage.py seed_us_cities --clear

	@echo "👤  Creating Django superuser (Email: <EMAIL>, Password: 123)..."
	python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); \
email='<EMAIL>'; password='123'; \
User.objects.create_superuser(email=email, password=password); \
print('Superuser created successfully!')"

	@echo "✅  Reset complete! Fresh database, no cached files, migrations recreated, superuser created, and US cities seeded."

# --- Reset database (Production Safe) ---
reset_db_production:
	@echo "🔄  Starting full Django reset in PRODUCTION MODE..."
	@echo "⚠️  WARNING: This will completely wipe your production database!"
	@echo "⚠️  All user data, bookings, payments, and content will be lost!"
	@read -p "Type 'RESET_PRODUCTION' to confirm: " confirm && [ "$$confirm" = "RESET_PRODUCTION" ] || exit 1

	@echo "⚙️  Creating fresh migrations..."
	python manage.py makemigrations

	@echo "⚙️  Applying migrations to rebuild the database schema..."
	python manage.py migrate

	@echo "🏙️  Seeding US cities data from CSV file..."
	python manage.py seed_us_cities --clear

	@echo "👤  Creating Django superuser (Email: <EMAIL>, Password: 123)..."
	python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); \
email='<EMAIL>'; password='123'; \
User.objects.create_superuser(email=email, password=password); \
print('Superuser created successfully!')"

	@echo "✅  Production reset complete! Fresh database, superuser created, and US cities seeded."



# --- Seed database with test data ---
seed_data:
	@echo "🌱 Seeding database with test data..."
	@if [ "$$DEBUG" = "false" ] || [ "$$RENDER" = "true" ]; then \
		echo "🚨 Production environment detected. Use 'make seed_data_production' instead."; \
		echo "ℹ️  This prevents accidental data seeding in production."; \
		exit 1; \
	fi
	python manage.py seed_data
	@echo "🏷️ Seeding service categories..."
	python manage.py seed_service_categories
	@echo "✅ Database seeding completed!"

# --- Seed database with test data (Production Safe) ---
seed_data_production:
	@echo "🌱 Seeding database with test data (PRODUCTION MODE)..."
	@echo "⚠️  WARNING: This will replace all data with test data!"
	@read -p "Are you sure you want to continue? (yes/no): " confirm && [ "$$confirm" = "yes" ] || exit 1
	python manage.py seed_data --force-production
	@echo "🏷️ Seeding service categories..."
	python manage.py seed_service_categories
	@echo "✅ Database seeding completed!"

# --- Apply migrations only (Production Safe) ---
migrate_only:
	@echo "⚙️  Applying database migrations..."
	python manage.py makemigrations
	python manage.py migrate
	@echo "✅ Migrations applied successfully!"

# --- Show help information ---
help:
	@echo "🚀 CozyWish Makefile Commands"
	@echo ""
	@echo "📋 Development Commands:"
	@echo "  make run              - Start Django development server"
	@echo "  make reset_db         - Reset database (development only)"
	@echo "  make seed_data        - Seed test data (development only)"
	@echo ""
	@echo "🏭 Production Commands:"
	@echo "  make reset_db_production   - Reset database in production (DANGEROUS)"
	@echo "  make seed_data_production  - Seed test data in production (DANGEROUS)"
	@echo "  make migrate_only          - Apply migrations only (safe)"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  make test_coverage    - Run Django tests with coverage"
	@echo "  make pytest          - Run all tests with pytest"
	@echo "  make pytest_fast     - Run pytest in fast mode"
	@echo ""
	@echo "ℹ️  Use 'make help' to see this message again"
	@echo ""



# --- Run Django tests with coverage reporting ---
test_coverage:
	@echo "📊 [1/1] Running Django tests with coverage..."
	coverage run --source='.' manage.py test
	@echo "📝 Coverage report:"
	coverage report
	@echo "✅ All tests with coverage completed!"



# ---Run all tests using pytest ---
pytest:
	@echo "🧪 [1/1] Running all tests with pytest..."
	pytest
	@echo "✅ All pytest tests completed!"



# --- Run tests for a specific app using pytest, Usage: make pytest_app APP=app_name ---
pytest_app:
	@echo "🧪 [1/2] Running tests for specific app with pytest..."
	@echo "ℹ️  Usage: make pytest_app APP=accounts_app"
	@if [ -z "$(APP)" ]; then \
		echo "❌ Please specify APP=<app_name> (e.g., make pytest_app APP=accounts_app)"; \
		exit 1; \
	fi
	pytest $(APP)/tests/ -v
	@echo "✅ Tests for app [$(APP)] completed!"



# --- Run pytest with coverage, optionally for a specific app, Usage: make pytest_coverage [APP=app_name] ---
pytest_coverage:
	@echo "📊 [1/2] Running pytest with coverage..."
	@if [ -z "$(APP)" ]; then \
		echo "🗂️  Running coverage for ALL apps..."; \
		pytest --cov=. --cov-report=term-missing --cov-report=html; \
		echo "📄 HTML coverage report generated at htmlcov/index.html"; \
	else \
		echo "🗂️  Running coverage for app: [$(APP)] ..."; \
		pytest $(APP)/tests/ --cov=$(APP) --cov-report=term-missing --cov-report=html; \
		echo "📄 HTML coverage report for $(APP) at htmlcov/index.html"; \
	fi
	@echo "✅ Pytest with coverage completed!"



# --- Run pytest quickly with minimal output (quiet mode) ---
pytest_fast:
	@echo "🏃 [1/1] Running pytest in fast mode (quiet output)..."
	pytest -q --tb=no
	@echo "✅ Fast pytest run completed!"








