/**
 * CozyWish Form Integration Test Suite
 * 
 * Tests for form validation patterns, backward compatibility,
 * accessibility compliance, and progressive enhancement.
 */

class FormIntegrationTest {
    constructor() {
        this.testResults = [];
        this.testsPassed = 0;
        this.testsFailed = 0;
        
        this.init();
    }
    
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.runTests());
        } else {
            this.runTests();
        }
    }
    
    async runTests() {
        console.log('🧪 Starting CozyWish Form Integration Tests...');
        
        // Test categories
        await this.testFormValidation();
        await this.testBackwardCompatibility();
        await this.testAccessibility();
        await this.testProgressiveEnhancement();
        await this.testFormWorkflows();
        
        this.displayResults();
    }
    
    // === FORM VALIDATION TESTS ===
    
    async testFormValidation() {
        console.log('📝 Testing Form Validation...');
        
        // Test email validation
        this.test('Email validation pattern', () => {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailPattern.test('<EMAIL>') && 
                   !emailPattern.test('invalid-email');
        });
        
        // Test password strength validation
        this.test('Password strength validation', () => {
            const password = 'StrongPass123!';
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasNumber = /\d/.test(password);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            const isLongEnough = password.length >= 8;
            
            return hasUpper && hasLower && hasNumber && hasSpecial && isLongEnough;
        });
        
        // Test phone number formatting
        this.test('Phone number formatting', () => {
            const phone = '1234567890';
            const formatted = this.formatPhoneNumber(phone);
            return formatted === '(*************';
        });
        
        // Test real-time validation attributes
        this.test('Real-time validation attributes', () => {
            const testField = document.createElement('input');
            testField.setAttribute('data-validation', 'email');
            testField.setAttribute('data-validation-realtime', 'true');
            
            return testField.getAttribute('data-validation') === 'email' &&
                   testField.getAttribute('data-validation-realtime') === 'true';
        });
        
        // Test CSRF token handling
        this.test('CSRF token handling', () => {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                             document.querySelector('meta[name=csrf-token]')?.content;
            return !!csrfToken;
        });
    }
    
    // === BACKWARD COMPATIBILITY TESTS ===
    
    async testBackwardCompatibility() {
        console.log('🔄 Testing Backward Compatibility...');
        
        // Test existing form classes
        this.test('Bootstrap form classes compatibility', () => {
            const testField = document.createElement('input');
            testField.className = 'form-control';
            
            // Should still work with existing Bootstrap classes
            return testField.classList.contains('form-control');
        });
        
        // Test existing validation feedback
        this.test('Existing validation feedback compatibility', () => {
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            
            return feedback.classList.contains('invalid-feedback');
        });
        
        // Test form submission without JavaScript
        this.test('Form submission without JavaScript', () => {
            // Forms should still submit normally without JavaScript enhancements
            const form = document.createElement('form');
            form.method = 'post';
            form.action = '/test/';
            
            return form.method === 'post' && form.action.includes('/test/');
        });
        
        // Test existing crispy forms compatibility
        this.test('Crispy forms compatibility', () => {
            // Should work with existing crispy form helpers
            const crispyField = document.createElement('div');
            crispyField.className = 'form-floating mb-3';
            
            return crispyField.classList.contains('form-floating') &&
                   crispyField.classList.contains('mb-3');
        });
    }
    
    // === ACCESSIBILITY TESTS ===
    
    async testAccessibility() {
        console.log('♿ Testing Accessibility...');
        
        // Test ARIA labels
        this.test('ARIA labels on form fields', () => {
            const field = document.createElement('input');
            field.setAttribute('aria-label', 'Email address');
            field.setAttribute('aria-required', 'true');
            
            return field.getAttribute('aria-label') === 'Email address' &&
                   field.getAttribute('aria-required') === 'true';
        });
        
        // Test keyboard navigation
        this.test('Keyboard navigation support', () => {
            const field = document.createElement('input');
            field.tabIndex = 0;
            
            return field.tabIndex === 0;
        });
        
        // Test screen reader compatibility
        this.test('Screen reader compatibility', () => {
            const feedback = document.createElement('div');
            feedback.setAttribute('role', 'alert');
            feedback.setAttribute('aria-live', 'polite');
            
            return feedback.getAttribute('role') === 'alert' &&
                   feedback.getAttribute('aria-live') === 'polite';
        });
        
        // Test high contrast mode support
        this.test('High contrast mode support', () => {
            // Check if CSS supports high contrast media queries
            return window.matchMedia && 
                   window.matchMedia('(prefers-contrast: high)') !== null;
        });
        
        // Test reduced motion support
        this.test('Reduced motion support', () => {
            return window.matchMedia && 
                   window.matchMedia('(prefers-reduced-motion: reduce)') !== null;
        });
    }
    
    // === PROGRESSIVE ENHANCEMENT TESTS ===
    
    async testProgressiveEnhancement() {
        console.log('🚀 Testing Progressive Enhancement...');
        
        // Test JavaScript availability detection
        this.test('JavaScript availability detection', () => {
            return document.documentElement.classList.contains('js-enhanced') ||
                   document.documentElement.classList.contains('no-js');
        });
        
        // Test feature detection
        this.test('Feature detection', () => {
            const hasIntersectionObserver = 'IntersectionObserver' in window;
            const hasRequestIdleCallback = 'requestIdleCallback' in window;
            const hasValidityState = 'ValidityState' in window;
            
            // At least some modern features should be available
            return hasIntersectionObserver || hasRequestIdleCallback || hasValidityState;
        });
        
        // Test graceful degradation
        this.test('Graceful degradation', () => {
            // Forms should work without JavaScript enhancements
            const form = document.createElement('form');
            const field = document.createElement('input');
            field.type = 'email';
            field.required = true;
            
            form.appendChild(field);
            
            return field.checkValidity !== undefined; // Browser validation available
        });
        
        // Test enhancement layering
        this.test('Enhancement layering', () => {
            const field = document.createElement('input');
            field.className = 'form-control'; // Base class
            field.setAttribute('data-validation', 'email'); // Enhancement
            
            return field.classList.contains('form-control') &&
                   field.getAttribute('data-validation') === 'email';
        });
    }
    
    // === FORM WORKFLOW TESTS ===
    
    async testFormWorkflows() {
        console.log('🔄 Testing Form Workflows...');
        
        // Test signup form workflow
        this.test('Signup form workflow', () => {
            const signupForm = document.querySelector('form[action*="signup"]');
            if (!signupForm) return true; // Skip if not on signup page
            
            const emailField = signupForm.querySelector('[name="email"]');
            const passwordField = signupForm.querySelector('[name="password1"]');
            
            return emailField && passwordField;
        });
        
        // Test login form workflow
        this.test('Login form workflow', () => {
            const loginForm = document.querySelector('form[action*="login"]');
            if (!loginForm) return true; // Skip if not on login page
            
            const emailField = loginForm.querySelector('[name="login"]');
            const passwordField = loginForm.querySelector('[name="password"]');
            
            return emailField && passwordField;
        });
        
        // Test profile form workflow
        this.test('Profile form workflow', () => {
            const profileForm = document.querySelector('form[action*="profile"]');
            if (!profileForm) return true; // Skip if not on profile page
            
            return profileForm.querySelector('input, textarea, select') !== null;
        });
        
        // Test venue form workflow
        this.test('Venue form workflow', () => {
            const venueForm = document.querySelector('form[action*="venue"]');
            if (!venueForm) return true; // Skip if not on venue page
            
            return venueForm.querySelector('input, textarea, select') !== null;
        });
        
        // Test form validation integration
        this.test('Form validation integration', () => {
            const forms = document.querySelectorAll('form');
            let hasValidation = false;
            
            forms.forEach(form => {
                if (form.getAttribute('data-validation') === 'enhanced' ||
                    form.querySelector('[data-validation]')) {
                    hasValidation = true;
                }
            });
            
            return forms.length === 0 || hasValidation; // Pass if no forms or has validation
        });
    }
    
    // === UTILITY METHODS ===
    
    test(name, testFunction) {
        try {
            const result = testFunction();
            if (result) {
                this.testsPassed++;
                this.testResults.push({ name, status: 'PASS', error: null });
                console.log(`✅ ${name}`);
            } else {
                this.testsFailed++;
                this.testResults.push({ name, status: 'FAIL', error: 'Test returned false' });
                console.log(`❌ ${name}`);
            }
        } catch (error) {
            this.testsFailed++;
            this.testResults.push({ name, status: 'ERROR', error: error.message });
            console.log(`💥 ${name}: ${error.message}`);
        }
    }
    
    formatPhoneNumber(phone) {
        const digitsOnly = phone.replace(/\D/g, '');
        if (digitsOnly.length === 10) {
            return `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`;
        }
        return phone;
    }
    
    displayResults() {
        const total = this.testsPassed + this.testsFailed;
        const passRate = total > 0 ? Math.round((this.testsPassed / total) * 100) : 0;
        
        console.log('\n📊 Test Results Summary:');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${this.testsPassed}`);
        console.log(`Failed: ${this.testsFailed}`);
        console.log(`Pass Rate: ${passRate}%`);
        
        if (this.testsFailed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(result => result.status !== 'PASS')
                .forEach(result => {
                    console.log(`  - ${result.name}: ${result.error || 'Failed'}`);
                });
        }
        
        // Create visual test results if in development
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            this.createTestResultsDisplay(total, passRate);
        }
    }
    
    createTestResultsDisplay(total, passRate) {
        // Only show in development environment
        const resultsDiv = document.createElement('div');
        resultsDiv.id = 'form-test-results';
        resultsDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            border: 2px solid ${passRate === 100 ? '#059669' : '#dc2626'};
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
        `;
        
        resultsDiv.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">
                🧪 Form Tests: ${passRate}% (${this.testsPassed}/${total})
            </div>
            <div style="font-size: 10px; color: #666;">
                Click to dismiss
            </div>
        `;
        
        resultsDiv.addEventListener('click', () => resultsDiv.remove());
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (resultsDiv.parentNode) {
                resultsDiv.remove();
            }
        }, 10000);
        
        document.body.appendChild(resultsDiv);
    }
}

// Run tests only in development or when explicitly requested
if (window.location.search.includes('test=forms') || 
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1') {
    new FormIntegrationTest();
}

// Export for manual testing
window.FormIntegrationTest = FormIntegrationTest;
