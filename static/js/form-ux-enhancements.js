/**
 * CozyWish Form UX Enhancements
 * 
 * Modern UX patterns including:
 * - Progressive form disclosure
 * - Smart form defaults
 * - Auto-save for long forms
 * - Form step indicators
 * - Contextual help and tooltips
 * - Enhanced form submission with loading states
 */

class FormUXEnhancements {
    constructor() {
        this.autoSaveTimers = new Map();
        this.progressiveDisclosures = new Map();
        this.stepIndicators = new Map();
        this.tooltips = new Map();
        this.smartDefaults = new Map();
        
        this.init();
    }
    
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEnhancements());
        } else {
            this.setupEnhancements();
        }
    }
    
    setupEnhancements() {
        this.setupProgressiveDisclosure();
        this.setupSmartDefaults();
        this.setupAutoSave();
        this.setupStepIndicators();
        this.setupContextualHelp();
        this.setupEnhancedSubmission();
        this.setupProfileCompletion();
    }
    
    // === PROGRESSIVE FORM DISCLOSURE ===
    
    setupProgressiveDisclosure() {
        const disclosureTriggers = document.querySelectorAll('[data-disclosure-trigger]');
        
        disclosureTriggers.forEach(trigger => {
            this.setupDisclosureTrigger(trigger);
        });
    }
    
    setupDisclosureTrigger(trigger) {
        const targetSelector = trigger.getAttribute('data-disclosure-target');
        const triggerValue = trigger.getAttribute('data-disclosure-value');
        const targets = document.querySelectorAll(targetSelector);
        
        if (!targets.length) return;
        
        const disclosure = {
            trigger,
            targets: Array.from(targets),
            triggerValue,
            isVisible: false
        };
        
        this.progressiveDisclosures.set(trigger, disclosure);
        
        // Setup event listeners
        if (trigger.type === 'checkbox' || trigger.type === 'radio') {
            trigger.addEventListener('change', () => this.handleDisclosureChange(disclosure));
        } else if (trigger.tagName === 'SELECT') {
            trigger.addEventListener('change', () => this.handleDisclosureChange(disclosure));
        } else {
            trigger.addEventListener('input', () => this.handleDisclosureChange(disclosure));
        }
        
        // Initial state
        this.handleDisclosureChange(disclosure);
    }
    
    handleDisclosureChange(disclosure) {
        const { trigger, targets, triggerValue } = disclosure;
        let shouldShow = false;
        
        if (trigger.type === 'checkbox') {
            shouldShow = trigger.checked;
        } else if (trigger.type === 'radio') {
            shouldShow = trigger.checked && trigger.value === triggerValue;
        } else if (trigger.tagName === 'SELECT') {
            shouldShow = trigger.value === triggerValue;
        } else {
            shouldShow = trigger.value === triggerValue;
        }
        
        targets.forEach(target => {
            if (shouldShow) {
                this.showDisclosureTarget(target);
            } else {
                this.hideDisclosureTarget(target);
            }
        });
        
        disclosure.isVisible = shouldShow;
    }
    
    showDisclosureTarget(target) {
        target.style.display = '';
        target.classList.add('disclosure-visible');
        target.classList.remove('disclosure-hidden');
        
        // Enable form fields inside
        const fields = target.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.disabled = false;
            if (field.hasAttribute('data-required-when-visible')) {
                field.required = true;
            }
        });
        
        // Animate in
        target.style.opacity = '0';
        target.style.transform = 'translateY(-10px)';
        
        requestAnimationFrame(() => {
            target.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            target.style.opacity = '1';
            target.style.transform = 'translateY(0)';
        });
    }
    
    hideDisclosureTarget(target) {
        target.classList.add('disclosure-hidden');
        target.classList.remove('disclosure-visible');
        
        // Disable form fields inside
        const fields = target.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.disabled = true;
            field.required = false;
        });
        
        // Animate out
        target.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        target.style.opacity = '0';
        target.style.transform = 'translateY(-10px)';
        
        setTimeout(() => {
            target.style.display = 'none';
        }, 300);
    }
    
    // === SMART FORM DEFAULTS ===
    
    setupSmartDefaults() {
        this.setupLocationDefaults();
        this.setupBusinessDefaults();
        this.setupContactDefaults();
    }
    
    setupLocationDefaults() {
        // Auto-populate city/state based on ZIP code
        const zipFields = document.querySelectorAll('[data-smart-default="zip"]');
        
        zipFields.forEach(zipField => {
            zipField.addEventListener('blur', () => this.handleZipCodeDefault(zipField));
        });
    }
    
    async handleZipCodeDefault(zipField) {
        const zipCode = zipField.value.trim();
        if (zipCode.length !== 5 || !/^\d{5}$/.test(zipCode)) return;
        
        const form = zipField.closest('form');
        const cityField = form.querySelector('[name="city"], [data-smart-target="city"]');
        const stateField = form.querySelector('[name="state"], [data-smart-target="state"]');
        
        if (!cityField || !stateField) return;
        
        try {
            // This would typically call an API to get location data
            // For now, we'll use a simple lookup
            const locationData = await this.lookupZipCode(zipCode);
            
            if (locationData) {
                if (!cityField.value) {
                    cityField.value = locationData.city;
                    this.showSmartDefaultFeedback(cityField, 'Auto-filled from ZIP code');
                }
                
                if (!stateField.value) {
                    stateField.value = locationData.state;
                    this.showSmartDefaultFeedback(stateField, 'Auto-filled from ZIP code');
                }
            }
        } catch (error) {
            console.warn('Failed to lookup ZIP code:', error);
        }
    }
    
    async lookupZipCode(zipCode) {
        // Simplified ZIP code lookup - in production, this would call a real API
        const zipData = {
            '10001': { city: 'New York', state: 'NY' },
            '90210': { city: 'Beverly Hills', state: 'CA' },
            '60601': { city: 'Chicago', state: 'IL' },
            '33101': { city: 'Miami', state: 'FL' },
            '78701': { city: 'Austin', state: 'TX' }
        };
        
        return zipData[zipCode] || null;
    }
    
    setupBusinessDefaults() {
        // Auto-populate business email based on business name
        const businessNameFields = document.querySelectorAll('[data-smart-default="business-name"]');
        
        businessNameFields.forEach(nameField => {
            nameField.addEventListener('blur', () => this.handleBusinessNameDefault(nameField));
        });
    }
    
    handleBusinessNameDefault(nameField) {
        const businessName = nameField.value.trim();
        if (!businessName) return;
        
        const form = nameField.closest('form');
        const emailField = form.querySelector('[data-smart-target="business-email"]');
        
        if (!emailField || emailField.value) return;
        
        // Generate suggested email
        const domain = this.generateBusinessDomain(businessName);
        const suggestedEmail = `info@${domain}`;
        
        this.showSmartDefaultSuggestion(emailField, suggestedEmail, 'Suggested business email');
    }
    
    generateBusinessDomain(businessName) {
        return businessName
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '')
            .substring(0, 20) + '.com';
    }
    
    setupContactDefaults() {
        // Auto-format and suggest contact information
        const phoneFields = document.querySelectorAll('[data-smart-default="phone"]');
        
        phoneFields.forEach(phoneField => {
            phoneField.addEventListener('input', () => this.handlePhoneDefault(phoneField));
        });
    }
    
    handlePhoneDefault(phoneField) {
        // This is handled by the phone formatting in the validation framework
        // Additional smart defaults could be added here
    }
    
    showSmartDefaultFeedback(field, message) {
        const container = field.closest('.form-group, .mb-3, .field-container');
        if (!container) return;
        
        let feedback = container.querySelector('.smart-default-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'smart-default-feedback text-success small mt-1';
            container.appendChild(feedback);
        }
        
        feedback.innerHTML = `<i class="fas fa-magic"></i> ${message}`;
        
        // Fade out after 3 seconds
        setTimeout(() => {
            feedback.style.opacity = '0';
            setTimeout(() => feedback.remove(), 300);
        }, 3000);
    }
    
    showSmartDefaultSuggestion(field, suggestion, message) {
        const container = field.closest('.form-group, .mb-3, .field-container');
        if (!container) return;
        
        const suggestionEl = document.createElement('div');
        suggestionEl.className = 'smart-default-suggestion alert alert-info alert-dismissible fade show mt-2';
        suggestionEl.innerHTML = `
            <small>
                <i class="fas fa-lightbulb"></i> ${message}: 
                <strong>${suggestion}</strong>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2" data-action="accept">
                    Use This
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </small>
        `;
        
        container.appendChild(suggestionEl);
        
        // Handle accept button
        suggestionEl.querySelector('[data-action="accept"]').addEventListener('click', () => {
            field.value = suggestion;
            field.dispatchEvent(new Event('input', { bubbles: true }));
            suggestionEl.remove();
        });
    }
    
    // === AUTO-SAVE FOR LONG FORMS ===
    
    setupAutoSave() {
        const autoSaveForms = document.querySelectorAll('[data-auto-save="true"]');
        
        autoSaveForms.forEach(form => {
            this.setupFormAutoSave(form);
        });
    }
    
    setupFormAutoSave(form) {
        const interval = parseInt(form.getAttribute('data-auto-save-interval')) || 30000; // 30 seconds default
        const fields = form.querySelectorAll('input, textarea, select');
        
        fields.forEach(field => {
            field.addEventListener('input', () => {
                this.scheduleAutoSave(form, interval);
            });
        });
        
        // Create auto-save indicator
        this.createAutoSaveIndicator();
    }
    
    scheduleAutoSave(form, interval) {
        // Clear existing timer
        if (this.autoSaveTimers.has(form)) {
            clearTimeout(this.autoSaveTimers.get(form));
        }
        
        // Schedule new auto-save
        const timer = setTimeout(() => {
            this.performAutoSave(form);
        }, interval);
        
        this.autoSaveTimers.set(form, timer);
    }
    
    async performAutoSave(form) {
        try {
            this.showAutoSaveIndicator('Saving...');
            
            const formData = new FormData(form);
            formData.append('auto_save', 'true');
            
            const response = await fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                this.showAutoSaveIndicator('Saved', 'success');
            } else {
                this.showAutoSaveIndicator('Save failed', 'error');
            }
        } catch (error) {
            console.warn('Auto-save failed:', error);
            this.showAutoSaveIndicator('Save failed', 'error');
        }
    }
    
    createAutoSaveIndicator() {
        if (document.getElementById('auto-save-indicator')) return;
        
        const indicator = document.createElement('div');
        indicator.id = 'auto-save-indicator';
        indicator.className = 'auto-save-indicator';
        document.body.appendChild(indicator);
    }
    
    showAutoSaveIndicator(message, type = 'info') {
        const indicator = document.getElementById('auto-save-indicator');
        if (!indicator) return;
        
        indicator.textContent = message;
        indicator.className = `auto-save-indicator show ${type}`;
        
        setTimeout(() => {
            indicator.classList.remove('show');
        }, 2000);
    }
    
    // === STEP INDICATORS ===
    
    setupStepIndicators() {
        const stepForms = document.querySelectorAll('[data-step-form="true"]');
        
        stepForms.forEach(form => {
            this.setupFormStepIndicator(form);
        });
    }
    
    setupFormStepIndicator(form) {
        const steps = form.querySelectorAll('[data-step]');
        if (steps.length < 2) return;
        
        const indicator = this.createStepIndicator(steps);
        form.insertBefore(indicator, form.firstChild);
        
        this.stepIndicators.set(form, {
            indicator,
            steps: Array.from(steps),
            currentStep: 0
        });
        
        this.updateStepIndicator(form);
    }
    
    createStepIndicator(steps) {
        const indicator = document.createElement('ol');
        indicator.className = 'form-steps';
        
        steps.forEach((step, index) => {
            const stepTitle = step.getAttribute('data-step-title') || `Step ${index + 1}`;
            const li = document.createElement('li');
            li.className = 'form-step';
            li.innerHTML = `
                <div class="form-step-indicator">${index + 1}</div>
                <div class="form-step-label">${stepTitle}</div>
            `;
            indicator.appendChild(li);
        });
        
        return indicator;
    }
    
    updateStepIndicator(form) {
        const stepData = this.stepIndicators.get(form);
        if (!stepData) return;
        
        const { indicator, steps, currentStep } = stepData;
        const stepElements = indicator.querySelectorAll('.form-step');
        
        stepElements.forEach((stepEl, index) => {
            stepEl.classList.remove('active', 'completed');
            
            if (index < currentStep) {
                stepEl.classList.add('completed');
            } else if (index === currentStep) {
                stepEl.classList.add('active');
            }
        });
    }
    
    // === CONTEXTUAL HELP AND TOOLTIPS ===
    
    setupContextualHelp() {
        const helpTriggers = document.querySelectorAll('[data-help]');
        
        helpTriggers.forEach(trigger => {
            this.setupHelpTrigger(trigger);
        });
        
        // Setup field-specific help
        this.setupFieldHelp();
    }
    
    setupHelpTrigger(trigger) {
        const helpText = trigger.getAttribute('data-help');
        const helpContainer = this.createHelpTooltip(helpText);
        
        trigger.parentElement.appendChild(helpContainer);
        this.tooltips.set(trigger, helpContainer);
        
        // Show/hide events
        trigger.addEventListener('mouseenter', () => this.showTooltip(helpContainer));
        trigger.addEventListener('mouseleave', () => this.hideTooltip(helpContainer));
        trigger.addEventListener('focus', () => this.showTooltip(helpContainer));
        trigger.addEventListener('blur', () => this.hideTooltip(helpContainer));
    }
    
    createHelpTooltip(text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'field-help-content';
        tooltip.textContent = text;
        tooltip.setAttribute('role', 'tooltip');
        return tooltip;
    }
    
    setupFieldHelp() {
        // Add help icons to complex fields
        const complexFields = document.querySelectorAll('[data-validation="password"], [data-validation="phone"]');
        
        complexFields.forEach(field => {
            this.addFieldHelpIcon(field);
        });
    }
    
    addFieldHelpIcon(field) {
        const label = field.labels?.[0] || field.closest('.form-group, .mb-3')?.querySelector('label');
        if (!label) return;
        
        const helpIcon = document.createElement('span');
        helpIcon.className = 'field-help';
        helpIcon.innerHTML = `
            <button type="button" class="field-help-trigger" aria-label="Help">?</button>
            <div class="field-help-content" role="tooltip">
                ${this.getFieldHelpText(field)}
            </div>
        `;
        
        label.appendChild(helpIcon);
    }
    
    getFieldHelpText(field) {
        const validationType = field.getAttribute('data-validation');
        
        const helpTexts = {
            password: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character.',
            phone: 'Enter your phone number. We\'ll format it automatically.',
            email: 'We\'ll never share your email address with anyone else.'
        };
        
        return helpTexts[validationType] || 'Additional information about this field.';
    }
    
    showTooltip(tooltip) {
        tooltip.style.opacity = '1';
        tooltip.style.visibility = 'visible';
    }
    
    hideTooltip(tooltip) {
        tooltip.style.opacity = '0';
        tooltip.style.visibility = 'hidden';
    }
    
    // === ENHANCED FORM SUBMISSION ===
    
    setupEnhancedSubmission() {
        const enhancedForms = document.querySelectorAll('[data-enhanced-submit="true"]');
        
        enhancedForms.forEach(form => {
            form.addEventListener('submit', (e) => this.handleEnhancedSubmit(e));
        });
    }
    
    handleEnhancedSubmit(e) {
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        
        if (submitButton) {
            this.setSubmitButtonLoading(submitButton, true);
            
            // Set timeout to reset button if form submission fails
            setTimeout(() => {
                this.setSubmitButtonLoading(submitButton, false);
            }, 10000);
        }
    }
    
    setSubmitButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.setAttribute('data-original-text', button.textContent);
            
            const spinner = document.createElement('span');
            spinner.className = 'spinner-border spinner-border-sm me-2';
            spinner.setAttribute('role', 'status');
            spinner.setAttribute('aria-hidden', 'true');
            
            button.innerHTML = '';
            button.appendChild(spinner);
            button.appendChild(document.createTextNode('Processing...'));
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text') || 'Submit';
            button.textContent = originalText;
        }
    }
    
    // === PROFILE COMPLETION ===
    
    setupProfileCompletion() {
        const completionIndicators = document.querySelectorAll('[data-profile-completion="true"]');
        
        completionIndicators.forEach(indicator => {
            this.setupProfileCompletionIndicator(indicator);
        });
    }
    
    setupProfileCompletionIndicator(indicator) {
        const form = indicator.closest('form') || document.querySelector('form');
        if (!form) return;
        
        const fields = form.querySelectorAll('[data-completion-field="true"]');
        
        fields.forEach(field => {
            field.addEventListener('input', () => {
                this.updateProfileCompletion(indicator, fields);
            });
        });
        
        // Initial calculation
        this.updateProfileCompletion(indicator, fields);
    }
    
    updateProfileCompletion(indicator, fields) {
        let totalWeight = 0;
        let completedWeight = 0;
        
        fields.forEach(field => {
            const weight = parseInt(field.getAttribute('data-completion-weight')) || 5;
            totalWeight += weight;
            
            if (field.value.trim()) {
                completedWeight += weight;
            }
        });
        
        const percentage = totalWeight > 0 ? Math.round((completedWeight / totalWeight) * 100) : 0;
        
        const percentageEl = indicator.querySelector('.completion-percentage');
        const fillEl = indicator.querySelector('.completion-fill');
        const messageEl = indicator.querySelector('.completion-message');
        
        if (percentageEl) percentageEl.textContent = `${percentage}%`;
        if (fillEl) fillEl.style.width = `${percentage}%`;
        
        if (messageEl) {
            if (percentage === 100) {
                messageEl.textContent = 'Your profile is complete!';
            } else if (percentage >= 75) {
                messageEl.textContent = 'Almost there! Just a few more details.';
            } else if (percentage >= 50) {
                messageEl.textContent = 'You\'re halfway there! Keep going.';
            } else {
                messageEl.textContent = 'Complete your profile to get the most out of CozyWish.';
            }
        }
    }
    
    // === UTILITY METHODS ===
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name=csrf-token]')?.content ||
               '';
    }
}

// Initialize the UX enhancements
const formUXEnhancements = new FormUXEnhancements();

// Export for use in other scripts
window.FormUXEnhancements = FormUXEnhancements;
window.formUXEnhancements = formUXEnhancements;
