/**
 * CozyWish Form Components
 * 
 * Enhanced form components with:
 * - Form validation without page reload
 * - Dynamic field visibility based on selections
 * - Auto-complete for common fields
 * - File upload with progress indicators
 * - Modern ES6+ JavaScript patterns
 */

class FormComponents {
    constructor() {
        this.ajaxForms = new Map();
        this.dynamicFields = new Map();
        this.autoCompletes = new Map();
        this.fileUploads = new Map();
        this.debounceTimers = new Map();
        
        this.init();
    }
    
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupComponents());
        } else {
            this.setupComponents();
        }
    }
    
    setupComponents() {
        this.setupAjaxForms();
        this.setupDynamicFields();
        this.setupAutoComplete();
        this.setupFileUploads();
        this.setupModernPatterns();
    }
    
    // === AJAX FORM VALIDATION ===
    
    setupAjaxForms() {
        const ajaxForms = document.querySelectorAll('[data-ajax-validation="true"]');
        
        ajaxForms.forEach(form => {
            this.enhanceAjaxForm(form);
        });
    }
    
    enhanceAjaxForm(form) {
        const config = {
            form,
            endpoint: form.getAttribute('data-validation-endpoint') || form.action,
            method: form.getAttribute('data-validation-method') || 'POST',
            realTime: form.getAttribute('data-realtime-validation') === 'true'
        };
        
        this.ajaxForms.set(form, config);
        
        // Setup form submission
        form.addEventListener('submit', (e) => this.handleAjaxSubmit(e, config));
        
        // Setup real-time validation if enabled
        if (config.realTime) {
            const fields = form.querySelectorAll('input, textarea, select');
            fields.forEach(field => {
                field.addEventListener('blur', () => {
                    this.validateFieldAjax(field, config);
                });
            });
        }
    }
    
    async handleAjaxSubmit(e, config) {
        e.preventDefault();
        
        const { form, endpoint, method } = config;
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        
        try {
            this.setFormLoading(form, true);
            
            const formData = new FormData(form);
            formData.append('ajax_validation', 'true');
            
            const response = await fetch(endpoint, {
                method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.handleAjaxSuccess(form, result);
            } else {
                this.handleAjaxErrors(form, result.errors || {});
            }
        } catch (error) {
            console.error('Ajax form submission failed:', error);
            this.handleAjaxError(form, 'An error occurred. Please try again.');
        } finally {
            this.setFormLoading(form, false);
        }
    }
    
    async validateFieldAjax(field, config) {
        const fieldName = field.name;
        if (!fieldName) return;
        
        // Debounce validation requests
        this.debounce(`validate_${fieldName}`, async () => {
            try {
                const formData = new FormData();
                formData.append(fieldName, field.value);
                formData.append('validate_field', fieldName);
                formData.append('ajax_validation', 'true');
                
                const response = await fetch(config.endpoint, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': this.getCSRFToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.field_errors && result.field_errors[fieldName]) {
                    this.showFieldError(field, result.field_errors[fieldName]);
                } else {
                    this.clearFieldError(field);
                }
            } catch (error) {
                console.warn('Field validation failed:', error);
            }
        }, 500);
    }
    
    handleAjaxSuccess(form, result) {
        // Clear all errors
        this.clearFormErrors(form);
        
        // Show success message
        this.showFormMessage(form, result.message || 'Form submitted successfully!', 'success');
        
        // Handle redirect
        if (result.redirect) {
            setTimeout(() => {
                window.location.href = result.redirect;
            }, 1500);
        }
        
        // Reset form if specified
        if (result.reset_form) {
            form.reset();
        }
    }
    
    handleAjaxErrors(form, errors) {
        this.clearFormErrors(form);
        
        // Show field-specific errors
        Object.entries(errors).forEach(([fieldName, errorMessages]) => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                const errorText = Array.isArray(errorMessages) ? errorMessages.join(', ') : errorMessages;
                this.showFieldError(field, errorText);
            }
        });
        
        // Show general error message
        this.showFormMessage(form, 'Please correct the errors below.', 'error');
        
        // Focus first error field
        const firstError = form.querySelector('.is-invalid');
        if (firstError) {
            firstError.focus();
        }
    }
    
    handleAjaxError(form, message) {
        this.showFormMessage(form, message, 'error');
    }
    
    // === DYNAMIC FIELD VISIBILITY ===
    
    setupDynamicFields() {
        const dynamicTriggers = document.querySelectorAll('[data-dynamic-fields]');
        
        dynamicTriggers.forEach(trigger => {
            this.setupDynamicTrigger(trigger);
        });
    }
    
    setupDynamicTrigger(trigger) {
        const config = JSON.parse(trigger.getAttribute('data-dynamic-fields'));
        
        this.dynamicFields.set(trigger, config);
        
        // Setup event listeners
        if (trigger.type === 'checkbox' || trigger.type === 'radio') {
            trigger.addEventListener('change', () => this.handleDynamicChange(trigger, config));
        } else {
            trigger.addEventListener('input', () => this.handleDynamicChange(trigger, config));
        }
        
        // Initial state
        this.handleDynamicChange(trigger, config);
    }
    
    handleDynamicChange(trigger, config) {
        const currentValue = this.getTriggerValue(trigger);
        
        Object.entries(config).forEach(([value, actions]) => {
            const shouldApply = this.shouldApplyActions(currentValue, value, trigger);
            
            actions.forEach(action => {
                this.applyDynamicAction(action, shouldApply);
            });
        });
    }
    
    getTriggerValue(trigger) {
        if (trigger.type === 'checkbox') {
            return trigger.checked ? trigger.value : '';
        } else if (trigger.type === 'radio') {
            const form = trigger.closest('form');
            const checked = form.querySelector(`input[name="${trigger.name}"]:checked`);
            return checked ? checked.value : '';
        } else {
            return trigger.value;
        }
    }
    
    shouldApplyActions(currentValue, targetValue, trigger) {
        if (trigger.type === 'checkbox') {
            return targetValue === 'checked' ? !!currentValue : !currentValue;
        }
        return currentValue === targetValue;
    }
    
    applyDynamicAction(action, shouldApply) {
        const { type, target, value } = action;
        const elements = document.querySelectorAll(target);
        
        elements.forEach(element => {
            switch (type) {
                case 'show':
                    element.style.display = shouldApply ? '' : 'none';
                    break;
                case 'hide':
                    element.style.display = shouldApply ? 'none' : '';
                    break;
                case 'enable':
                    element.disabled = !shouldApply;
                    break;
                case 'disable':
                    element.disabled = shouldApply;
                    break;
                case 'require':
                    element.required = shouldApply;
                    break;
                case 'setValue':
                    if (shouldApply) element.value = value;
                    break;
                case 'addClass':
                    if (shouldApply) {
                        element.classList.add(value);
                    } else {
                        element.classList.remove(value);
                    }
                    break;
            }
        });
    }
    
    // === AUTO-COMPLETE FUNCTIONALITY ===
    
    setupAutoComplete() {
        const autoCompleteFields = document.querySelectorAll('[data-autocomplete]');
        
        autoCompleteFields.forEach(field => {
            this.setupAutoCompleteField(field);
        });
    }
    
    setupAutoCompleteField(field) {
        const config = {
            source: field.getAttribute('data-autocomplete'),
            minLength: parseInt(field.getAttribute('data-autocomplete-min-length')) || 2,
            maxResults: parseInt(field.getAttribute('data-autocomplete-max-results')) || 10,
            delay: parseInt(field.getAttribute('data-autocomplete-delay')) || 300
        };
        
        const dropdown = this.createAutoCompleteDropdown(field);
        this.autoCompletes.set(field, { config, dropdown, isOpen: false });
        
        // Setup event listeners
        field.addEventListener('input', (e) => {
            this.handleAutoCompleteInput(field, e.target.value);
        });
        
        field.addEventListener('keydown', (e) => {
            this.handleAutoCompleteKeydown(field, e);
        });
        
        field.addEventListener('blur', () => {
            setTimeout(() => this.hideAutoComplete(field), 150);
        });
    }
    
    createAutoCompleteDropdown(field) {
        const dropdown = document.createElement('div');
        dropdown.className = 'autocomplete-dropdown';
        dropdown.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        `;
        
        const container = field.closest('.form-group, .mb-3, .field-container') || field.parentElement;
        container.style.position = 'relative';
        container.appendChild(dropdown);
        
        return dropdown;
    }
    
    handleAutoCompleteInput(field, value) {
        const { config } = this.autoCompletes.get(field);
        
        if (value.length < config.minLength) {
            this.hideAutoComplete(field);
            return;
        }
        
        this.debounce(`autocomplete_${field.name}`, async () => {
            await this.fetchAutoCompleteResults(field, value);
        }, config.delay);
    }
    
    async fetchAutoCompleteResults(field, query) {
        const { config, dropdown } = this.autoCompletes.get(field);
        
        try {
            const url = new URL(config.source, window.location.origin);
            url.searchParams.set('q', query);
            url.searchParams.set('limit', config.maxResults);
            
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const results = await response.json();
            this.displayAutoCompleteResults(field, results);
        } catch (error) {
            console.warn('Autocomplete fetch failed:', error);
        }
    }
    
    displayAutoCompleteResults(field, results) {
        const { dropdown } = this.autoCompletes.get(field);
        
        if (!results || results.length === 0) {
            this.hideAutoComplete(field);
            return;
        }
        
        dropdown.innerHTML = '';
        
        results.forEach((result, index) => {
            const item = document.createElement('div');
            item.className = 'autocomplete-item';
            item.style.cssText = `
                padding: 8px 12px;
                cursor: pointer;
                border-bottom: 1px solid #eee;
            `;
            
            if (typeof result === 'string') {
                item.textContent = result;
                item.addEventListener('click', () => {
                    field.value = result;
                    this.hideAutoComplete(field);
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                });
            } else {
                item.textContent = result.label || result.name || result.value;
                item.addEventListener('click', () => {
                    field.value = result.value || result.label || result.name;
                    this.hideAutoComplete(field);
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                });
            }
            
            // Hover effects
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = '#f8f9fa';
            });
            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = '';
            });
            
            dropdown.appendChild(item);
        });
        
        this.showAutoComplete(field);
    }
    
    handleAutoCompleteKeydown(field, e) {
        const { dropdown } = this.autoCompletes.get(field);
        const items = dropdown.querySelectorAll('.autocomplete-item');
        
        if (items.length === 0) return;
        
        let currentIndex = Array.from(items).findIndex(item => 
            item.classList.contains('highlighted')
        );
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, items.length - 1);
                this.highlightAutoCompleteItem(items, currentIndex);
                break;
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                this.highlightAutoCompleteItem(items, currentIndex);
                break;
            case 'Enter':
                e.preventDefault();
                if (currentIndex >= 0) {
                    items[currentIndex].click();
                }
                break;
            case 'Escape':
                this.hideAutoComplete(field);
                break;
        }
    }
    
    highlightAutoCompleteItem(items, index) {
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('highlighted');
                item.style.backgroundColor = '#e9ecef';
            } else {
                item.classList.remove('highlighted');
                item.style.backgroundColor = '';
            }
        });
    }
    
    showAutoComplete(field) {
        const { dropdown } = this.autoCompletes.get(field);
        dropdown.style.display = 'block';
        this.autoCompletes.get(field).isOpen = true;
    }
    
    hideAutoComplete(field) {
        const { dropdown } = this.autoCompletes.get(field);
        dropdown.style.display = 'none';
        this.autoCompletes.get(field).isOpen = false;
    }
    
    // === FILE UPLOAD WITH PROGRESS ===
    
    setupFileUploads() {
        const fileInputs = document.querySelectorAll('[data-upload-progress="true"]');
        
        fileInputs.forEach(input => {
            this.setupFileUpload(input);
        });
    }
    
    setupFileUpload(input) {
        const config = {
            maxSize: parseInt(input.getAttribute('data-max-size')) || 5 * 1024 * 1024, // 5MB default
            allowedTypes: input.getAttribute('data-allowed-types')?.split(',') || [],
            uploadUrl: input.getAttribute('data-upload-url') || '/upload/',
            previewContainer: input.getAttribute('data-preview-container')
        };
        
        this.fileUploads.set(input, config);
        
        input.addEventListener('change', (e) => {
            this.handleFileSelection(input, e.target.files);
        });
    }
    
    handleFileSelection(input, files) {
        const config = this.fileUploads.get(input);
        
        Array.from(files).forEach(file => {
            if (this.validateFile(file, config)) {
                this.uploadFile(input, file, config);
            }
        });
    }
    
    validateFile(file, config) {
        // Size validation
        if (file.size > config.maxSize) {
            alert(`File size must be less than ${this.formatFileSize(config.maxSize)}`);
            return false;
        }
        
        // Type validation
        if (config.allowedTypes.length > 0) {
            const fileType = file.type || '';
            const fileExt = file.name.split('.').pop().toLowerCase();
            
            const isAllowed = config.allowedTypes.some(type => {
                return fileType.includes(type) || type.includes(fileExt);
            });
            
            if (!isAllowed) {
                alert(`File type not allowed. Allowed types: ${config.allowedTypes.join(', ')}`);
                return false;
            }
        }
        
        return true;
    }
    
    async uploadFile(input, file, config) {
        const progressContainer = this.createProgressIndicator(input, file);
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('field_name', input.name);
            
            const xhr = new XMLHttpRequest();
            
            // Progress tracking
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentage = Math.round((e.loaded / e.total) * 100);
                    this.updateProgress(progressContainer, percentage);
                }
            });
            
            // Success/Error handling
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    this.handleUploadSuccess(input, file, response, progressContainer);
                } else {
                    this.handleUploadError(progressContainer, 'Upload failed');
                }
            });
            
            xhr.addEventListener('error', () => {
                this.handleUploadError(progressContainer, 'Upload failed');
            });
            
            // Start upload
            xhr.open('POST', config.uploadUrl);
            xhr.setRequestHeader('X-CSRFToken', this.getCSRFToken());
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            xhr.send(formData);
            
        } catch (error) {
            this.handleUploadError(progressContainer, error.message);
        }
    }
    
    createProgressIndicator(input, file) {
        const container = document.createElement('div');
        container.className = 'upload-progress';
        container.innerHTML = `
            <div class="upload-info">
                <span class="file-name">${file.name}</span>
                <span class="file-size">${this.formatFileSize(file.size)}</span>
            </div>
            <div class="progress">
                <div class="progress-bar" style="width: 0%"></div>
            </div>
            <div class="upload-status">Uploading...</div>
        `;
        
        const inputContainer = input.closest('.form-group, .mb-3, .field-container') || input.parentElement;
        inputContainer.appendChild(container);
        
        return container;
    }
    
    updateProgress(container, percentage) {
        const progressBar = container.querySelector('.progress-bar');
        const status = container.querySelector('.upload-status');
        
        progressBar.style.width = `${percentage}%`;
        status.textContent = `Uploading... ${percentage}%`;
    }
    
    handleUploadSuccess(input, file, response, progressContainer) {
        const status = progressContainer.querySelector('.upload-status');
        status.textContent = 'Upload complete!';
        status.style.color = '#28a745';
        
        // Update input value if response contains file URL/ID
        if (response.file_id) {
            input.setAttribute('data-file-id', response.file_id);
        }
        
        // Show preview if applicable
        if (response.preview_url) {
            this.showFilePreview(input, response.preview_url, file.type);
        }
        
        // Remove progress indicator after delay
        setTimeout(() => {
            progressContainer.remove();
        }, 3000);
    }
    
    handleUploadError(progressContainer, message) {
        const status = progressContainer.querySelector('.upload-status');
        status.textContent = `Error: ${message}`;
        status.style.color = '#dc3545';
        
        setTimeout(() => {
            progressContainer.remove();
        }, 5000);
    }
    
    showFilePreview(input, previewUrl, fileType) {
        const config = this.fileUploads.get(input);
        if (!config.previewContainer) return;
        
        const previewContainer = document.querySelector(config.previewContainer);
        if (!previewContainer) return;
        
        const preview = document.createElement('div');
        preview.className = 'file-preview';
        
        if (fileType.startsWith('image/')) {
            preview.innerHTML = `<img src="${previewUrl}" alt="Preview" style="max-width: 200px; max-height: 200px;">`;
        } else {
            preview.innerHTML = `<a href="${previewUrl}" target="_blank">View File</a>`;
        }
        
        previewContainer.appendChild(preview);
    }
    
    // === MODERN ES6+ PATTERNS ===
    
    setupModernPatterns() {
        // Use modern event delegation
        this.setupEventDelegation();
        
        // Setup intersection observer for lazy loading
        this.setupIntersectionObserver();
        
        // Setup modern form features
        this.setupModernFormFeatures();
    }
    
    setupEventDelegation() {
        // Modern event delegation using closest()
        document.addEventListener('click', (e) => {
            // Handle dynamic buttons
            const dynamicBtn = e.target.closest('[data-dynamic-action]');
            if (dynamicBtn) {
                e.preventDefault();
                this.handleDynamicAction(dynamicBtn);
            }
        });
    }
    
    setupIntersectionObserver() {
        // Lazy load form sections
        const lazyForms = document.querySelectorAll('[data-lazy-load="true"]');
        
        if (lazyForms.length > 0 && 'IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadFormSection(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            lazyForms.forEach(form => observer.observe(form));
        }
    }
    
    setupModernFormFeatures() {
        // Use modern APIs where available
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.optimizeFormPerformance();
            });
        }
        
        // Setup modern validation API
        if ('ValidityState' in window) {
            this.setupCustomValidation();
        }
    }
    
    // === UTILITY METHODS ===
    
    debounce(key, func, wait) {
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }
        
        const timer = setTimeout(() => {
            func();
            this.debounceTimers.delete(key);
        }, wait);
        
        this.debounceTimers.set(key, timer);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name=csrf-token]')?.content ||
               '';
    }
    
    setFormLoading(form, loading) {
        if (loading) {
            form.classList.add('form-loading');
        } else {
            form.classList.remove('form-loading');
        }
    }
    
    showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        
        const container = field.closest('.form-group, .mb-3, .field-container');
        let feedback = container?.querySelector('.invalid-feedback, .validation-feedback');
        
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            container?.appendChild(feedback);
        }
        
        feedback.textContent = message;
    }
    
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        
        const container = field.closest('.form-group, .mb-3, .field-container');
        const feedback = container?.querySelector('.invalid-feedback, .validation-feedback');
        
        if (feedback) {
            feedback.textContent = '';
        }
    }
    
    clearFormErrors(form) {
        const invalidFields = form.querySelectorAll('.is-invalid');
        invalidFields.forEach(field => this.clearFieldError(field));
        
        const messageContainer = form.querySelector('.form-message');
        if (messageContainer) {
            messageContainer.remove();
        }
    }
    
    showFormMessage(form, message, type) {
        let messageContainer = form.querySelector('.form-message');
        
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.className = 'form-message';
            form.insertBefore(messageContainer, form.firstChild);
        }
        
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        messageContainer.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

// Initialize the form components
const formComponents = new FormComponents();

// Export for use in other scripts
window.FormComponents = FormComponents;
window.formComponents = formComponents;
