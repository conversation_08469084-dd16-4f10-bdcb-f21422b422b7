/**
 * Enhanced Venue Creation JavaScript for 5-Step Wizard
 * Provides multi-step wizard functionality, real-time validation, progress saving,
 * character counters, field dependencies, image upload, dynamic forms, and mobile-responsive interactions.
 */

class VenueCreationWizard {
    constructor() {
        this.currentStep = document.querySelector('#wizardForm')?.dataset.step || 'basic';
        this.autoSaveDelay = 2000; // 2 seconds
        this.autoSaveTimeout = null;
        this.validationCache = new Map();

        // 5-step wizard configuration
        this.stepOrder = ['basic', 'location', 'services', 'gallery', 'details'];
        this.totalSteps = 5;

        // Dynamic data storage
        this.operatingHours = {};
        this.services = [];
        this.teamMembers = [];
        this.faqs = [];
        this.images = [];

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupRealTimeValidation();
        this.setupCharacterCounters();
        this.setupFieldDependencies();
        this.setupMobileEnhancements();
        this.setupAutoSave();
        this.initializeExistingValues();

        // Initialize step-specific functionality
        this.initializeStepSpecific();
    }

    initializeStepSpecific() {
        switch(this.currentStep) {
            case 'hours_amenities':
                this.initializeOperatingHours();
                break;
            case 'services':
                this.initializeServicesManager();
                break;
            case 'gallery_team':
                this.initializeImageUpload();
                this.initializeTeamManager();
                break;
            case 'details':
                this.initializeOperatingHours();
                this.initializeFAQManager();
                break;
            case 'review_submit':
                this.initializeFAQManager();
                break;
        }
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('wizardForm');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }

        // Category selection
        this.setupCategorySelection();

        // Status selection
        this.setupStatusSelection();

        // Navigation buttons
        this.setupNavigationButtons();

        // Save progress button
        const saveProgressBtn = document.getElementById('saveProgressBtn');
        if (saveProgressBtn) {
            saveProgressBtn.addEventListener('click', this.saveProgress.bind(this));
        }

        // Step-specific event listeners
        this.setupStepSpecificListeners();
    }

    setupStepSpecificListeners() {
        // Add service button
        const addServiceBtn = document.getElementById('add-service-btn');
        if (addServiceBtn) {
            addServiceBtn.addEventListener('click', this.addService.bind(this));
        }

        // Add team member button
        const addTeamMemberBtn = document.getElementById('add-team-member-btn');
        if (addTeamMemberBtn) {
            addTeamMemberBtn.addEventListener('click', this.addTeamMember.bind(this));
        }

        // Add FAQ button
        const addFAQBtn = document.getElementById('add-faq-btn');
        if (addFAQBtn) {
            addFAQBtn.addEventListener('click', this.addFAQ.bind(this));
        }

        // Image upload area
        const imageUploadArea = document.getElementById('image-upload-area');
        if (imageUploadArea) {
            this.setupImageUploadListeners(imageUploadArea);
        }
    }

    setupRealTimeValidation() {
        const validatedFields = [
            'venue_name', 'short_description', 'phone', 'email', 'website_url',
            'state', 'county', 'city', 'street_number', 'street_name'
        ];

        validatedFields.forEach(fieldName => {
            const field = document.getElementById(`id_${fieldName}`);
            if (field) {
                // Input event for real-time validation
                field.addEventListener('input', (e) => this.debounce(() => {
                    this.validateField(fieldName, e.target.value);
                }, 500)());

                // Blur event for final validation
                field.addEventListener('blur', (e) => {
                    this.validateField(fieldName, e.target.value);
                });

                // Focus event to clear previous errors
                field.addEventListener('focus', (e) => {
                    this.clearFieldValidation(fieldName);
                });
            }
        });
    }

    setupCharacterCounters() {
        const fieldsWithCounters = [
            { field: 'short_description', max: 500, min: 10 },
            { field: 'venue_name', max: 255, min: 2 }
        ];

        fieldsWithCounters.forEach(({ field, max, min }) => {
            const input = document.getElementById(`id_${field}`);
            if (input) {
                this.createCharacterCounter(input, max, min);
                
                input.addEventListener('input', (e) => {
                    this.updateCharacterCounter(e.target, max, min);
                });

                // Initialize counter
                this.updateCharacterCounter(input, max, min);
            }
        });
    }

    createCharacterCounter(input, max, min) {
        const counterId = `${input.id}_counter`;
        const existing = document.getElementById(counterId);
        
        if (!existing) {
            const counter = document.createElement('div');
            counter.id = counterId;
            counter.className = 'character-counter d-flex justify-content-between align-items-center mt-2';
            counter.innerHTML = `
                <div class="character-count">
                    <span class="current">0</span> / <span class="max">${max}</span>
                </div>
                <div class="counter-hint text-muted">
                    ${min ? `Minimum ${min} characters` : ''}
                </div>
            `;
            
            input.parentNode.insertBefore(counter, input.nextSibling);
        }
    }

    updateCharacterCounter(input, max, min) {
        const counterId = `${input.id}_counter`;
        const counter = document.getElementById(counterId);
        const currentLength = input.value.length;
        
        if (counter) {
            const currentSpan = counter.querySelector('.current');
            const counterDiv = counter.querySelector('.character-count');
            
            if (currentSpan) {
                currentSpan.textContent = currentLength;
            }
            
            // Apply styling based on length
            counterDiv.className = 'character-count';
            if (currentLength > max) {
                counterDiv.classList.add('text-danger', 'fw-bold');
            } else if (currentLength > max * 0.9) {
                counterDiv.classList.add('text-warning', 'fw-bold');
            } else if (min && currentLength >= min) {
                counterDiv.classList.add('text-success');
            }
        }
    }

    setupFieldDependencies() {
        // State -> County dependency
        const stateField = document.getElementById('id_state');
        const countyField = document.getElementById('id_county');
        const cityField = document.getElementById('id_city');

        if (stateField && countyField) {
            stateField.addEventListener('change', () => {
                this.updateDependentField(stateField, countyField, 'county');
                this.clearField(cityField);
            });
        }

        if (countyField && cityField) {
            countyField.addEventListener('input', this.debounce(() => {
                this.updateDependentField(countyField, cityField, 'city');
            }, 300));
        }
    }

    updateDependentField(sourceField, targetField, type) {
        if (!sourceField.value.trim()) {
            targetField.disabled = true;
            targetField.placeholder = `Select ${sourceField.name} first`;
            return;
        }

        targetField.disabled = false;
        targetField.placeholder = `Enter ${type} name`;
        
        // Add autocomplete suggestions based on state/county
        this.loadLocationSuggestions(sourceField.value, type, targetField);
    }

    async loadLocationSuggestions(parentValue, type, targetField) {
        try {
            const response = await fetch(`/venues/api/location-autocomplete/?type=${type}&parent=${encodeURIComponent(parentValue)}`);
            const data = await response.json();
            
            if (data.suggestions) {
                this.setupAutocomplete(targetField, data.suggestions);
            }
        } catch (error) {
            console.error('Error loading location suggestions:', error);
        }
    }

    setupAutocomplete(field, suggestions) {
        // Remove existing datalist
        const existingDatalist = document.getElementById(`${field.id}_datalist`);
        if (existingDatalist) {
            existingDatalist.remove();
        }

        // Create new datalist
        const datalist = document.createElement('datalist');
        datalist.id = `${field.id}_datalist`;
        
        suggestions.forEach(suggestion => {
            const option = document.createElement('option');
            option.value = suggestion;
            datalist.appendChild(option);
        });

        field.setAttribute('list', datalist.id);
        field.parentNode.appendChild(datalist);
    }

    setupCategorySelection() {
        const categoryCards = document.querySelectorAll('.category-card');
        const maxSelections = 3;

        categoryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const checkbox = card.querySelector('input[type="checkbox"]');
                if (!checkbox) return;

                const selectedCount = document.querySelectorAll('.category-card input[type="checkbox"]:checked').length;
                
                if (!checkbox.checked && selectedCount >= maxSelections) {
                    this.showToast('warning', `You can select up to ${maxSelections} categories only.`);
                    return;
                }

                checkbox.checked = !checkbox.checked;
                card.classList.toggle('selected', checkbox.checked);

                // Trigger auto-save
                this.scheduleAutoSave();
            });
        });
    }

    setupStatusSelection() {
        const statusCards = document.querySelectorAll('.status-card');
        
        statusCards.forEach(card => {
            card.addEventListener('click', () => {
                const radio = card.querySelector('input[type="radio"]');
                if (!radio) return;

                // Remove selected class from all cards
                statusCards.forEach(c => c.classList.remove('selected'));
                
                // Select current card
                radio.checked = true;
                card.classList.add('selected');

                // Trigger auto-save
                this.scheduleAutoSave();
            });
        });
    }

    setupNavigationButtons() {
        const nextBtn = document.querySelector('.btn-next');
        const prevBtn = document.querySelector('.btn-prev');

        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                if (!this.validateCurrentStep()) {
                    e.preventDefault();
                    this.showToast('error', 'Please fix the errors before continuing.');
                }
            });
        }
    }

    setupMobileEnhancements() {
        // Improve mobile form experience
        this.setupMobileKeyboard();
        this.setupTouchOptimizations();
        this.setupMobileNavigation();
    }

    setupMobileKeyboard() {
        // Set appropriate input types for mobile keyboards
        const phoneField = document.getElementById('id_phone');
        const emailField = document.getElementById('id_email');
        const websiteField = document.getElementById('id_website_url');

        if (phoneField) {
            phoneField.setAttribute('inputmode', 'tel');
            phoneField.setAttribute('pattern', '[0-9\\-\\(\\)\\+\\s]*');
        }

        if (emailField) {
            emailField.setAttribute('inputmode', 'email');
        }

        if (websiteField) {
            websiteField.setAttribute('inputmode', 'url');
        }
    }

    setupTouchOptimizations() {
        // Increase touch targets on mobile
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            const touchElements = document.querySelectorAll('.category-card, .status-card, .btn-wizard');
            touchElements.forEach(element => {
                element.style.minHeight = '48px';
                element.style.padding = Math.max(12, parseInt(getComputedStyle(element).padding) || 12) + 'px';
            });
        }
    }

    setupMobileNavigation() {
        // Add swipe navigation for mobile
        if ('ontouchstart' in window) {
            let startX = 0;
            let startY = 0;

            document.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            document.addEventListener('touchend', (e) => {
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const diffX = startX - endX;
                const diffY = startY - endY;

                // Detect horizontal swipe (and not vertical scroll)
                if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        // Swipe left - go to next step
                        const nextBtn = document.querySelector('.btn-next');
                        if (nextBtn && !nextBtn.disabled) {
                            nextBtn.click();
                        }
                    } else {
                        // Swipe right - go to previous step
                        const prevBtn = document.querySelector('.btn-prev');
                        if (prevBtn) {
                            prevBtn.click();
                        }
                    }
                }
            });
        }
    }

    setupAutoSave() {
        const form = document.getElementById('wizardForm');
        if (!form) return;

        // Save on form changes
        form.addEventListener('input', () => {
            this.scheduleAutoSave();
        });

        form.addEventListener('change', () => {
            this.scheduleAutoSave();
        });

        // Save before page unload
        window.addEventListener('beforeunload', () => {
            this.saveProgress(false); // Don't show UI feedback
        });
    }

    scheduleAutoSave() {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = setTimeout(() => {
            this.saveProgress();
        }, this.autoSaveDelay);
    }

    async saveProgress(showFeedback = true) {
        const form = document.getElementById('wizardForm');
        if (!form) return;

        const formData = new FormData(form);
        formData.append('action', 'save_progress');

        try {
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            });

            const data = await response.json();
            
            if (data.success && showFeedback) {
                this.showAutoSaveIndicator();
                this.updateProgressBar(data.progress_percentage);
            }
        } catch (error) {
            console.error('Auto-save error:', error);
        }
    }

    async validateField(fieldName, value) {
        if (!value.trim()) {
            this.clearFieldValidation(fieldName);
            return;
        }

        // Check cache first
        const cacheKey = `${fieldName}:${value}`;
        if (this.validationCache.has(cacheKey)) {
            const cachedResult = this.validationCache.get(cacheKey);
            this.showFieldValidation(fieldName, cachedResult);
            return;
        }

        const formData = new FormData();
        formData.append('action', 'validate_field');
        formData.append('field_name', fieldName);
        formData.append('field_value', value);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        try {
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();
            
            // Cache result
            this.validationCache.set(cacheKey, data);
            
            this.showFieldValidation(fieldName, data);
        } catch (error) {
            console.error('Validation error:', error);
        }
    }

    showFieldValidation(fieldName, validationResult) {
        const feedbackId = fieldName.replace('_', '-') + '-feedback';
        const feedback = document.getElementById(feedbackId);
        const field = document.getElementById(`id_${fieldName}`);

        if (!feedback || !field) return;

        if (validationResult.is_valid) {
            feedback.innerHTML = '<i class="fas fa-check text-success"></i> <span class="text-success">Looks good!</span>';
            feedback.className = 'field-feedback d-flex align-items-center gap-2 mt-1';
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            const errors = Array.isArray(validationResult.errors) ? validationResult.errors : [validationResult.errors];
            feedback.innerHTML = `<i class="fas fa-times text-danger"></i> <span class="text-danger">${errors.join(', ')}</span>`;
            feedback.className = 'field-feedback d-flex align-items-center gap-2 mt-1';
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }
    }

    clearFieldValidation(fieldName) {
        const feedbackId = fieldName.replace('_', '-') + '-feedback';
        const feedback = document.getElementById(feedbackId);
        const field = document.getElementById(`id_${fieldName}`);

        if (feedback) {
            feedback.innerHTML = '';
            feedback.className = 'field-feedback';
        }

        if (field) {
            field.classList.remove('is-valid', 'is-invalid');
        }
    }

    validateCurrentStep() {
        const form = document.getElementById('wizardForm');
        if (!form) return true;

        let isValid = true;
        const visibleFields = form.querySelectorAll('input:not([style*="display: none"]), select:not([style*="display: none"]), textarea:not([style*="display: none"])');

        visibleFields.forEach(field => {
            if (field.required && !field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else if (field.classList.contains('is-invalid')) {
                isValid = false;
            }
        });

        return isValid;
    }

    showAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }
    }

    updateProgressBar(percentage) {
        const progressFill = document.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
    }

    showToast(type, message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'times'}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add styles if not exists
        if (!document.getElementById('toast-styles')) {
            const styles = document.createElement('style');
            styles.id = 'toast-styles';
            styles.textContent = `
                .toast-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 1rem 1.5rem;
                    border-radius: 0.5rem;
                    color: white;
                    font-weight: 500;
                    z-index: 9999;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                }
                .toast-success { background: #10b981; }
                .toast-warning { background: #f59e0b; }
                .toast-error { background: #ef4444; }
                .toast-notification.show { transform: translateX(0); }
                .toast-content { display: flex; align-items: center; gap: 0.5rem; }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    clearField(field) {
        if (field) {
            field.value = '';
            field.classList.remove('is-valid', 'is-invalid');
        }
    }

    handleFormSubmit(e) {
        // Update hidden fields with current data before validation
        this.updateHiddenFields();

        if (!this.validateCurrentStep()) {
            e.preventDefault();
            this.showToast('error', 'Please fix all errors before proceeding.');
            return false;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }

        return true;
    }

    initializeExistingValues() {
        // Initialize character counters for existing values
        const descriptionField = document.getElementById('id_short_description');
        if (descriptionField && descriptionField.value) {
            this.updateCharacterCounter(descriptionField, 500, 10);
        }

        // Initialize selected categories
        const selectedCategories = document.querySelectorAll('.category-card input[type="checkbox"]:checked');
        selectedCategories.forEach(checkbox => {
            checkbox.closest('.category-card').classList.add('selected');
        });

        // Initialize selected status
        const selectedStatus = document.querySelector('.status-card input[type="radio"]:checked');
        if (selectedStatus) {
            selectedStatus.closest('.status-card').classList.add('selected');
        }
        
        // Load existing FAQ data from hidden field
        const faqsField = document.querySelector('input[name="faqs"]');
        if (faqsField && faqsField.value) {
            try {
                const existingFaqs = JSON.parse(faqsField.value);
                if (Array.isArray(existingFaqs) && existingFaqs.length > 0) {
                    this.faqs = existingFaqs.map(faq => ({
                        id: faq.id || Date.now() + Math.random(),
                        question: faq.question || '',
                        answer: faq.answer || ''
                    }));
                    
                    // Render existing FAQs
                    this.faqs.forEach(faq => this.renderFAQ(faq));
                }
            } catch (error) {
                console.warn('Failed to parse existing FAQ data:', error);
            }
        }
    }

    // Utility functions
    debounce(func, delay) {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    // ===== NEW 7-STEP WIZARD METHODS =====

    updateHiddenFields() {
        // Update operating hours hidden field
        const operatingHoursField = document.querySelector('input[name="operating_hours"]');
        if (operatingHoursField && Object.keys(this.operatingHours).length > 0) {
            operatingHoursField.value = JSON.stringify(this.operatingHours);
        }

        // Update services hidden field
        const servicesField = document.querySelector('input[name="services"]');
        if (servicesField && this.services.length > 0) {
            servicesField.value = JSON.stringify(this.services);
        }

        // Update team members hidden field
        const teamMembersField = document.querySelector('input[name="team_members"]');
        if (teamMembersField && this.teamMembers.length > 0) {
            teamMembersField.value = JSON.stringify(this.teamMembers);
        }

        // Update FAQs hidden field
        const faqsField = document.querySelector('input[name="faqs"]');
        if (faqsField && this.faqs.length > 0) {
            faqsField.value = JSON.stringify(this.faqs);
        }

        // Update images hidden field - only include successfully uploaded images
        const imagesField = document.querySelector('input[name="images"]');
        if (imagesField) {
            // Filter out uploading images and images without URLs
            const uploadedImages = this.images.filter(img => 
                !img.uploading && 
                img.url && 
                !img.file  // Exclude any remaining file objects
            ).map(img => ({
                url: img.url,
                path: img.path,
                name: img.name,
                order: img.order,
                is_main: img.isMain || false
            }));
            
            if (uploadedImages.length > 0) {
                imagesField.value = JSON.stringify(uploadedImages);
            } else {
                imagesField.value = '';
            }
        }
    }

    // ===== OPERATING HOURS MANAGEMENT =====

    initializeOperatingHours() {
        const container = document.getElementById('operating-hours-container');
        if (!container) return;

        const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        days.forEach((day, index) => {
            const dayRow = this.createOperatingHourRow(day, index);
            container.appendChild(dayRow);
        });
    }

    createOperatingHourRow(dayName, dayIndex) {
        const row = document.createElement('div');
        row.className = 'operating-hour-row';
        row.innerHTML = `
            <div class="day-name">${dayName}</div>
            <div class="time-controls">
                <input type="checkbox" id="closed-${dayIndex}" class="closed-checkbox">
                <label for="closed-${dayIndex}">Closed</label>

                <input type="time" id="open-${dayIndex}" class="time-input" placeholder="Open">
                <span>to</span>
                <input type="time" id="close-${dayIndex}" class="time-input" placeholder="Close">

                <input type="checkbox" id="24hours-${dayIndex}" class="hours-checkbox">
                <label for="24hours-${dayIndex}">24 Hours</label>
            </div>
        `;

        // Add event listeners
        const closedCheckbox = row.querySelector(`#closed-${dayIndex}`);
        const hours24Checkbox = row.querySelector(`#24hours-${dayIndex}`);
        const openTime = row.querySelector(`#open-${dayIndex}`);
        const closeTime = row.querySelector(`#close-${dayIndex}`);

        closedCheckbox.addEventListener('change', () => {
            const isDisabled = closedCheckbox.checked;
            openTime.disabled = isDisabled;
            closeTime.disabled = isDisabled;
            hours24Checkbox.disabled = isDisabled;
            if (isDisabled) {
                hours24Checkbox.checked = false;
            }
            this.updateOperatingHours(dayIndex);
        });

        hours24Checkbox.addEventListener('change', () => {
            const is24Hours = hours24Checkbox.checked;
            openTime.disabled = is24Hours;
            closeTime.disabled = is24Hours;
            this.updateOperatingHours(dayIndex);
        });

        openTime.addEventListener('change', () => this.updateOperatingHours(dayIndex));
        closeTime.addEventListener('change', () => this.updateOperatingHours(dayIndex));

        return row;
    }

    updateOperatingHours(dayIndex) {
        const closedCheckbox = document.querySelector(`#closed-${dayIndex}`);
        const hours24Checkbox = document.querySelector(`#24hours-${dayIndex}`);
        const openTime = document.querySelector(`#open-${dayIndex}`);
        const closeTime = document.querySelector(`#close-${dayIndex}`);

        this.operatingHours[dayIndex] = {
            day: dayIndex,
            is_closed: closedCheckbox.checked,
            is_24_hours: hours24Checkbox.checked,
            opening: openTime.value,
            closing: closeTime.value
        };
    }

    // ===== SERVICES MANAGEMENT =====

    initializeServicesManager() {
        // Initialize with one empty service if none exist
        if (this.services.length === 0) {
            this.addService();
        }
    }

    addService() {
        const serviceId = Date.now();
        const service = {
            id: serviceId,
            title: '',
            description: '',
            price_min: '',
            price_max: '',
            duration: 60
        };

        this.services.push(service);
        this.renderService(service);
    }

    renderService(service) {
        const container = document.getElementById('services-container');
        if (!container) return;

        const serviceDiv = document.createElement('div');
        serviceDiv.className = 'service-item';
        serviceDiv.dataset.serviceId = service.id;
        serviceDiv.innerHTML = `
            <div class="service-header">
                <h6>Service ${this.services.length}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-service-btn">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Service Title</label>
                        <input type="text" class="form-control service-title" value="${service.title}" placeholder="e.g., Deep Tissue Massage">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Duration (minutes)</label>
                        <input type="number" class="form-control service-duration" value="${service.duration}" min="15" max="480">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea class="form-control service-description" rows="2" placeholder="Brief description of the service">${service.description}</textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Minimum Price ($)</label>
                        <input type="number" class="form-control service-price-min" value="${service.price_min}" min="0" step="0.01">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Maximum Price ($)</label>
                        <input type="number" class="form-control service-price-max" value="${service.price_max}" min="0" step="0.01">
                    </div>
                </div>
            </div>
        `;

        container.appendChild(serviceDiv);

        // Add event listeners
        this.setupServiceEventListeners(serviceDiv, service.id);
    }

    setupServiceEventListeners(serviceDiv, serviceId) {
        const inputs = serviceDiv.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateService(serviceId, serviceDiv);
            });
        });

        const removeBtn = serviceDiv.querySelector('.remove-service-btn');
        removeBtn.addEventListener('click', () => {
            this.removeService(serviceId);
        });
    }

    updateService(serviceId, serviceDiv) {
        const service = this.services.find(s => s.id == serviceId);
        if (!service) return;

        service.title = serviceDiv.querySelector('.service-title').value;
        service.description = serviceDiv.querySelector('.service-description').value;
        service.price_min = serviceDiv.querySelector('.service-price-min').value;
        service.price_max = serviceDiv.querySelector('.service-price-max').value;
        service.duration = serviceDiv.querySelector('.service-duration').value;
    }

    removeService(serviceId) {
        this.services = this.services.filter(s => s.id != serviceId);
        const serviceDiv = document.querySelector(`[data-service-id="${serviceId}"]`);
        if (serviceDiv) {
            serviceDiv.remove();
        }
    }

    // ===== IMAGE UPLOAD MANAGEMENT =====

    initializeImageUpload() {
        const uploadArea = document.getElementById('image-upload-area');
        if (!uploadArea) return;

        this.setupImageUploadListeners(uploadArea);
    }

    setupImageUploadListeners(uploadArea) {
        const fileInput = document.getElementById('image-upload');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            this.handleImageFiles(files);
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleImageFiles(e.target.files);
        });
    }

    handleImageFiles(files) {
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/') && this.images.length < 5) {
                this.addImage(file);
            }
        });
    }

    addImage(file) {
        const imageId = Date.now() + Math.random();
        
        // Create a placeholder image object
        const image = {
            id: imageId,
            file: file,
            url: null,
            name: file.name,
            order: this.images.length + 1,
            isMain: false,
            uploading: true
        };

        this.images.push(image);
        this.renderImagePreview(image);
        
        // Upload the file to the server
        this.uploadImageFile(file, imageId);
    }
    
    async uploadImageFile(file, imageId) {
        const formData = new FormData();
        formData.append('image', file);
        
        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        formData.append('csrfmiddlewaretoken', csrfToken);
        
        try {
            const response = await fetch('/venues/provider/create/wizard/upload-image/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update the image object with the uploaded URL
                const image = this.images.find(img => img.id === imageId);
                if (image) {
                    image.url = data.image_url;
                    image.path = data.image_path;
                    image.uploading = false;
                    image.file = null; // Remove file object to prevent serialization issues
                    
                    // Update the preview to show success
                    this.updateImagePreview(imageId, true);
                }
            } else {
                // Handle upload error
                this.handleUploadError(imageId, data.error);
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.handleUploadError(imageId, 'Network error during upload');
        }
    }
    
    handleUploadError(imageId, errorMessage) {
        // Remove the failed image
        this.removeImage(imageId);
        
        // Show error message
        this.showToast('error', `Upload failed: ${errorMessage}`);
    }
    
    updateImagePreview(imageId, success) {
        const imageDiv = document.querySelector(`[data-image-id="${imageId}"]`);
        if (!imageDiv) return;
        
        const image = this.images.find(img => img.id === imageId);
        if (!image) return;
        
        if (success) {
            imageDiv.classList.remove('uploading');
            imageDiv.classList.add('uploaded');
            
            // Update the image src and remove loading elements
            const imgElement = imageDiv.querySelector('img');
            const uploadSpinner = imageDiv.querySelector('.upload-spinner');
            const uploadStatus = imageDiv.querySelector('.upload-status');
            const setMainBtn = imageDiv.querySelector('.set-main-btn');
            
            if (imgElement && image.url) {
                imgElement.src = image.url;
            }
            
            if (uploadSpinner) {
                uploadSpinner.remove();
            }
            
            if (uploadStatus) {
                uploadStatus.remove();
            }
            
            if (setMainBtn) {
                setMainBtn.disabled = false;
            }
        } else {
            imageDiv.classList.add('error');
        }
    }

    renderImagePreview(image) {
        const container = document.getElementById('image-preview-container');
        if (!container) return;

        const imageDiv = document.createElement('div');
        imageDiv.className = 'image-preview';
        if (image.uploading) {
            imageDiv.classList.add('uploading');
        }
        imageDiv.dataset.imageId = image.id;
        
        // Use uploaded URL or show loading placeholder
        const imageUrl = image.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+';
        
        imageDiv.innerHTML = `
            <div class="image-wrapper">
                <img src="${imageUrl}" alt="${image.name}">
                ${image.uploading ? '<div class="upload-spinner"><i class="fas fa-spinner fa-spin"></i></div>' : ''}
                ${image.isMain ? '<div class="main-badge">Main</div>' : ''}
            </div>
            <div class="image-controls">
                <button type="button" class="btn btn-sm btn-primary set-main-btn" ${image.uploading ? 'disabled' : ''}>
                    Set as Main
                </button>
                <button type="button" class="btn btn-sm btn-danger remove-image-btn">
                    Remove
                </button>
            </div>
            <div class="image-info">
                <div class="image-name">${image.name}</div>
                <div class="image-order">Order: ${image.order}</div>
                ${image.uploading ? '<div class="upload-status">Uploading...</div>' : ''}
            </div>
        `;

        container.appendChild(imageDiv);

        // Add event listeners
        const setMainBtn = imageDiv.querySelector('.set-main-btn');
        const removeBtn = imageDiv.querySelector('.remove-image-btn');

        setMainBtn.addEventListener('click', () => {
            if (!image.uploading) {
                this.setMainImage(image.id);
            }
        });

        removeBtn.addEventListener('click', () => {
            this.removeImage(image.id);
        });
    }

    setMainImage(imageId) {
        // Update main image status
        this.images.forEach(img => {
            img.isMain = img.id === imageId;
        });

        // Update UI - remove main badge from all images
        document.querySelectorAll('.image-preview').forEach(preview => {
            preview.classList.remove('main-image');
            const mainBadge = preview.querySelector('.main-badge');
            if (mainBadge) {
                mainBadge.remove();
            }
        });

        // Add main badge to selected image
        const mainPreview = document.querySelector(`[data-image-id="${imageId}"]`);
        if (mainPreview) {
            mainPreview.classList.add('main-image');
            const imageWrapper = mainPreview.querySelector('.image-wrapper');
            if (imageWrapper && !imageWrapper.querySelector('.main-badge')) {
                const mainBadge = document.createElement('div');
                mainBadge.className = 'main-badge';
                mainBadge.textContent = 'Main';
                imageWrapper.appendChild(mainBadge);
            }
        }
        
        // Update hidden fields to reflect the change
        this.updateHiddenFields();
    }

    removeImage(imageId) {
        this.images = this.images.filter(img => img.id !== imageId);
        const imageDiv = document.querySelector(`[data-image-id="${imageId}"]`);
        if (imageDiv) {
            imageDiv.remove();
        }
    }

    // ===== TEAM MEMBERS MANAGEMENT =====

    initializeTeamManager() {
        // Initialize with one empty team member if none exist
        if (this.teamMembers.length === 0) {
            this.addTeamMember();
        }
    }

    addTeamMember() {
        const memberId = Date.now();
        const member = {
            id: memberId,
            name: '',
            title: '',
            bio: ''
        };

        this.teamMembers.push(member);
        this.renderTeamMember(member);
    }

    renderTeamMember(member) {
        const container = document.getElementById('team-members-container');
        if (!container) return;

        const memberDiv = document.createElement('div');
        memberDiv.className = 'team-member-item';
        memberDiv.dataset.memberId = member.id;
        memberDiv.innerHTML = `
            <div class="team-member-header">
                <h6>Team Member ${this.teamMembers.length}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-member-btn">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" class="form-control member-name" value="${member.name}" placeholder="Full name">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Title/Position</label>
                        <input type="text" class="form-control member-title" value="${member.title}" placeholder="e.g., Licensed Massage Therapist">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Bio (Optional)</label>
                <textarea class="form-control member-bio" rows="2" placeholder="Brief bio or specialties">${member.bio}</textarea>
            </div>
        `;

        container.appendChild(memberDiv);

        // Add event listeners
        this.setupTeamMemberEventListeners(memberDiv, member.id);
    }

    setupTeamMemberEventListeners(memberDiv, memberId) {
        const inputs = memberDiv.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateTeamMember(memberId, memberDiv);
            });
        });

        const removeBtn = memberDiv.querySelector('.remove-member-btn');
        removeBtn.addEventListener('click', () => {
            this.removeTeamMember(memberId);
        });
    }

    updateTeamMember(memberId, memberDiv) {
        const member = this.teamMembers.find(m => m.id == memberId);
        if (!member) return;

        member.name = memberDiv.querySelector('.member-name').value;
        member.title = memberDiv.querySelector('.member-title').value;
        member.bio = memberDiv.querySelector('.member-bio').value;
    }

    removeTeamMember(memberId) {
        this.teamMembers = this.teamMembers.filter(m => m.id != memberId);
        const memberDiv = document.querySelector(`[data-member-id="${memberId}"]`);
        if (memberDiv) {
            memberDiv.remove();
        }
    }

    // ===== FAQ MANAGEMENT =====

    initializeFAQManager() {
        // Initialize with 3 empty FAQs if none exist and none were loaded from existing data
        if (this.faqs.length === 0) {
            // Add 3 FAQ forms by default
            this.addFAQ();
            this.addFAQ();
            this.addFAQ();
        }
    }

    addFAQ() {
        const faqId = Date.now();
        const faq = {
            id: faqId,
            question: '',
            answer: ''
        };

        this.faqs.push(faq);
        this.renderFAQ(faq);
        
        // Update hidden fields to sync form data
        this.updateHiddenFields();
    }

    renderFAQ(faq) {
        const container = document.getElementById('faqs-container');
        if (!container) return;

        const faqDiv = document.createElement('div');
        faqDiv.className = 'faq-item';
        faqDiv.dataset.faqId = faq.id;
        faqDiv.innerHTML = `
            <div class="faq-header">
                <h6>FAQ ${this.faqs.length}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-faq-btn">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="form-group">
                <label>Question</label>
                <input type="text" class="form-control faq-question" value="${faq.question}" placeholder="What is your cancellation policy?">
            </div>
            <div class="form-group">
                <label>Answer</label>
                <textarea class="form-control faq-answer" rows="3" placeholder="We require 24 hours notice for cancellations...">${faq.answer}</textarea>
            </div>
        `;

        container.appendChild(faqDiv);

        // Add event listeners
        this.setupFAQEventListeners(faqDiv, faq.id);
    }

    setupFAQEventListeners(faqDiv, faqId) {
        const inputs = faqDiv.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateFAQ(faqId, faqDiv);
            });
        });

        const removeBtn = faqDiv.querySelector('.remove-faq-btn');
        removeBtn.addEventListener('click', () => {
            this.removeFAQ(faqId);
        });
    }

    updateFAQ(faqId, faqDiv) {
        const faq = this.faqs.find(f => f.id == faqId);
        if (!faq) return;

        faq.question = faqDiv.querySelector('.faq-question').value;
        faq.answer = faqDiv.querySelector('.faq-answer').value;
        
        // Update hidden fields to sync form data
        this.updateHiddenFields();
    }

    removeFAQ(faqId) {
        this.faqs = this.faqs.filter(f => f.id != faqId);
        const faqDiv = document.querySelector(`[data-faq-id="${faqId}"]`);
        if (faqDiv) {
            faqDiv.remove();
        }
        
        // Update hidden fields to sync form data
        this.updateHiddenFields();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.venueWizard = new VenueCreationWizard();
});

// Additional utility functions for backward compatibility
function selectStatusOption(element, value) {
    if (window.venueWizard) {
        element.click();
    }
}

function toggleCategoryOption(element, value) {
    if (window.venueWizard) {
        element.click();
    }
} 