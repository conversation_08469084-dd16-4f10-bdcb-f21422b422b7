/**
 * CozyWish Form Validation Framework
 * 
 * A comprehensive client-side validation system with:
 * - Real-time email validation
 * - Password strength indicators
 * - Phone number formatting
 * - Field validation on blur
 * - Submit button state management
 * - Progressive enhancement
 */

class FormValidationFramework {
    constructor() {
        this.validators = new Map();
        this.formatters = new Map();
        this.strengthIndicators = new Map();
        this.validationRules = {
            email: {
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Please enter a valid email address'
            },
            phone: {
                pattern: /^[\+]?[1-9][\d]{0,15}$/,
                format: /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
                message: 'Please enter a valid phone number'
            },
            url: {
                pattern: /^https?:\/\/.+\..+/,
                message: 'Please enter a valid URL starting with http:// or https://'
            },
            password: {
                minLength: 8,
                requireUppercase: true,
                requireLowercase: true,
                requireNumbers: true,
                requireSpecialChars: true,
                message: 'Password must meet security requirements'
            }
        };
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupValidation());
        } else {
            this.setupValidation();
        }
    }
    
    setupValidation() {
        this.setupFormValidation();
        this.setupFieldValidation();
        this.setupPasswordStrengthIndicators();
        this.setupPhoneFormatting();
        this.setupSubmitButtonManagement();
        this.setupProgressiveEnhancement();
    }
    
    // === FORM-LEVEL VALIDATION ===
    
    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validation="enhanced"]');
        
        forms.forEach(form => {
            this.enhanceForm(form);
        });
    }
    
    enhanceForm(form) {
        // Add validation container if not exists
        if (!form.querySelector('.form-validation-summary')) {
            const summary = document.createElement('div');
            summary.className = 'form-validation-summary d-none';
            summary.setAttribute('role', 'alert');
            summary.setAttribute('aria-live', 'polite');
            form.insertBefore(summary, form.firstChild);
        }
        
        // Setup form submission handling
        form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Setup real-time form validation
        const fields = form.querySelectorAll('input, textarea, select');
        fields.forEach(field => {
            field.addEventListener('input', () => this.validateForm(form));
            field.addEventListener('blur', () => this.validateField(field, true));
        });
        
        // Initial validation
        this.validateForm(form);
    }
    
    handleFormSubmit(e) {
        const form = e.target;
        const isValid = this.validateForm(form, true);
        
        if (!isValid) {
            e.preventDefault();
            this.showFormErrors(form);
            this.focusFirstError(form);
            return false;
        }
        
        // Show loading state
        this.setFormLoading(form, true);
        return true;
    }
    
    validateForm(form, showErrors = false) {
        const fields = form.querySelectorAll('[data-validation]');
        let isValid = true;
        const errors = [];
        
        fields.forEach(field => {
            const fieldValid = this.validateField(field, showErrors);
            if (!fieldValid) {
                isValid = false;
                const label = this.getFieldLabel(field);
                errors.push(`${label}: ${this.getFieldError(field)}`);
            }
        });
        
        // Update submit button state
        this.updateSubmitButton(form, isValid);
        
        // Update validation summary
        if (showErrors) {
            this.updateValidationSummary(form, errors);
        }
        
        return isValid;
    }
    
    // === FIELD-LEVEL VALIDATION ===
    
    setupFieldValidation() {
        const fields = document.querySelectorAll('[data-validation]');
        
        fields.forEach(field => {
            this.enhanceField(field);
        });
    }
    
    enhanceField(field) {
        const validationType = field.getAttribute('data-validation');
        const fieldContainer = this.getFieldContainer(field);
        
        // Create feedback elements
        this.createFeedbackElements(field, fieldContainer);
        
        // Setup validation events
        if (field.getAttribute('data-validation-realtime') === 'true') {
            field.addEventListener('input', this.debounce(() => {
                this.validateField(field);
            }, 300));
        }
        
        field.addEventListener('blur', () => {
            this.validateField(field, true);
        });
        
        // Setup formatters
        if (validationType === 'phone') {
            field.addEventListener('input', () => this.formatPhone(field));
        }
    }
    
    validateField(field, showErrors = false) {
        const validationType = field.getAttribute('data-validation');
        const value = field.value.trim();
        const fieldContainer = this.getFieldContainer(field);
        
        // Clear previous validation state
        this.clearFieldValidation(field, fieldContainer);
        
        // Skip validation for empty optional fields
        if (!value && !field.hasAttribute('required')) {
            return true;
        }
        
        let isValid = true;
        let errorMessage = '';
        
        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }
        
        // Type-specific validation
        if (value && validationType && this.validationRules[validationType]) {
            const result = this.validateFieldType(field, validationType, value);
            if (!result.isValid) {
                isValid = false;
                errorMessage = result.message;
            }
        }
        
        // Update field state
        this.updateFieldState(field, fieldContainer, isValid, errorMessage, showErrors);
        
        return isValid;
    }
    
    validateFieldType(field, type, value) {
        const rules = this.validationRules[type];
        
        switch (type) {
            case 'email':
                return this.validateEmail(value, rules);
            case 'phone':
                return this.validatePhone(value, rules);
            case 'url':
                return this.validateUrl(value, rules);
            case 'password':
                return this.validatePassword(value, rules, field);
            default:
                return { isValid: true, message: '' };
        }
    }
    
    validateEmail(value, rules) {
        if (!rules.pattern.test(value)) {
            return { isValid: false, message: rules.message };
        }
        
        // Additional email validation
        const domain = value.split('@')[1];
        if (domain && domain.length < 2) {
            return { isValid: false, message: 'Please enter a valid email domain' };
        }
        
        return { isValid: true, message: 'Valid email address' };
    }
    
    validatePhone(value, rules) {
        const digitsOnly = value.replace(/\D/g, '');
        
        if (digitsOnly.length < 10) {
            return { isValid: false, message: 'Phone number must be at least 10 digits' };
        }
        
        if (digitsOnly.length > 15) {
            return { isValid: false, message: 'Phone number is too long' };
        }
        
        return { isValid: true, message: 'Valid phone number' };
    }
    
    validateUrl(value, rules) {
        if (!rules.pattern.test(value)) {
            return { isValid: false, message: rules.message };
        }
        
        return { isValid: true, message: 'Valid URL' };
    }
    
    validatePassword(value, rules, field) {
        const errors = [];
        
        if (value.length < rules.minLength) {
            errors.push(`At least ${rules.minLength} characters`);
        }
        
        if (rules.requireUppercase && !/[A-Z]/.test(value)) {
            errors.push('One uppercase letter');
        }
        
        if (rules.requireLowercase && !/[a-z]/.test(value)) {
            errors.push('One lowercase letter');
        }
        
        if (rules.requireNumbers && !/\d/.test(value)) {
            errors.push('One number');
        }
        
        if (rules.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
            errors.push('One special character');
        }
        
        // Update strength indicator
        this.updatePasswordStrength(field, value, errors.length === 0);
        
        if (errors.length > 0) {
            return { 
                isValid: false, 
                message: `Password needs: ${errors.join(', ')}` 
            };
        }
        
        return { isValid: true, message: 'Strong password' };
    }
    
    // === PASSWORD STRENGTH INDICATORS ===
    
    setupPasswordStrengthIndicators() {
        const passwordFields = document.querySelectorAll('[data-validation="password"]');
        
        passwordFields.forEach(field => {
            this.createPasswordStrengthIndicator(field);
        });
    }
    
    createPasswordStrengthIndicator(field) {
        const container = this.getFieldContainer(field);
        
        if (container.querySelector('.password-strength-indicator')) {
            return; // Already exists
        }
        
        const indicator = document.createElement('div');
        indicator.className = 'password-strength-indicator mt-2';
        indicator.innerHTML = `
            <div class="strength-bar">
                <div class="strength-fill"></div>
            </div>
            <div class="strength-text text-muted small">
                Enter a password to see strength
            </div>
            <div class="strength-requirements small text-muted mt-1">
                <div class="requirement" data-requirement="length">
                    <i class="fas fa-circle text-muted"></i> At least 8 characters
                </div>
                <div class="requirement" data-requirement="uppercase">
                    <i class="fas fa-circle text-muted"></i> One uppercase letter
                </div>
                <div class="requirement" data-requirement="lowercase">
                    <i class="fas fa-circle text-muted"></i> One lowercase letter
                </div>
                <div class="requirement" data-requirement="number">
                    <i class="fas fa-circle text-muted"></i> One number
                </div>
                <div class="requirement" data-requirement="special">
                    <i class="fas fa-circle text-muted"></i> One special character
                </div>
            </div>
        `;
        
        container.appendChild(indicator);
        this.strengthIndicators.set(field, indicator);
    }
    
    updatePasswordStrength(field, password, isStrong) {
        const indicator = this.strengthIndicators.get(field);
        if (!indicator) return;
        
        const strengthFill = indicator.querySelector('.strength-fill');
        const strengthText = indicator.querySelector('.strength-text');
        const requirements = indicator.querySelectorAll('.requirement');
        
        if (!password) {
            strengthFill.style.width = '0%';
            strengthFill.className = 'strength-fill';
            strengthText.textContent = 'Enter a password to see strength';
            requirements.forEach(req => {
                const icon = req.querySelector('i');
                icon.className = 'fas fa-circle text-muted';
            });
            return;
        }
        
        // Calculate strength
        let strength = 0;
        const checks = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        
        Object.values(checks).forEach(check => {
            if (check) strength++;
        });
        
        // Update visual indicators
        const percentage = (strength / 5) * 100;
        strengthFill.style.width = `${percentage}%`;
        
        let strengthClass = 'weak';
        let strengthLabel = 'Weak';
        
        if (strength >= 5) {
            strengthClass = 'strong';
            strengthLabel = 'Strong';
        } else if (strength >= 3) {
            strengthClass = 'medium';
            strengthLabel = 'Medium';
        }
        
        strengthFill.className = `strength-fill strength-${strengthClass}`;
        strengthText.textContent = `Password strength: ${strengthLabel}`;
        
        // Update requirement indicators
        Object.entries(checks).forEach(([requirement, met]) => {
            const reqElement = indicator.querySelector(`[data-requirement="${requirement}"]`);
            if (reqElement) {
                const icon = reqElement.querySelector('i');
                icon.className = met ? 'fas fa-check-circle text-success' : 'fas fa-circle text-muted';
            }
        });
    }
    
    // === PHONE FORMATTING ===
    
    setupPhoneFormatting() {
        const phoneFields = document.querySelectorAll('[data-phone-format="true"]');
        
        phoneFields.forEach(field => {
            this.formatters.set(field, this.createPhoneFormatter(field));
        });
    }
    
    createPhoneFormatter(field) {
        return {
            format: (value) => this.formatPhone(field, value),
            clean: (value) => value.replace(/\D/g, '')
        };
    }
    
    formatPhone(field, value = null) {
        const currentValue = value || field.value;
        const digitsOnly = currentValue.replace(/\D/g, '');
        
        let formatted = digitsOnly;
        
        if (digitsOnly.length >= 6) {
            formatted = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`;
        } else if (digitsOnly.length >= 3) {
            formatted = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3)}`;
        }
        
        if (field.value !== formatted) {
            const cursorPosition = field.selectionStart;
            field.value = formatted;
            
            // Restore cursor position
            const newPosition = this.calculateCursorPosition(currentValue, formatted, cursorPosition);
            field.setSelectionRange(newPosition, newPosition);
        }
        
        return formatted;
    }
    
    calculateCursorPosition(oldValue, newValue, oldPosition) {
        // Simple cursor position calculation for phone formatting
        const oldDigits = oldValue.slice(0, oldPosition).replace(/\D/g, '').length;
        let newPosition = 0;
        let digitCount = 0;
        
        for (let i = 0; i < newValue.length && digitCount < oldDigits; i++) {
            if (/\d/.test(newValue[i])) {
                digitCount++;
            }
            newPosition = i + 1;
        }
        
        return newPosition;
    }
    
    // === UTILITY METHODS ===
    
    getFieldContainer(field) {
        return field.closest('.form-group, .mb-3, .form-floating, .field-container') || field.parentElement;
    }
    
    createFeedbackElements(field, container) {
        if (!container.querySelector('.validation-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'validation-feedback';
            feedback.setAttribute('role', 'alert');
            container.appendChild(feedback);
        }
        
        if (!container.querySelector('.valid-feedback')) {
            const validFeedback = document.createElement('div');
            validFeedback.className = 'valid-feedback';
            container.appendChild(validFeedback);
        }
    }
    
    clearFieldValidation(field, container) {
        field.classList.remove('is-valid', 'is-invalid');
        const feedback = container.querySelector('.validation-feedback');
        const validFeedback = container.querySelector('.valid-feedback');
        
        if (feedback) feedback.textContent = '';
        if (validFeedback) validFeedback.textContent = '';
    }
    
    updateFieldState(field, container, isValid, message, showErrors) {
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            
            const validFeedback = container.querySelector('.valid-feedback');
            if (validFeedback && message) {
                validFeedback.textContent = message;
            }
        } else if (showErrors) {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            
            const feedback = container.querySelector('.validation-feedback');
            if (feedback) {
                feedback.textContent = message;
            }
        }
    }
    
    getFieldLabel(field) {
        const label = field.labels?.[0] || 
                    document.querySelector(`label[for="${field.id}"]`) ||
                    field.closest('.form-group, .mb-3')?.querySelector('label');
        
        return label?.textContent?.trim() || field.name || 'Field';
    }
    
    getFieldError(field) {
        const container = this.getFieldContainer(field);
        const feedback = container.querySelector('.validation-feedback');
        return feedback?.textContent || 'Invalid value';
    }
    
    updateSubmitButton(form, isValid) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (!submitButton) return;
        
        submitButton.disabled = !isValid;
        
        if (isValid) {
            submitButton.classList.remove('btn-disabled');
            submitButton.classList.add('btn-cw-primary');
        } else {
            submitButton.classList.add('btn-disabled');
            submitButton.classList.remove('btn-cw-primary');
        }
    }
    
    setupSubmitButtonManagement() {
        // This is handled in validateForm method
    }
    
    setupProgressiveEnhancement() {
        // Add enhanced class to indicate JavaScript is available
        document.documentElement.classList.add('js-enhanced');
        
        // Setup CSRF token handling
        this.setupCSRFHandling();
    }
    
    setupCSRFHandling() {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                         document.querySelector('meta[name=csrf-token]')?.content;
        
        if (csrfToken) {
            this.csrfToken = csrfToken;
        }
    }
    
    setFormLoading(form, loading) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (!submitButton) return;
        
        if (loading) {
            submitButton.disabled = true;
            const spinner = document.createElement('span');
            spinner.className = 'spinner-border spinner-border-sm ms-2';
            spinner.setAttribute('role', 'status');
            spinner.setAttribute('aria-hidden', 'true');
            submitButton.appendChild(spinner);
        } else {
            submitButton.disabled = false;
            const spinner = submitButton.querySelector('.spinner-border');
            if (spinner) {
                spinner.remove();
            }
        }
    }
    
    showFormErrors(form) {
        const summary = form.querySelector('.form-validation-summary');
        if (!summary) return;
        
        const errors = [];
        const invalidFields = form.querySelectorAll('.is-invalid');
        
        invalidFields.forEach(field => {
            const label = this.getFieldLabel(field);
            const error = this.getFieldError(field);
            errors.push(`${label}: ${error}`);
        });
        
        if (errors.length > 0) {
            summary.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h6>Please correct the following errors:</h6>
                    <ul class="mb-0">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
            summary.classList.remove('d-none');
        }
    }
    
    updateValidationSummary(form, errors) {
        const summary = form.querySelector('.form-validation-summary');
        if (!summary) return;
        
        if (errors.length === 0) {
            summary.classList.add('d-none');
        } else {
            this.showFormErrors(form);
        }
    }
    
    focusFirstError(form) {
        const firstError = form.querySelector('.is-invalid');
        if (firstError) {
            firstError.focus();
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the framework when the script loads
const formValidation = new FormValidationFramework();

// Export for use in other scripts
window.FormValidationFramework = FormValidationFramework;
window.formValidation = formValidation;
