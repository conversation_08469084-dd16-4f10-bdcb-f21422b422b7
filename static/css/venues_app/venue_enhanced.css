/**
 * Enhanced Venue Management Styles
 * Styles for FAQ templates, rich text editor, SEO analysis, and improved forms
 */

:root {
    --cw-brand-primary: #2F160F;
    --cw-brand-secondary: #8B4513;
    --cw-neutral-light: #F5F5F5;
    --cw-neutral-medium: #E5E5E5;
    --cw-accent-warm: #D2691E;
    --cw-success: #10B981;
    --cw-warning: #F59E0B;
    --cw-error: #EF4444;
    --cw-info: #3B82F6;
    --cw-shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --cw-shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --cw-shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --cw-font-heading: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --cw-border-radius: 0.5rem;
    --cw-transition: all 0.2s ease;
}

/* =============================================================================
   Rich Text Editor Styles
   ============================================================================= */

.description-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--cw-neutral-light);
    border: 2px solid var(--cw-neutral-medium);
    border-bottom: none;
    border-radius: var(--cw-border-radius) var(--cw-border-radius) 0 0;
    font-family: var(--cw-font-primary);
}

.toolbar-section {
    display: flex;
    gap: 0.25rem;
    padding-right: 0.75rem;
    border-right: 1px solid var(--cw-neutral-medium);
}

.toolbar-section:last-child {
    border-right: none;
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    background: white;
    border: 1px solid var(--cw-neutral-medium);
    border-radius: 0.25rem;
    color: var(--cw-brand-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--cw-transition);
    min-width: 2rem;
    justify-content: center;
}

.toolbar-btn:hover {
    background: var(--cw-brand-primary);
    color: white;
    border-color: var(--cw-brand-primary);
    transform: translateY(-1px);
}

.toolbar-btn:active {
    transform: translateY(0);
}

.rich-text-wrapper {
    position: relative;
}

.rich-text-editor {
    border-radius: 0 0 var(--cw-border-radius) var(--cw-border-radius) !important;
    border-top: none !important;
    resize: vertical;
    font-family: var(--cw-font-primary);
    line-height: 1.6;
}

.rich-text-editor:focus {
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1) !important;
}

/* =============================================================================
   Character Counter Enhanced
   ============================================================================= */

.character-counter-enhanced {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: var(--cw-neutral-light);
    border: 1px solid var(--cw-neutral-medium);
    border-radius: var(--cw-border-radius);
    font-family: var(--cw-font-primary);
}

.counter-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.char-count {
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.char-limit {
    color: #6b7280;
}

.counter-status {
    color: #6b7280;
    font-style: italic;
}

.counter-bar {
    width: 100%;
    height: 4px;
    background: var(--cw-neutral-medium);
    border-radius: 2px;
    overflow: hidden;
}

.counter-progress {
    height: 100%;
    background: var(--cw-info);
    transition: var(--cw-transition);
    border-radius: 2px;
}

.counter-progress.warning {
    background: var(--cw-warning);
}

.counter-progress.error {
    background: var(--cw-error);
}

.counter-progress.success {
    background: var(--cw-success);
}

/* =============================================================================
   SEO Analysis Panel
   ============================================================================= */

.seo-analysis-panel {
    margin-top: 1rem;
    background: white;
    border: 2px solid var(--cw-info);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow-md);
    font-family: var(--cw-font-primary);
    max-width: 500px;
}

.seo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--cw-info);
    color: white;
    border-radius: var(--cw-border-radius) var(--cw-border-radius) 0 0;
}

.seo-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-panel {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: var(--cw-transition);
}

.close-panel:hover {
    background: rgba(255, 255, 255, 0.2);
}

.seo-content {
    padding: 1rem;
}

.seo-score {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--cw-neutral-medium);
}

.score-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: 3px solid var(--cw-neutral-medium);
    border-radius: 50%;
    background: var(--cw-neutral-light);
}

.score-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
}

.score-number.excellent {
    color: var(--cw-success);
}

.score-number.good {
    color: var(--cw-info);
}

.score-number.fair {
    color: var(--cw-warning);
}

.score-number.needs_improvement {
    color: var(--cw-error);
}

.score-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-status {
    flex: 1;
}

.status-text {
    font-weight: 600;
    font-size: 1.1rem;
}

.status-text.excellent {
    color: var(--cw-success);
}

.status-text.good {
    color: var(--cw-info);
}

.status-text.fair {
    color: var(--cw-warning);
}

.status-text.needs_improvement {
    color: var(--cw-error);
}

.seo-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--cw-neutral-medium);
}

.metric {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.metric-label {
    color: #6b7280;
}

.metric-value {
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.seo-suggestions h5,
.seo-keywords h5 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.suggestions-list {
    margin: 0;
    padding-left: 1.25rem;
    list-style-type: disc;
}

.suggestions-list li {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
    color: #4b5563;
}

.keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.keyword-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: var(--cw-neutral-light);
    border: 1px solid var(--cw-neutral-medium);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--cw-brand-primary);
}

.keyword-tag.found {
    background: var(--cw-success);
    color: white;
    border-color: var(--cw-success);
}

/* =============================================================================
   Templates Panel
   ============================================================================= */

.templates-panel {
    margin-top: 1rem;
    background: white;
    border: 2px solid var(--cw-accent-warm);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow-md);
    font-family: var(--cw-font-primary);
    max-width: 700px;
}

.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--cw-accent-warm);
    color: white;
    border-radius: var(--cw-border-radius) var(--cw-border-radius) 0 0;
}

.templates-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.templates-content {
    padding: 1rem;
}

.templates-help {
    margin: 0 0 1rem 0;
    color: #6b7280;
    font-style: italic;
    font-size: 0.875rem;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.template-card {
    background: var(--cw-neutral-light);
    border: 2px solid var(--cw-neutral-medium);
    border-radius: var(--cw-border-radius);
    padding: 1rem;
    transition: var(--cw-transition);
    cursor: pointer;
}

.template-card:hover {
    border-color: var(--cw-accent-warm);
    transform: translateY(-2px);
    box-shadow: var(--cw-shadow-md);
}

.template-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.template-preview {
    margin: 0 0 0.75rem 0;
    font-size: 0.875rem;
    line-height: 1.4;
    color: #4b5563;
}

.template-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.btn-use-template {
    width: 100%;
    padding: 0.5rem;
    background: var(--cw-accent-warm);
    color: white;
    border: none;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--cw-transition);
}

.btn-use-template:hover {
    background: var(--cw-brand-primary);
    transform: translateY(-1px);
}

/* =============================================================================
   Notifications
   ============================================================================= */

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border: 2px solid var(--cw-info);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow-lg);
    font-family: var(--cw-font-primary);
    font-size: 0.875rem;
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-color: var(--cw-success);
    background: #f0f9ff;
}

.notification.warning {
    border-color: var(--cw-warning);
    background: #fffbeb;
}

.notification.error {
    border-color: var(--cw-error);
    background: #fef2f2;
}

.notification-message {
    flex: 1;
    color: var(--cw-brand-primary);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    transition: var(--cw-transition);
}

.notification-close:hover {
    color: var(--cw-brand-primary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* =============================================================================
   FAQ Enhanced Styles
   ============================================================================= */

/* FAQ Wizard Section */
.faqs-section {
    margin-bottom: 2rem;
}

.faq-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.faq-item:hover {
    background: #ffffff;
    border-color: var(--cw-brand-primary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.faq-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.faq-header h6 {
    margin: 0;
    color: var(--cw-brand-primary);
    font-weight: 600;
}

.remove-faq-btn {
    border: none;
    background: transparent;
    color: #dc3545;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.remove-faq-btn:hover {
    background: #dc3545;
    color: white;
}

/* FAQ List Section (existing styles) */
.faqs-list-section .faq-item[data-faq-id] {
    cursor: grab;
    position: relative;
}

.faqs-list-section .faq-item[data-faq-id]:active {
    cursor: grabbing;
}

.faqs-list-section .faq-item[data-faq-id].dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.faq-drag-handle {
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #d1d5db;
    cursor: grab;
    font-size: 1.25rem;
    opacity: 0;
    transition: var(--cw-transition);
}

.faq-item:hover .faq-drag-handle {
    opacity: 1;
}

.faq-drag-handle:active {
    cursor: grabbing;
}

/* =============================================================================
   Form Enhancements
   ============================================================================= */

.form-group-enhanced {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-label-enhanced {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    font-family: var(--cw-font-heading);
}

.form-control-enhanced {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--cw-neutral-medium);
    border-radius: var(--cw-border-radius);
    font-size: 1rem;
    font-family: var(--cw-font-primary);
    transition: var(--cw-transition);
    background: white;
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
}

.form-control-enhanced.error {
    border-color: var(--cw-error);
}

.form-control-enhanced.success {
    border-color: var(--cw-success);
}

.form-help-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
}

.form-error-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--cw-error);
    font-weight: 500;
}

/* =============================================================================
   Loading States
   ============================================================================= */

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--cw-neutral-medium);
    border-radius: 50%;
    border-top-color: var(--cw-brand-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* =============================================================================
   Responsive Design
   ============================================================================= */

@media (max-width: 768px) {
    .description-toolbar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .toolbar-section {
        border-right: none;
        border-bottom: 1px solid var(--cw-neutral-medium);
        padding-bottom: 0.5rem;
        padding-right: 0;
    }

    .toolbar-section:last-child {
        border-bottom: none;
    }

    .seo-analysis-panel,
    .templates-panel {
        max-width: 100%;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .seo-metrics {
        grid-template-columns: 1fr;
    }

    .seo-score {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .notification {
        left: 10px;
        right: 10px;
        max-width: none;
    }

    #image-preview-container {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .image-wrapper {
        height: 120px;
    }

    .image-controls .btn {
        font-size: 0.75rem;
        padding: 2px 6px;
    }

    /* Mobile responsive styles for new image cards */
    .image-cards-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .image-upload-card {
        min-height: 220px;
    }

    .upload-area {
        height: 180px;
    }

    .upload-placeholder {
        padding: 15px;
    }

    .upload-placeholder i {
        font-size: 2rem;
    }

    .upload-placeholder h6 {
        font-size: 1rem;
    }

    .upload-placeholder p {
        font-size: 0.85rem;
    }

    .upload-hint {
        font-size: 0.75rem;
    }

    .image-preview-wrapper {
        height: 180px;
    }

    .image-overlay {
        padding: 10px;
        flex-direction: column;
        gap: 6px;
    }

    .image-overlay .btn {
        font-size: 0.75rem;
        padding: 6px 12px;
    }

    .main-badge {
        top: 8px;
        right: 8px;
        padding: 3px 8px;
        font-size: 0.7rem;
    }

    .gallery-tips {
        padding: 12px 15px;
    }

    .tips-list li {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }

    .add-more-container {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .toolbar-btn {
        min-width: 1.5rem;
        padding: 0.375rem;
        font-size: 0.8rem;
    }

    .toolbar-btn i {
        font-size: 0.875rem;
    }

    .seo-content,
    .templates-content {
        padding: 0.75rem;
    }

    .template-card {
        padding: 0.75rem;
    }

    .image-cards-container {
        gap: 10px;
    }

    .image-upload-card {
        min-height: 200px;
    }

    .upload-area {
        height: 160px;
    }

    .upload-placeholder {
        padding: 10px;
    }

    .upload-placeholder i {
        font-size: 1.8rem;
    }

    .upload-placeholder h6 {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }

    .upload-placeholder p {
        font-size: 0.8rem;
        margin-bottom: 6px;
    }

    .upload-hint {
        font-size: 0.7rem;
    }

    .image-preview-wrapper {
        height: 160px;
    }

    .image-overlay {
        padding: 8px;
        gap: 4px;
    }

    .image-overlay .btn {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .main-badge {
        top: 6px;
        right: 6px;
        padding: 2px 6px;
        font-size: 0.65rem;
    }

    .gallery-tips h6 {
        font-size: 1rem;
    }

    .tips-list li {
        font-size: 0.8rem;
    }

    .add-more-container {
        padding: 12px;
    }

    .add-image-btn {
        font-size: 0.9rem;
        padding: 8px 16px;
    }
}

/* =============================================================================
   Accessibility Enhancements
   ============================================================================= */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for keyboard navigation */
.toolbar-btn:focus,
.btn-use-template:focus,
.close-panel:focus {
    outline: 2px solid var(--cw-brand-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .toolbar-btn,
    .template-card,
    .seo-analysis-panel,
    .templates-panel {
        border-width: 3px;
    }

    .keyword-tag {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .rich-text-editor,
    .form-control-enhanced {
        background: #1f2937;
        color: white;
        border-color: #374151;
    }

    .seo-analysis-panel,
    .templates-panel {
        background: #1f2937;
        color: white;
    }

    .template-card {
        background: #374151;
        border-color: #4b5563;
    }

    .notification {
        background: #1f2937;
        color: white;
    }
}

/* ===== IMAGE UPLOAD INTERFACE ===== */

/* Reset any conflicting styles for venue gallery */
.gallery-section * {
    box-sizing: border-box;
}

/* Prevent profile image styles from affecting venue gallery */
.gallery-section img:not(.preview-image) {
    border-radius: 0 !important;
    width: auto !important;
    height: auto !important;
    object-fit: initial !important;
}

/* Ensure venue gallery has priority over other image styles */
.venue-create-wizard .gallery-section {
    position: relative;
    z-index: 1;
}

/* Image Cards Container */
.image-cards-container {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
    width: 100% !important;
}

/* Ensure gallery image cards container has correct layout */
.gallery-section .image-cards-container {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
    width: 100% !important;
}

/* Individual Image Upload Card */
.image-upload-card {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-height: 250px;
    display: flex;
    flex-direction: column;
}

.image-upload-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-upload-card.has-image {
    border: 3px solid #28a745 !important;
    background: transparent !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2) !important;
    min-height: 300px !important;
}

.image-upload-card.main-image {
    border-color: #ffc107;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

/* Upload Area */
.upload-area {
    height: 100%;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    flex: 1;
}

/* Ensure upload areas in gallery section maintain proper styling */
.gallery-section .upload-area,
.image-cards-container .upload-area {
    height: 100%;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    flex: 1;
}

.upload-area:hover {
    background: #e3f2fd;
}

.upload-area.drag-over {
    background: #bbdefb;
    border-color: #007bff;
}

.upload-placeholder {
    text-align: center;
    color: #6c757d;
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.upload-placeholder i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #007bff;
}

.upload-placeholder h6 {
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.upload-placeholder p {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.upload-hint {
    font-size: 0.8rem;
    color: #007bff;
    font-style: italic;
}

/* Image Preview - Full Card Display */
.image-preview-wrapper {
    position: relative !important;
    height: 100% !important;
    min-height: 250px !important;
    overflow: hidden !important;
    border-radius: 8px !important;
    background: #f8f9fa !important;
    width: 100% !important;
    display: block !important;
    transition: all 0.3s ease !important;
}

/* Ensure image preview wrapper in gallery section has correct styling */
.gallery-section .image-preview-wrapper,
.image-cards-container .image-preview-wrapper {
    position: relative !important;
    height: 100% !important;
    min-height: 250px !important;
    overflow: hidden !important;
    border-radius: 8px !important;
    background: #f8f9fa !important;
    width: 100% !important;
    display: block !important;
}

.preview-image {
    width: 100% !important;
    height: 100% !important;
    min-height: 250px !important;
    object-fit: cover !important;
    display: block !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: none !important;
    max-width: none !important;
    max-height: none !important;
    opacity: 0;
    animation: fadeInImage 0.5s ease forwards;
}

@keyframes fadeInImage {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.image-upload-card:hover .preview-image {
    transform: scale(1.05) !important;
}

/* Override any profile image styles that might interfere */
.image-upload-card .preview-image {
    border-radius: 8px !important;
    width: 100% !important;
    height: 100% !important;
    min-height: 250px !important;
    object-fit: cover !important;
}

/* Ensure venue gallery images don't inherit profile picture styles */
.gallery-section .preview-image,
.image-cards-container .preview-image {
    border-radius: 8px !important;
    width: 100% !important;
    height: 100% !important;
    min-height: 250px !important;
    object-fit: cover !important;
    border: none !important;
    box-shadow: none !important;
}

/* Full Card Image Upload Cards */
.image-upload-card.has-image {
    border: 3px solid #28a745 !important;
    background: transparent !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2) !important;
}

.image-upload-card.has-image .image-preview-wrapper {
    height: 100% !important;
    min-height: 300px !important;
    border-radius: 8px !important;
}

.image-upload-card.has-image .preview-image {
    min-height: 300px !important;
    height: 100% !important;
}

.image-overlay {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)) !important;
    padding: 15px !important;
    display: flex !important;
    gap: 8px !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    z-index: 10 !important;
}

/* Ensure gallery image overlays work correctly */
.gallery-section .image-overlay,
.image-cards-container .image-overlay {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7)) !important;
    padding: 15px !important;
    display: flex !important;
    gap: 8px !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.image-upload-card:hover .image-overlay {
    opacity: 1 !important;
}

/* Ensure gallery image cards show overlay on hover */
.gallery-section .image-upload-card:hover .image-overlay,
.image-cards-container .image-upload-card:hover .image-overlay {
    opacity: 1 !important;
}

.image-overlay .btn {
    font-size: 0.8rem;
    padding: 4px 8px;
    flex: 1;
}

.main-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ffc107;
    color: #000;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Upload Progress */
.upload-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    z-index: 15;
    border-radius: 0 0 8px 8px;
    backdrop-filter: blur(3px);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.upload-progress .progress {
    height: 6px;
    margin-bottom: 8px;
    border-radius: 3px;
    background: #e9ecef;
    overflow: hidden;
}

.upload-progress .progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 3px;
    animation: progress-pulse 1.5s ease-in-out infinite;
}

@keyframes progress-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.upload-status {
    color: #007bff;
    font-weight: 500;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.upload-status::before {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Add More Container */
.add-more-container {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.add-image-btn {
    margin-bottom: 8px;
}

/* Gallery Tips */
.gallery-tips {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px 20px;
    border-radius: 0 8px 8px 0;
    margin-top: 20px;
}

.gallery-tips h6 {
    color: #007bff;
    margin-bottom: 10px;
    font-weight: 600;
}

.tips-list {
    margin: 0;
    padding-left: 20px;
    color: #6c757d;
}

.tips-list li {
    margin-bottom: 4px;
    font-size: 0.9rem;
}

/* Legacy upload area for backward compatibility */
.image-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.image-upload-area:hover {
    border-color: #007bff;
    background: #f0f7ff;
}

.image-upload-area.drag-over {
    border-color: #007bff;
    background: #e3f2fd;
    transform: scale(1.02);
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.upload-placeholder i {
    font-size: 2.5rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.upload-placeholder p {
    margin: 0;
    color: #6c757d;
    font-size: 1.1rem;
}

/* Image Preview Container */
#image-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-preview {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.image-preview:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.image-preview.main-image {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.image-preview.uploading {
    opacity: 0.7;
}

.image-preview.uploaded {
    opacity: 1;
}

.image-preview.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.image-wrapper {
    position: relative;
    width: 100%;
    height: 150px;
    overflow: hidden;
}

.image-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.upload-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.upload-spinner i {
    color: #007bff;
    font-size: 1.2rem;
}

.main-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    z-index: 1;
}

.image-controls {
    padding: 10px;
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

.image-controls .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 4px 8px;
}

.image-info {
    padding: 0 10px 10px;
    font-size: 0.875rem;
}

.image-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.image-order {
    color: #6c757d;
    font-size: 0.8rem;
}

.upload-status {
    color: #007bff;
    font-size: 0.8rem;
    font-style: italic;
}

/* Loading placeholder for images */
.image-preview.uploading .image-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    z-index: 1;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Toast notifications for upload feedback */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.upload-toast {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.upload-toast.success {
    border-left: 4px solid #28a745;
}

.upload-toast.error {
    border-left: 4px solid #dc3545;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* =============================================================================
   Enhanced Gallery Image Styles - Fix for profile image styling interference
   ============================================================================= */

/* Ensure venue gallery images are NEVER styled like profile pictures */
.venue-create-wizard .gallery-section .preview-image,
.venue-create-wizard .image-cards-container .preview-image,
.gallery-section .image-cards-container .preview-image,
.image-cards-container .image-upload-card .preview-image,
.gallery-section .image-upload-card .preview-image {
    border-radius: 8px !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    max-width: none !important;
    max-height: none !important;
}

/* Override any Bootstrap or profile image classes that might interfere */
.gallery-section .preview-image.rounded-circle,
.image-cards-container .preview-image.rounded-circle,
.venue-create-wizard .preview-image.rounded-circle {
    border-radius: 8px !important;
}

/* Ensure gallery image containers maintain proper rectangular shape */
.venue-create-wizard .gallery-section .image-preview-wrapper,
.gallery-section .image-cards-container .image-preview-wrapper,
.image-cards-container .image-upload-card .image-preview-wrapper {
    border-radius: 8px !important;
    overflow: hidden !important;
    background: #f8f9fa !important;
    width: 100% !important;
    display: block !important;
    position: relative !important;
    aspect-ratio: 4/3 !important;
}

/* Ensure upload cards maintain rectangular styling */
.venue-create-wizard .gallery-section .image-upload-card,
.gallery-section .image-cards-container .image-upload-card,
.image-cards-container .image-upload-card {
    border-radius: 12px !important;
    border: 2px solid #e5e7eb !important;
    background: white !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Hover effects for upload cards */
.venue-create-wizard .gallery-section .image-upload-card:hover,
.gallery-section .image-cards-container .image-upload-card:hover,
.image-cards-container .image-upload-card:hover {
    border-color: #007bff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Prevent any global image styles from interfering */
.gallery-section img,
.image-cards-container img,
.venue-create-wizard .gallery-section img {
    border-radius: 8px !important;
}

/* Specifically override any customer profile styling that might leak */
.gallery-section .customer-profile,
.gallery-section .profile-picture,
.gallery-section .rounded-circle,
.image-cards-container .customer-profile,
.image-cards-container .profile-picture,
.image-cards-container .rounded-circle {
    border-radius: 8px !important;
}

/* =============================================================================
   Original Gallery Section Styles
   ============================================================================= */

.gallery-section * {
    box-sizing: border-box !important;
} 