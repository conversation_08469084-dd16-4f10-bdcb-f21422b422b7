/**
 * CozyWish Form Validation Styles
 *
 * Modern validation styles with success/error states, loading indicators,
 * progress bars, tooltips, and animations that match the CozyWish design system.
 */

/* === CSS CUSTOM PROPERTIES === */
:root {
    /* CozyWish Brand Colors */
    --cw-brand-primary: #2F160F;
    --cw-brand-light: #4a2a1f;
    --cw-brand-accent: #fae1d7;
    --cw-accent-light: #fef7f0;
    --cw-accent-dark: #f1d4c4;

    /* Semantic Colors */
    --cw-success: #059669;
    --cw-warning: #d97706;
    --cw-error: #dc2626;
    --cw-info: #0284c7;

    /* Neutral Colors */
    --cw-neutral-50: #fafafa;
    --cw-neutral-100: #f5f5f5;
    --cw-neutral-200: #e5e5e5;
    --cw-neutral-600: #525252;
    --cw-neutral-700: #404040;

    /* Typography */
    --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;

    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    /* Gradients */
    --cw-gradient-accent: linear-gradient(135deg, #fae1d7 0%, #f1d4c4 100%);
    --cw-gradient-brand: linear-gradient(135deg, #2F160F 0%, #4a2a1f 100%);
}

/* === FORM VALIDATION STATES === */

.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--cw-success);
    box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.25);
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--cw-error);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    font-family: var(--cw-font-primary);
    color: var(--cw-success);
}

.invalid-feedback,
.validation-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    font-family: var(--cw-font-primary);
    color: var(--cw-error);
}

/* === PASSWORD STRENGTH INDICATOR === */

.password-strength-indicator {
    margin-top: 0.5rem;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 2px;
}

.strength-fill.strength-weak {
    background-color: var(--cw-error);
}

.strength-fill.strength-medium {
    background-color: var(--cw-warning);
}

.strength-fill.strength-strong {
    background-color: var(--cw-success);
}

.strength-text {
    font-size: 0.875rem;
    font-weight: 500;
    font-family: var(--cw-font-primary);
    color: var(--cw-neutral-600);
    margin-bottom: 0.5rem;
}

.strength-requirements {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.25rem;
}

.requirement {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    font-family: var(--cw-font-primary);
    color: var(--cw-neutral-600);
}

.requirement i {
    font-size: 0.625rem;
    transition: color 0.3s ease;
}

/* === FORM LOADING STATES === */

.form-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-disabled:hover {
    opacity: 0.6;
}

/* === VALIDATION SUMMARY === */

.form-validation-summary {
    margin-bottom: 1rem;
}

.form-validation-summary .alert {
    border-left: 4px solid var(--cw-error);
    background: var(--cw-accent-light);
    border-color: var(--cw-error);
    color: var(--cw-brand-primary);
}

.form-validation-summary ul {
    padding-left: 1.5rem;
}

.form-validation-summary li {
    margin-bottom: 0.25rem;
}

/* === FIELD CONTAINERS === */

.field-container {
    position: relative;
    margin-bottom: 1rem;
}

.field-container.has-validation {
    padding-bottom: 1.5rem;
}

/* === REAL-TIME VALIDATION INDICATORS === */

.validation-indicator {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    z-index: 5;
}

.validation-indicator.valid {
    color: var(--cw-success);
}

.validation-indicator.invalid {
    color: var(--cw-error);
}

.validation-indicator.checking {
    color: var(--cw-neutral-600);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* === PROGRESSIVE ENHANCEMENT === */

.js-enhanced .no-js {
    display: none !important;
}

.no-js .js-only {
    display: none !important;
}

/* === FORM STEP INDICATORS === */

.form-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 0;
    list-style: none;
}

.form-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.form-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 1rem;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.form-step.completed::after {
    background-color: #28a745;
}

.form-step-indicator {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--cw-neutral-200);
    color: var(--cw-neutral-600);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: 600;
    font-family: var(--cw-font-heading);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.form-step.active .form-step-indicator {
    background-color: var(--cw-brand-primary);
    color: white;
}

.form-step.completed .form-step-indicator {
    background-color: var(--cw-success);
    color: white;
}

.form-step-label {
    font-size: 0.875rem;
    color: var(--cw-neutral-600);
    font-weight: 500;
    font-family: var(--cw-font-primary);
}

.form-step.active .form-step-label {
    color: var(--cw-brand-primary);
    font-weight: 600;
}

.form-step.completed .form-step-label {
    color: var(--cw-success);
}

/* === CONTEXTUAL HELP TOOLTIPS === */

.field-help {
    position: relative;
    display: inline-block;
    margin-left: 0.5rem;
}

.field-help-trigger {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: var(--cw-neutral-600);
    color: white;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: help;
    border: none;
    transition: background-color 0.3s ease;
}

.field-help-trigger:hover {
    background-color: var(--cw-brand-primary);
}

.field-help-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--cw-brand-primary);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-family: var(--cw-font-primary);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
    margin-bottom: 0.25rem;
    box-shadow: var(--cw-shadow-md);
}

.field-help-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--cw-brand-primary);
}

.field-help:hover .field-help-content,
.field-help-trigger:focus + .field-help-content {
    opacity: 1;
    visibility: visible;
}

/* === AUTO-SAVE INDICATOR === */

.auto-save-indicator {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: var(--cw-success);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: var(--cw-font-primary);
    font-weight: 500;
    z-index: 1050;
    opacity: 0;
    transform: translateY(-1rem);
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: var(--cw-shadow-md);
}

.auto-save-indicator.show {
    opacity: 1;
    transform: translateY(0);
}

.auto-save-indicator.error {
    background: var(--cw-error);
}

.auto-save-indicator.info {
    background: var(--cw-info);
}

/* === PROFILE COMPLETION INDICATOR === */

.profile-completion {
    background: var(--cw-gradient-accent);
    border: 1px solid var(--cw-accent-dark);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--cw-shadow-sm);
}

.completion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.completion-title {
    font-size: 1rem;
    font-weight: 600;
    font-family: var(--cw-font-heading);
    color: var(--cw-brand-primary);
    margin: 0;
}

.completion-percentage {
    font-size: 1.25rem;
    font-weight: 700;
    font-family: var(--cw-font-heading);
    color: var(--cw-brand-primary);
}

.completion-bar {
    width: 100%;
    height: 8px;
    background-color: var(--cw-accent-dark);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.completion-fill {
    height: 100%;
    background: var(--cw-gradient-brand);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.completion-message {
    font-size: 0.875rem;
    font-family: var(--cw-font-primary);
    color: var(--cw-neutral-600);
    margin: 0;
}

/* === RESPONSIVE DESIGN === */

@media (max-width: 768px) {
    .strength-requirements {
        grid-template-columns: 1fr;
    }
    
    .form-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-step:not(:last-child)::after {
        display: none;
    }
    
    .field-help-content {
        position: fixed;
        bottom: auto;
        top: 50%;
        left: 1rem;
        right: 1rem;
        transform: translateY(-50%);
        white-space: normal;
        text-align: center;
    }
}

/* === ACCESSIBILITY ENHANCEMENTS === */

@media (prefers-reduced-motion: reduce) {
    .strength-fill,
    .validation-indicator,
    .form-step-indicator,
    .field-help-trigger,
    .field-help-content,
    .auto-save-indicator,
    .completion-fill {
        transition: none;
    }
    
    .validation-indicator.checking {
        animation: none;
    }
}

/* === HIGH CONTRAST MODE === */

@media (prefers-contrast: high) {
    .form-control.is-valid {
        border-color: #000;
        border-width: 2px;
    }
    
    .form-control.is-invalid {
        border-color: #000;
        border-width: 2px;
        background-color: #ffe6e6;
    }
    
    .strength-fill.strength-weak {
        background-color: #000;
    }
    
    .strength-fill.strength-medium {
        background-color: #666;
    }
    
    .strength-fill.strength-strong {
        background-color: #000;
    }
}

/* === FOCUS MANAGEMENT === */

.form-control:focus,
.form-select:focus {
    outline: 2px solid #42241A;
    outline-offset: 2px;
}

.form-control:focus.is-valid {
    outline-color: #28a745;
}

.form-control:focus.is-invalid {
    outline-color: #dc3545;
}
