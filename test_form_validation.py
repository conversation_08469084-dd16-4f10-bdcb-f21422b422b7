#!/usr/bin/env python3
"""
CozyWish Form Validation Integration Test

This script tests the form validation mixins and ensures they work correctly
with existing Django forms.
"""

import os
import sys
import django
from django.test import TestCase
from django.forms import Form, Char<PERSON>ield, EmailField, PasswordInput

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
django.setup()

# Import after Django setup
from accounts_app.forms.common import (
    CommonValidationMixin,
    RoleBasedFormMixin,
    ProfileCompletionMixin,
    SecurityValidationMixin
)


class TestFormValidationMixins(TestCase):
    """Test cases for form validation mixins."""
    
    def test_common_validation_mixin(self):
        """Test CommonValidationMixin functionality."""
        
        class TestForm(CommonValidationMixin, Form):
            email = EmailField()
            password = CharField(widget=PasswordInput())
        
        form = TestForm()
        
        # Test validation attributes are added
        email_attrs = form.fields['email'].widget.attrs
        password_attrs = form.fields['password'].widget.attrs
        
        self.assertEqual(email_attrs.get('data-validation'), 'email')
        self.assertEqual(password_attrs.get('data-validation'), 'password')
        self.assertEqual(email_attrs.get('data-validation-realtime'), 'true')
        self.assertEqual(password_attrs.get('data-validation-realtime'), 'true')
        
        print("✅ CommonValidationMixin: Validation attributes added correctly")
    
    def test_role_based_form_mixin(self):
        """Test RoleBasedFormMixin functionality."""
        
        class TestForm(RoleBasedFormMixin, Form):
            business_name = CharField(required=False)
            business_email = EmailField(required=False)
        
        # Test service provider role
        form = TestForm(user_role='service_provider')
        self.assertTrue(form.fields['business_name'].required)
        self.assertTrue(form.fields['business_email'].required)
        
        # Test customer role
        form = TestForm(user_role='customer')
        self.assertFalse(form.fields['business_name'].required)
        self.assertFalse(form.fields['business_email'].required)
        
        print("✅ RoleBasedFormMixin: Role-based requirements work correctly")
    
    def test_profile_completion_mixin(self):
        """Test ProfileCompletionMixin functionality."""
        
        class TestForm(ProfileCompletionMixin, Form):
            email = EmailField()
            first_name = CharField()
            phone_number = CharField()
        
        form = TestForm()
        
        # Test completion attributes are added
        email_attrs = form.fields['email'].widget.attrs
        self.assertEqual(email_attrs.get('data-completion-field'), 'true')
        self.assertEqual(email_attrs.get('data-completion-weight'), '20')
        
        first_name_attrs = form.fields['first_name'].widget.attrs
        self.assertEqual(first_name_attrs.get('data-completion-weight'), '10')
        
        print("✅ ProfileCompletionMixin: Completion tracking attributes added correctly")
    
    def test_security_validation_mixin(self):
        """Test SecurityValidationMixin functionality."""
        
        class TestForm(SecurityValidationMixin, Form):
            email = EmailField()
            password = CharField(widget=PasswordInput())
        
        form = TestForm()
        
        # Test security attributes are added
        email_attrs = form.fields['email'].widget.attrs
        password_attrs = form.fields['password'].widget.attrs
        
        self.assertEqual(email_attrs.get('data-security-sensitive'), 'true')
        self.assertEqual(password_attrs.get('data-security-sensitive'), 'true')
        self.assertEqual(email_attrs.get('data-rate-limit'), 'true')
        
        print("✅ SecurityValidationMixin: Security attributes added correctly")
    
    def test_mixin_combination(self):
        """Test that multiple mixins work together."""
        
        class TestForm(CommonValidationMixin, ProfileCompletionMixin, SecurityValidationMixin, Form):
            email = EmailField()
            password = CharField(widget=PasswordInput())
        
        form = TestForm()
        
        # Test that all mixin attributes are present
        email_attrs = form.fields['email'].widget.attrs
        
        # From CommonValidationMixin
        self.assertEqual(email_attrs.get('data-validation'), 'email')
        
        # From ProfileCompletionMixin
        self.assertEqual(email_attrs.get('data-completion-field'), 'true')
        
        # From SecurityValidationMixin
        self.assertEqual(email_attrs.get('data-security-sensitive'), 'true')
        
        print("✅ Mixin Combination: Multiple mixins work together correctly")


def test_existing_forms():
    """Test that existing forms still work with new mixins."""
    
    try:
        from accounts_app.forms import CustomerSignupForm, CustomerLoginForm
        
        # Test CustomerSignupForm
        signup_form = CustomerSignupForm()
        email_field = signup_form.fields.get('email')
        if email_field:
            attrs = email_field.widget.attrs
            print(f"✅ CustomerSignupForm: Email field has validation: {attrs.get('data-validation') == 'email'}")
        
        # Test CustomerLoginForm
        login_form = CustomerLoginForm()
        email_field = login_form.fields.get('email')
        if email_field:
            attrs = email_field.widget.attrs
            print(f"✅ CustomerLoginForm: Email field has validation: {attrs.get('data-validation') == 'email'}")
        
        print("✅ Existing Forms: Integration successful")
        
    except ImportError as e:
        print(f"⚠️  Could not import existing forms: {e}")


def test_template_tags():
    """Test that template tags are available."""
    
    try:
        from accounts_app.templatetags.form_validation_tags import (
            add_validation_attrs,
            add_enhanced_attrs,
            add_autocomplete_attrs
        )
        
        print("✅ Template Tags: All validation tags imported successfully")
        
    except ImportError as e:
        print(f"❌ Template Tags: Import failed: {e}")


def run_all_tests():
    """Run all integration tests."""
    
    print("🧪 Starting CozyWish Form Validation Integration Tests...\n")
    
    # Run Django test cases
    test_case = TestFormValidationMixins()
    test_case.test_common_validation_mixin()
    test_case.test_role_based_form_mixin()
    test_case.test_profile_completion_mixin()
    test_case.test_security_validation_mixin()
    test_case.test_mixin_combination()
    
    print()
    
    # Test existing forms integration
    test_existing_forms()
    
    print()
    
    # Test template tags
    test_template_tags()
    
    print("\n🎉 All tests completed!")


if __name__ == '__main__':
    run_all_tests()
