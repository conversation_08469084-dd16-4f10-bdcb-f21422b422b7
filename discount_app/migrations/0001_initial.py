# Generated by Django 5.2.4 on 2025-07-05 17:43

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('venues_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformDiscount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name/title of the discount', max_length=255)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly version of the discount name (auto-generated)', max_length=255, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description of the discount')),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed_amount', 'Fixed Amount')], default='percentage', help_text='Type of discount calculation', max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='Discount value (percentage or fixed amount)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Maximum number of times this discount can be used', null=True)),
                ('start_date', models.DateTimeField(help_text='When the discount becomes active')),
                ('end_date', models.DateTimeField(help_text='When the discount expires')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('min_booking_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Minimum booking value required to apply this discount', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('max_discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum discount amount for percentage discounts (optional)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this discount should be featured on the homepage')),
                ('category', models.ForeignKey(blank=True, help_text='Category this discount applies to (optional - leave blank for all categories)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='platform_discounts', to='venues_app.category')),
                ('created_by', models.ForeignKey(help_text='User who created this discount', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Platform Discount',
                'verbose_name_plural': 'Platform Discounts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ServiceDiscount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name/title of the discount', max_length=255)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly version of the discount name (auto-generated)', max_length=255, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description of the discount')),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed_amount', 'Fixed Amount')], default='percentage', help_text='Type of discount calculation', max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='Discount value (percentage or fixed amount)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Maximum number of times this discount can be used', null=True)),
                ('start_date', models.DateTimeField(help_text='When the discount becomes active')),
                ('end_date', models.DateTimeField(help_text='When the discount expires')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_approved', models.BooleanField(default=False, help_text='Whether this discount has been approved by admin')),
                ('approved_at', models.DateTimeField(blank=True, help_text='When this discount was approved', null=True)),
                ('approved_by', models.ForeignKey(blank=True, help_text='Admin who approved this discount', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_service_discounts', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(help_text='User who created this discount', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('service', models.ForeignKey(help_text='Service this discount applies to', on_delete=django.db.models.deletion.CASCADE, related_name='discounts', to='venues_app.service')),
            ],
            options={
                'verbose_name': 'Service Discount',
                'verbose_name_plural': 'Service Discounts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VenueDiscount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name/title of the discount', max_length=255)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly version of the discount name (auto-generated)', max_length=255, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description of the discount')),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed_amount', 'Fixed Amount')], default='percentage', help_text='Type of discount calculation', max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='Discount value (percentage or fixed amount)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Maximum number of times this discount can be used', null=True)),
                ('start_date', models.DateTimeField(help_text='When the discount becomes active')),
                ('end_date', models.DateTimeField(help_text='When the discount expires')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('min_booking_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Minimum booking value required to apply this discount', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('max_discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum discount amount for percentage discounts (optional)', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('is_approved', models.BooleanField(default=False, help_text='Whether this discount has been approved by admin')),
                ('approved_at', models.DateTimeField(blank=True, help_text='When this discount was approved', null=True)),
                ('approved_by', models.ForeignKey(blank=True, help_text='Admin who approved this discount', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_venue_discounts', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(help_text='User who created this discount', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue this discount applies to', on_delete=django.db.models.deletion.CASCADE, related_name='discounts', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Venue Discount',
                'verbose_name_plural': 'Venue Discounts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DiscountUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discount_type', models.CharField(choices=[('VenueDiscount', 'Venue Discount'), ('ServiceDiscount', 'Service Discount'), ('PlatformDiscount', 'Platform Discount')], help_text='Type of discount that was used', max_length=20)),
                ('discount_id', models.PositiveIntegerField(help_text='ID of the specific discount that was used')),
                ('booking_reference', models.CharField(help_text='Reference to the booking where this discount was applied', max_length=100)),
                ('original_price', models.DecimalField(decimal_places=2, help_text='Original price before discount', max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, help_text='Amount of discount applied', max_digits=10)),
                ('final_price', models.DecimalField(decimal_places=2, help_text='Final price after discount', max_digits=10)),
                ('used_at', models.DateTimeField(auto_now_add=True, help_text='When the discount was used')),
                ('user', models.ForeignKey(help_text='Customer who used this discount', on_delete=django.db.models.deletion.CASCADE, related_name='discount_usages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Discount Usage',
                'verbose_name_plural': 'Discount Usages',
                'ordering': ['-used_at'],
                'indexes': [models.Index(fields=['discount_type', 'discount_id'], name='discount_ap_discoun_925968_idx'), models.Index(fields=['user', '-used_at'], name='discount_ap_user_id_d2ad74_idx'), models.Index(fields=['-used_at'], name='discount_ap_used_at_5ba916_idx')],
                'constraints': [models.UniqueConstraint(fields=('user', 'discount_type', 'discount_id', 'booking_reference'), name='unique_user_discount_booking')],
            },
        ),
    ]
