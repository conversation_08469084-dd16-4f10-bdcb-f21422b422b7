"""Shared views and utilities used across discount components."""

# --- Standard Library Imports ---
from datetime import datetime, timedelta
import json

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Avg, Count, F, Q, Sum
from django.http import Http404, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods

# --- Local App Imports ---
from ..forms import (
    ServiceDiscountForm,
    VenueDiscountForm,
    PlatformDiscountForm,
    DiscountFilterForm,
    DiscountApprovalForm,
    QuickServiceDiscountForm,
    QuickVenueDiscountForm,
)
from ..models import PlatformDiscount, DiscountUsage, ServiceDiscount, VenueDiscount
from ..utils import get_applicable_discounts, get_best_discount
from venues_app.models import Service, Venue, Category
from notifications_app.utils import create_notification, send_notification_email
from notifications_app.models import Notification

# --- Logging Imports ---
from utils.logging_utils import (
    get_app_logger, log_user_activity, log_error, log_security_event,
    log_audit_event, log_performance, get_client_info
)
from ..logging_utils import (
    log_discount_creation, log_discount_update, log_discount_deletion,
    log_discount_approval, log_discount_search, log_unauthorized_discount_access,
    log_discount_error
)


def test_view(request):
    """Test view to verify the discount_app is working correctly."""

    if not settings.DEBUG:
        raise Http404()

    venue_discounts = VenueDiscount.objects.count()
    service_discounts = ServiceDiscount.objects.count()
    platform_discounts = PlatformDiscount.objects.count()
    discount_usages = DiscountUsage.objects.count()

    html = f"""
    <h1>Discount App Test Page</h1>
    <p>The discount_app is working correctly!</p>

    <h2>Current Statistics:</h2>
    <ul>
        <li>Venue Discounts: {venue_discounts}</li>
        <li>Service Discounts: {service_discounts}</li>
        <li>Platform Discounts: {platform_discounts}</li>
        <li>Discount Usages: {discount_usages}</li>
    </ul>

    <h2>Models Available:</h2>
    <ul>
        <li>VenueDiscount - Discounts for entire venues</li>
        <li>ServiceDiscount - Discounts for specific services</li>
        <li>PlatformDiscount - Platform-wide discounts</li>
        <li>DiscountUsage - Usage tracking and analytics</li>
    </ul>

    <p><a href="/admin/">Go to Admin Panel</a> to manage discounts</p>
    """

    return HttpResponse(html)


# ===== PROVIDER VIEWS =====

def service_provider_required(view_func):
    """Decorator to ensure only service providers can access the view."""
    def wrapper(request, *args, **kwargs):
        from django.http import HttpResponseForbidden
        from django.core.exceptions import PermissionDenied

        if not request.user.is_authenticated:
            messages.error(request, 'Please log in to access this page.')
            return redirect('accounts_app:service_provider_login')

        if not hasattr(request.user, 'service_provider_profile'):
            log_unauthorized_discount_access(
                user_email=request.user.email,
                attempted_action='access_provider_view',
                discount_id=0,  # No specific discount
                request=request,
                additional_details={'reason': 'no_service_provider_profile'}
            )
            messages.error(request, 'Only service providers can access this page.')
            raise PermissionDenied("Only service providers can access this page.")

        # Check if provider has a venue
        try:
            venue = request.user.service_provider_profile.venue
            if not venue:
                log_unauthorized_discount_access(
                    user_email=request.user.email,
                    attempted_action='access_provider_view',
                    discount_id=0,  # No specific discount
                    request=request,
                    additional_details={'reason': 'no_venue'}
                )
                context = {
                    'feature_name': "Discounts",
                    'feature_description': 'Create and manage discounts to attract more customers and increase bookings.',
                    'feature_benefits': [
                        'Offer special promotions and deals to customers',
                        'Create service-specific or venue-wide discounts',
                        'Track discount usage and effectiveness',
                        'Increase customer engagement and bookings'
                    ]
                }
                return render(request, 'dashboard_app/provider/feature_locked.html', context)
        except Exception as e:
            log_unauthorized_discount_access(
                user_email=request.user.email,
                attempted_action='access_provider_view',
                discount_id=0,  # No specific discount
                request=request,
                additional_details={'reason': 'venue_access_error', 'error': str(e)}
            )
            context = {
                'feature_name': "Discounts",
                'feature_description': 'Create and manage discounts to attract more customers and increase bookings.',
                'feature_benefits': [
                    'Offer special promotions and deals to customers',
                    'Create service-specific or venue-wide discounts',
                    'Track discount usage and effectiveness',
                    'Increase customer engagement and bookings'
                ]
            }
            return render(request, 'dashboard_app/provider/feature_locked.html', context)

        return view_func(request, *args, **kwargs)
    return wrapper


